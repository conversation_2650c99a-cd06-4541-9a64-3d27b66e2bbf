package com.payermax.channel.inst.center.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractProductAssembler;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractBaseFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractFeeFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractSettleFormMsgDTO;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.app.manage.contract.InstContractBusinessValidator;
import com.payermax.channel.inst.center.app.model.contract.dataParser.FeeItemConvertUtils;
import com.payermax.channel.inst.center.app.model.contract.desensitizer.AuthConstant;
import com.payermax.channel.inst.center.app.request.InstContractFeeItemRequestDTO;
import com.payermax.channel.inst.center.app.request.InstContractProductQueryRequest;
import com.payermax.channel.inst.center.app.request.InstSettleBatchModifiedReqDTO;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemFullyVO;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemVO;
import com.payermax.channel.inst.center.app.response.InstContractSettlementVO;
import com.payermax.channel.inst.center.app.response.InstFeeQueryVO;
import com.payermax.channel.inst.center.app.service.InstContractFeeWorkflowService;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.DiffUtil;
import com.payermax.channel.inst.center.common.utils.ListUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.infrastructure.adapter.VoucherAdapter;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractFeeItemRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractOperateLogRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractSettlementItemRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstProcessDockRepository;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ObjectUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/1/3
 * @DESC 机构合约费用管理服务
 */
@Service
@Slf4j
@AllArgsConstructor
public class InstContractFeeService {

    private final InstContractFeeItemRepository instContractFeeItemRepository;
    private final InstContractSettlementItemRepository instContractSettlementItemRepository;
    private final InstContractOperateLogRepository contractOperateLogRepository;
    private final InstProcessDockRepository processDockRepository;
    private final OperateLogFactory operateLogFactory;
    private final VoucherAdapter voucherAdapter;
    private final InstContractProductService contractProductService;
    private final InstContractProductAssembler instContractProductAssembler;
    private final InstContractFeeWorkflowService contractFeeWorkflowService;
    private final InstContractBusinessValidator businessValidator;



    /**
     * 查询费用信息列表，无敏感数据
     */
    public List<InstFeeQueryVO> queryFeeList(InstContractProductQueryRequest request) {
        List<InstContractFeeItemFullyVO> feeItemFullyVoList = queryFeeFullyList(request);
        // 去除费用敏感信息
        return feeItemFullyVoList.stream().map(instContractProductAssembler::feeFullyVo2QueryVo).collect(Collectors.toList());
    }

    /**
     * 费用完整信息查询，带费用信息
     */
    public List<InstContractFeeItemFullyVO> queryFeeFullyList(InstContractProductQueryRequest request){
        // 1. 根据参数查询并构造原始产品列表
        List<InstContractOriginProductPO> originProductList = contractProductService.composeOriginProductList(request);
        return filterAndComposeFeeList(request, originProductList);
    }

    /**
     * 查询最新版本的费用信息列表，带费用信息
     */
    public List<InstContractFeeItemFullyVO> queryLatestVersionFeeFullyList(InstContractProductQueryRequest request){
        // 1. 根据参数查询并构造原始产品列表
        List<InstContractOriginProductPO> originProductList = contractProductService.composeLatestVersionOriginProductList(request);
        return filterAndComposeFeeList(request, originProductList);
    }

    /**
     * 根据查询参数过滤并构造费用列表
     */
    private List<InstContractFeeItemFullyVO> filterAndComposeFeeList(InstContractProductQueryRequest request, List<InstContractOriginProductPO> originProductList){
        // 2. 根据查询条件过滤数据
        // 2.1 目标机构、卡组、支付方式类型 过滤原始产品
        originProductList = contractProductService.filterOriginProductList(request, originProductList);
        Map<String, InstContractOriginProductPO> originProductMap = originProductList.stream().collect(Collectors.toMap(InstContractOriginProductPO::getInstOriginProductNo, product -> product));

        // 2.2 获取 feeItem 列表
        List<InstContractFeeItemPO> feeItemList = originProductList.stream().filter(product -> Objects.nonNull(product.getContractFeeItems())).flatMap(product -> product.getContractFeeItems().stream()).collect(Collectors.toList());

        // 2.3 币种过滤
        if(StringUtil.isNotBlank(request.getPayCurrency())){
            feeItemList = feeItemList.stream().filter(feeItem -> feeItem.getPayCurrency().equals(request.getPayCurrency())).collect(Collectors.toList());
        }

        // 3. 处理数据
        return feeItemList.stream()
                // feeItem 转 VO
                .map(instContractProductAssembler::feeItem2FullyVO)
                // 填充合同、产品信息
                .peek(vo -> Optional.ofNullable(originProductMap.get(vo.getInstOriginProductNo()))
                        .ifPresent(originProduct -> instContractProductAssembler.feeFullyVoAddContractMsg(vo, originProduct, originProduct.getContractBaseInfo(), originProduct.getContractVersionInfo())))
                // 填充标准化信息
                .peek(vo -> vo.setStandardProductMsgList(contractProductService.getMsgFromOriginMap(originProductMap,vo.getInstOriginProductNo())))
                .sorted(Comparator.comparing(InstContractFeeItemFullyVO::getInstProductName))
                .collect(Collectors.toList());
    }


    /**
     * 查询结算信息列表
     */
    public List<InstFeeQueryVO> querySettleList(InstContractProductQueryRequest request) {
        // 1. 根据参数查询并构造原始产品列表
        List<InstContractOriginProductPO> originProductList = contractProductService.composeOriginProductList(request);

        // 2. 根据查询条件过滤数据
        // 2.1 目标机构、卡组、支付方式类型 过滤原始产品
        originProductList = contractProductService.filterOriginProductList(request, originProductList);
        Map<String, InstContractOriginProductPO> originProductMap = originProductList.stream().collect(Collectors.toMap(InstContractOriginProductPO::getInstOriginProductNo, product -> product));

        // 2.2 获取 settle 列表
        List<InstContractSettlementItemPO> settleItemList = originProductList.stream().filter(product -> Objects.nonNull(product.getSettlementItems())).flatMap(product -> product.getSettlementItems().stream()).collect(Collectors.toList());

        // 2.3 币种过滤
        if(StringUtil.isNotBlank(request.getPayCurrency())){
            settleItemList = settleItemList.stream().filter(item -> item.getPayCurrency().equals(request.getPayCurrency())).collect(Collectors.toList());
        }

        // 3. 处理数据
        return settleItemList.stream()
                // settleItem 转 VO
                .map(vo -> {
                    InstContractOriginProductPO originProduct = originProductMap.get(vo.getInstOriginProductNo());
                    return instContractProductAssembler.settleItem2QueryVO(vo , originProduct, originProduct.getContractBaseInfo(), originProduct.getContractVersionInfo());
                })
                // 标准化信息
                .peek(vo -> vo.setStandardProductMsgList(contractProductService.getMsgFromOriginMap(originProductMap,vo.getInstOriginProductNo())))
                .collect(Collectors.toList());

    }

    /**
     * 根据原始产品编号查询费用信息
     */
    public List<InstContractFeeItemVO> queryFeeConfigByOriginProduct(String originProductNo){
        AssertUtil.notEmpty(originProductNo, "ERROR", "originProductNo 为空");
        List<InstContractFeeItemPO> instContractFeeItemPo = instContractFeeItemRepository.queryByOriginProductNo(originProductNo);
        return instContractFeeItemPo.stream().map(item -> {
            InstContractFeeItemVO feeItemVO = new InstContractFeeItemVO();
            Map<String, Object> feeConfigMap = JSON.parseObject(item.getFeeConfig(), new TypeReference<Map<String, Object>>(){});
            feeItemVO.setInstOriginProductNo(item.getInstOriginProductNo());
            feeItemVO.setInstContractFeeItemNo(item.getInstContractFeeItemNo());
            feeItemVO.setTradeFeeConfig(JSON.parseObject(JSON.toJSONString(feeConfigMap.get(FeeTypeEnum.TRADE.name())), InstContractFeeItemVO.FeeConfig.class));
            feeItemVO.setRefundFeeConfig(JSON.parseObject(JSON.toJSONString(feeConfigMap.get(FeeTypeEnum.REFUND.name())), InstContractFeeItemVO.FeeConfig.class));
            feeItemVO.setCbFeeConfig(JSON.parseObject(JSON.toJSONString(feeConfigMap.get(FeeTypeEnum.CHARGEBACK.name())), InstContractFeeItemVO.FeeConfig.class));
            feeItemVO.setFxFeeConfig(instContractProductAssembler.instContractFeeItem2FXConfig(item));
            feeItemVO.setTaxFeeConfig(JSON.parseArray(item.getTaxConfig(), InstContractFeeItemVO.TaxConfig.class));
            return feeItemVO;
        }).collect(Collectors.toList());
    }



    /**
     * 根据 FeeItemNo 编号查询费用信息
     */
    public InstContractFeeItemVO queryFeeConfigByNo(String feeItemNo){
        AssertUtil.notEmpty(feeItemNo, "ERROR", "feeItemNo 为空");
        InstContractFeeItemPO feeItem = instContractFeeItemRepository.queryByFeeItemNo(feeItemNo);
        InstContractFeeItemVO feeItemVO = new InstContractFeeItemVO();
        Map<String, Object> feeConfigMap = JSON.parseObject(feeItem.getFeeConfig(), new TypeReference<Map<String, Object>>(){});
        feeItemVO.setInstOriginProductNo(feeItem.getInstOriginProductNo());
        feeItemVO.setInstContractFeeItemNo(feeItem.getInstContractFeeItemNo());

        // 解析费用配置
        setVoFeeConfig(feeConfigMap, FeeTypeEnum.TRADE, feeItemVO::setTradeFeeConfig);
        setVoFeeConfig(feeConfigMap, FeeTypeEnum.VA_ACCOUNT, feeItemVO::setVaAccountFeeConfig);
        setVoFeeConfig(feeConfigMap, FeeTypeEnum.REFUND, feeItemVO::setRefundFeeConfig);
        setVoFeeConfig(feeConfigMap, FeeTypeEnum.CHARGEBACK, feeItemVO::setCbFeeConfig);
        setVoFeeConfig(feeConfigMap, FeeTypeEnum.RETRIEVAL_REQUEST, feeItemVO::setRetrievalRequestFeeConfig);

        // 特殊费用
        feeItemVO.setFxFeeConfig(instContractProductAssembler.instContractFeeItem2FXConfig(feeItem));
        feeItemVO.setTaxFeeConfig(JSON.parseArray(feeItem.getTaxConfig(), InstContractFeeItemVO.TaxConfig.class));
        return feeItemVO;
    }

    /**
     * 解析 FeeConfigMap 中费用配置
     */
    private void setVoFeeConfig(Map<String, Object> feeConfigMap, FeeTypeEnum feeType, Consumer<InstContractFeeItemVO.FeeConfig> setter) {
        Object config = feeConfigMap.get(feeType.name());
        if (Objects.nonNull(config)) {
            InstContractFeeItemVO.FeeConfig feeConfig = JSON.parseObject(JSON.toJSONString(config), InstContractFeeItemVO.FeeConfig.class);
            setter.accept(feeConfig);
        }
    }

    /**
     * 费用信息修改
     * 一次只能修改一种费用
     */
    public Boolean saveFeeConfigByFeeItemNo(String shareId, String feeItemNo, InstContractFeeItemVO feeItem){
        AssertUtil.isTrue(StringUtil.isNotEmpty(feeItemNo), "ERROR", "feeItemNo is empty");
        AssertUtil.isTrue(ObjectUtil.isNotEmpty(feeItem), "ERROR", "feeItem is empty");
        // 1. 提交数据判断
        feeItemUpdateLimit(feeItem);
        // 存入数据都为 JSON 字符串，做单字段校验容易出 BUG，直接校验存在无权限数据则不提交，看不到费用数据时本就无修改权限
        AssertUtil.isTrue( !JSON.toJSONString(feeItem).contains(AuthConstant.NO_PERMISSION.name()), "ERROR", "存在无权限的数据");

        // 查询原始数据
        InstContractFeeItemPO originFeeItem = instContractFeeItemRepository.queryByFeeItemNo(feeItemNo);
        // 检查是否存在版本升级审批流程
        InstContractBaseInfoPO baseInfo = businessValidator.contractVersionUpgradeProcessCheck(originFeeItem.getInstOriginProductNo());
        // 2. 组装费用数据
        InstContractFeeItemPO newFeeItem = new InstContractFeeItemPO();
        BeanUtils.copyProperties(originFeeItem, newFeeItem);
        // 审批表单信息
        InstContractFeeFormMsgDTO formMsg = composeFeeModifiedProcessFormMsg(originFeeItem.getInstOriginProductNo(),originFeeItem, null);

        // TRADE 数据
        updateFeeConfigForType(originFeeItem, newFeeItem, feeItem.getTradeFeeConfig(), FeeTypeEnum.TRADE);

        // VA 账号费
        if(ContractBusinessTypeEnum.VA.name().equals(baseInfo.getInstProductType())){
            updateFeeConfigForType(originFeeItem, newFeeItem, feeItem.getVaAccountFeeConfig(), FeeTypeEnum.VA_ACCOUNT);
        }

        // 退款手续费
        updateFeeConfigForType(originFeeItem, newFeeItem, feeItem.getRefundFeeConfig(), FeeTypeEnum.REFUND);

        // 拒付手续费 | 调单手续费
        if(ContractBusinessTypeEnum.DISP.name().equals(baseInfo.getInstProductType())){
            updateFeeConfigForType(originFeeItem, newFeeItem, feeItem.getCbFeeConfig(), FeeTypeEnum.CHARGEBACK);
            updateFeeConfigForType(originFeeItem, newFeeItem, feeItem.getRetrievalRequestFeeConfig(), FeeTypeEnum.RETRIEVAL_REQUEST);
        }

        // FX 数据
        if(Objects.nonNull(feeItem.getFxFeeConfig())){
            Optional.ofNullable(feeItem.getFxFeeConfig().getFxSpread()).ifPresent( fxSpread -> newFeeItem.setFxSpread(new BigDecimal(fxSpread)));
            newFeeItem.setCurrencyExchangeTime(feeItem.getFxFeeConfig().getCurrencyExchangeTime());
        }

        // TAX 数据
        if(Objects.nonNull(feeItem.getTaxFeeConfig())){
            List<TaxConfig> taxConfigList = JSON.parseArray(JSON.toJSONString(feeItem.getTaxFeeConfig()), TaxConfig.class);
            newFeeItem.setTaxConfig(JSON.toJSONString(taxConfigList));
        }
        formMsg.setModifiedData(newFeeItem);
        return contractFeeWorkflowService.feeConfigModifiedProcessStart(shareId, formMsg, originFeeItem, newFeeItem);
    }

    private void updateFeeConfigForType(InstContractFeeItemPO originFeeItem, InstContractFeeItemPO targetFeeItem, InstContractFeeItemVO.FeeConfig feeConfig, FeeTypeEnum feeType){
        if(Objects.nonNull(feeConfig)){
            // 原始 FeeMap，不存在时初始化
            Map<String, Object> feeMap = Optional.ofNullable(JSON.parseObject(originFeeItem.getFeeConfig(), new TypeReference<Map<String, Object>>(){}))
                    .orElseGet(HashMap::new);
            // 覆盖费用项配置
            feeMap.put(feeType.name(), JSON.parseObject(JSON.toJSONString(feeConfig), FeeConfig.class));
            // 更新 FeeConfig
            targetFeeItem.setFeeConfig(JSON.toJSONString(feeMap));
        }
    }

    /**
     * 限制只能提交一项修改
     */
    public void feeItemUpdateLimit(InstContractFeeItemVO feeItem){
        long itemCount = Stream.of(
                feeItem.getTradeFeeConfig(),
                feeItem.getFxFeeConfig(),
                feeItem.getRefundFeeConfig(),
                feeItem.getCbFeeConfig(),
                feeItem.getTaxFeeConfig(),
                feeItem.getVaAccountFeeConfig(),
                feeItem.getRetrievalRequestFeeConfig()
        ).filter(Objects::nonNull).count();

        AssertUtil.isTrue(itemCount == 1, "ERROR", "只能提交一项费用信息修改");
    }



    /**
     * 根据原始产品编号查询结算信息
     */
    public List<InstContractSettlementVO> querySettlementByOriginProduct(String originProductNo){
        AssertUtil.isTrue(StringUtil.isNotEmpty(originProductNo), "ERROR", "originProductNo is empty");
        List<InstContractSettlementItemPO> settlementItemList = instContractSettlementItemRepository.queryByOriginProductNo(originProductNo);
        return settlementItemList.stream().map( item -> {
            InstContractSettlementVO settlementVO = new InstContractSettlementVO();
            settlementVO.setInstOriginProductNo(item.getInstOriginProductNo());
            settlementVO.setInstContractSettlementItemNo(item.getInstContractSettlementItemNo());
            settlementVO.setSettleFeeConfig(JSON.parseObject(item.getSettleFeeConfig(), InstContractFeeItemVO.FeeConfig.class));
            settlementVO.setSettleDateConfigs(JSON.parseArray(item.getSettleDateConfig(), InstContractSettlementVO.InstSettleDateConfig.class));
            settlementVO.setSettlePaymentConfig(JSON.parseObject(item.getSettlePaymentConfig(), InstContractSettlementVO.InstSettlePaymentConfig.class));
            return settlementVO;
        }).collect(Collectors.toList());
    }


    /**
     * 根据 SettleItemNo 查询结算信息
     */
    public InstContractSettlementVO querySettlementFeeByNo(String settleItemNo){
        AssertUtil.isTrue(StringUtil.isNotEmpty(settleItemNo), "ERROR", "settleItemNo is empty");
        InstContractSettlementItemPO settleItem = instContractSettlementItemRepository.queryOneByNo(settleItemNo);
        AssertUtil.isTrue(Objects.nonNull(settleItem), "ERROR", "settleItem is not exist");
        return instContractProductAssembler.settleItem2VO(settleItem);
    }
    /**
     * 根据 SettleItemNo 列表查询结算信息
     */
    public List<InstContractSettlementVO> querySettlementFeeByNoList(List<String> settleItemNoList){
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(settleItemNoList), "ERROR", "settleItemNo is empty");
        List<InstContractSettlementItemPO> settlementItemPoList = instContractSettlementItemRepository.queryListByNos(settleItemNoList);
        return settlementItemPoList.stream().map(instContractProductAssembler::settleItem2VO).collect(Collectors.toList());
    }

    /**
     * 根据原始产品编号修改结算数据
     */
    public Boolean saveSettlementConfigBySettleItemNo(String shareId, String settleItemNo, InstContractSettlementVO settlementItem){
        AssertUtil.isTrue(StringUtil.isNotEmpty(settleItemNo), "ERROR", "settleItemNo is empty");
        AssertUtil.isTrue(ObjectUtil.isNotEmpty(settlementItem), "ERROR", "feeItem is empty");
        // 1. 提交数据判断
        settleItemUpdateLimit(settlementItem);
        // 存入数据都为 JSON 字符串，做单字段校验容易出 BUG，直接校验存在无权限数据则不提交，看不到费用数据时本就无修改权限
        AssertUtil.isTrue( !JSON.toJSONString(settlementItem).contains(AuthConstant.NO_PERMISSION.name()), "ERROR", "存在无权限的数据");

        // 查询原始数据
        InstContractSettlementItemPO originSettleItem = instContractSettlementItemRepository.queryOneByNo(settleItemNo);
        // 检查是否存在版本升级审批流程
        businessValidator.contractVersionUpgradeProcessCheck(originSettleItem.getInstOriginProductNo());

        // 2. 组装费用数据
        InstContractSettlementItemPO newSettleItem = new InstContractSettlementItemPO();
        BeanUtils.copyProperties(originSettleItem, newSettleItem);
        InstContractSettleFormMsgDTO formMsg = composeSettleModifiedProcessFormMsg(originSettleItem.getInstOriginProductNo(), originSettleItem, null);

        // 2.1 修改结算币种
        if(Objects.nonNull(settlementItem.getSettleConfig())){
            newSettleItem.setSettleCurrency(settlementItem.getSettleConfig().getSettleCurrency());
            newSettleItem.setChannelMerchantNo(settlementItem.getSettleConfig().getChannelMerchantNo());
        }

        // 2.2 结算费用
        if(Objects.nonNull(settlementItem.getSettleFeeConfig())) {
            FeeConfig settleFeeConfig = JSON.parseObject(JSON.toJSONString(settlementItem.getSettleFeeConfig()), FeeConfig.class);
            newSettleItem.setSettleFeeConfig(JSON.toJSONString(settleFeeConfig));
        }
        // 2.3 结算周期
        if(Objects.nonNull(settlementItem.getSettleDateConfigs())) {
            newSettleItem.setSettleDateConfig(JSON.toJSONString(settlementItem.getSettleDateConfigs()));
        }
        // 2.4 结算账户
        if(Objects.nonNull(settlementItem.getSettlePaymentConfig())) {
            InstSettlePaymentConfig settlePaymentConfig = JSON.parseObject(JSON.toJSONString(settlementItem.getSettlePaymentConfig()), InstSettlePaymentConfig.class);
            newSettleItem.setSettlePaymentConfig(JSON.toJSONString(settlePaymentConfig));
        }
        formMsg.setModifiedData(newSettleItem);
        return contractFeeWorkflowService.settleConfigModifiedProcessStart(shareId, formMsg, originSettleItem, newSettleItem);
    }

    /**
     * 限制只能提交一项修改
     */
    public void settleItemUpdateLimit(InstContractSettlementVO settlementItem){
        int itemCount = 0;
        itemCount = Objects.nonNull(settlementItem.getSettleConfig()) ? itemCount+1 : itemCount;
        itemCount = ObjectUtil.isNotEmpty(settlementItem.getSettleFeeConfig()) ? itemCount+1 : itemCount;
        itemCount = Objects.nonNull(settlementItem.getSettleDateConfigs()) ? itemCount+1 : itemCount;
        itemCount = ObjectUtil.isNotEmpty(settlementItem.getSettlePaymentConfig()) ? itemCount+1 : itemCount;
        AssertUtil.isTrue( itemCount == 1, "ERROR", "只能提交一项费用信息修改");
    }


    /**
     * 批量修改结算信息
     */
    public Boolean modifiedSettleByNoList(String shareId, InstSettleBatchModifiedReqDTO request){
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(request.getSettleItemNoList()), "ERROR", "settleItemNoList is empty");
        AssertUtil.isTrue(Objects.nonNull(request.getSettleData()), "ERROR", "settleData is null");
        // 查询原始数据
        List<InstContractSettlementItemPO> originSettleItemPos = instContractSettlementItemRepository.queryListByNos(request.getSettleItemNoList());
        // 校验是否单个机构批量修改
        List<String> originProductNoList = originSettleItemPos.stream().map(InstContractSettlementItemPO::getInstOriginProductNo).collect(Collectors.toList());
        InstContractBaseInfoPO baseInfo = businessValidator.isMultiContract(originProductNoList);
        String instCode = baseInfo.getInstCode();
        String entity = baseInfo.getContractEntity();
        String instProductType = baseInfo.getInstProductType();
        String businessKey = ListUtils.quickBuildUnderlineKey(instProductType, instCode, entity);

        // 检查是否存在版本升级审批流程
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(originProductNoList), "ERROR", "originProductNoList is empty");
        businessValidator.contractVersionUpgradeProcessCheck(originProductNoList.get(0)); // CHECKER 校验机构版本升级流程，仅需使用第一个原始产品号即可

        // 组装结算数据
        List<InstContractSettlementItemPO> modifiedSettleItemPos = originSettleItemPos.stream()
                .map(instContractProductAssembler::settlePoDeepCopy)
                .peek(settlePo -> instContractProductAssembler.settleVO2PO(settlePo,request.getSettleData()))
                .peek(settlePo -> settlePo.setSettleFeeConfig(Optional.ofNullable(request.getSettleData().getSettleFeeConfig()).map(data -> JSON.toJSONString(JSON.parseObject(JSON.toJSONString(data), FeeConfig.class))).orElse(settlePo.getSettleFeeConfig())))
                .peek(settlePo -> settlePo.setSettleDateConfig(mergeDateConfigs(settlePo.getSettleDateConfig(), request.getSettleData())))
                .peek(settlePo -> settlePo.setSettlePaymentConfig(Optional.ofNullable(request.getSettleData().getSettlePaymentConfig())
                        .map(data -> JSON.toJSONString(JSON.parseObject(JSON.toJSONString(request.getSettleData().getSettlePaymentConfig()), InstSettlePaymentConfig.class)))
                        .orElse(settlePo.getSettlePaymentConfig())))
                .collect(Collectors.toList());

        Map<String, InstContractSettlementItemPO> originSettleItemMap = originSettleItemPos.stream().collect(Collectors.toMap(InstContractSettlementItemPO::getInstContractSettlementItemNo, v -> v));
        Map<String, InstContractSettlementItemPO> modifiedSettleItemMap = modifiedSettleItemPos.stream().collect(Collectors.toMap(InstContractSettlementItemPO::getInstContractSettlementItemNo, v -> v));

        // 组装审批信息
        List<InstContractSettleFormMsgDTO> formMsgList = request.getSettleItemNoList().stream().map(no -> {
            Map<String, Object> diffRes = new HashMap<>(4);
            diffRes.putAll(DiffUtil.diff2Map(originSettleItemMap.get(no).getSettleFeeConfig(), modifiedSettleItemMap.get(no).getSettleFeeConfig()));
            diffRes.putAll(DiffUtil.diff2Map(originSettleItemMap.get(no).getSettleDateConfig(), modifiedSettleItemMap.get(no).getSettleDateConfig()));
            diffRes.putAll(DiffUtil.diff2Map(originSettleItemMap.get(no).getSettlePaymentConfig(), modifiedSettleItemMap.get(no).getSettlePaymentConfig()));
            return composeSettleModifiedProcessFormMsg(originSettleItemMap.get(no).getInstOriginProductNo(), originSettleItemMap.get(no), diffRes);
        }).collect(Collectors.toList());

        // 4. 发起审批
        return contractFeeWorkflowService.settleConfigBatchModifiedProcessStart(shareId, formMsgList, originSettleItemPos, modifiedSettleItemPos,request.getSettleItemNoList(), businessKey);
    }

    /**
     * 合并结算周期信息
     */
    private String mergeDateConfigs(String originDateConfigStr, InstContractSettlementVO modifiedSettleData){
        List<InstContractSettlementVO.InstSettleDateConfig> originDateConfig = JSONArray.parseArray(originDateConfigStr, InstContractSettlementVO.InstSettleDateConfig.class);
        List<InstContractSettlementVO.InstSettleDateConfig> modifiedDateConfig = Optional.ofNullable(modifiedSettleData.getSettleDateConfigs()).orElse(new ArrayList<>());

        AssertUtil.isTrue(modifiedDateConfig.size() <= 1, "ERROR", "修改后结算周期信息长度不能大于1");
        AssertUtil.isTrue(originDateConfig.size() <= 1, "ERROR", "修改前结算周期信息长度不能大于1");

        // 原始周期信息不存在时直接替换
        if(CollectionUtils.isEmpty(originDateConfig)){
            originDateConfig = modifiedDateConfig;
        }else if(CollectionUtils.isNotEmpty(modifiedDateConfig)){
            // 原始周期信息存在则进行合并
            InstContractSettlementVO.InstSettleDateConfig originConfig = originDateConfig.get(0); //CHECKED
            Optional<InstContractSettlementVO.InstSettleDateConfig> modifiedConfig = Optional.ofNullable(modifiedDateConfig.get(0)); //CHECKED
            if(modifiedConfig.isPresent()){
                originConfig.setTimezone(Optional.ofNullable(modifiedConfig.get().getTimezone()).orElse(originConfig.getTimezone()));
                originConfig.setTransactionStartDate(Optional.ofNullable(modifiedConfig.get().getTransactionStartDate()).orElse(originConfig.getTransactionStartDate()));
                originConfig.setTransactionEndDate(Optional.ofNullable(modifiedConfig.get().getTransactionEndDate()).orElse(originConfig.getTransactionEndDate()));
                originConfig.setBillDate(Optional.ofNullable(modifiedConfig.get().getBillDate()).orElse(originConfig.getBillDate()));
                originConfig.setPaymentDate(Optional.ofNullable(modifiedConfig.get().getPaymentDate()).orElse(originConfig.getPaymentDate()));
                originConfig.setArriveDate(Optional.ofNullable(modifiedConfig.get().getArriveDate()).orElse(originConfig.getArriveDate()));
                originConfig.setExchangeDate(Optional.ofNullable(modifiedConfig.get().getExchangeDate()).orElse(originConfig.getExchangeDate()));
            }
        }
        return JSON.toJSONString(originDateConfig);
    }


    /**
     * 根据 feeItem 构造费用修改审批表单信息
     */
    public InstContractFeeFormMsgDTO composeFeeModifiedProcessFormMsg(String originProductNo, InstContractFeeItemPO originData, Map<String, Object> diffRes) {
        InstContractBaseFormMsgDTO baseFormMsg = contractProductService.composeBaseModifiedProcessFormMsg(originProductNo);
        InstContractFeeFormMsgDTO formMsg = instContractProductAssembler.baseFormMsg2FeeFormMsg(baseFormMsg);
        formMsg.setFeeItemNo(originData.getInstContractFeeItemNo());
        formMsg.setPayCurrency(originData.getPayCurrency());
        formMsg.setOriginData(originData);
        formMsg.setDiffMsg(diffRes);
        return formMsg;
    }

    /**
     * 根据 settleItem 构造结算修改审批表单信息
     */
    private InstContractSettleFormMsgDTO composeSettleModifiedProcessFormMsg(String originProductNo, InstContractSettlementItemPO originData, Map<String, Object> diffRes) {
        InstContractBaseFormMsgDTO baseFormMsg = contractProductService.composeBaseModifiedProcessFormMsg(originProductNo);
        InstContractSettleFormMsgDTO formMsg = instContractProductAssembler.baseFormMsg2SettleFormMsg(baseFormMsg);
        formMsg.setSettleItemNo(originData.getInstContractSettlementItemNo());
        formMsg.setPayCurrency(originData.getPayCurrency());
        formMsg.setOriginData(originData);
        formMsg.setDiffMsg(diffRes);
        return formMsg;
    }

    /**
     * 根据业务主键查询费用修改信息
     */
    public Map<String,Object> queryFeeDiffMsgByBusinessId(String businessId) {
        InstProcessDockPO processDockPo = processDockRepository.queryByBusinessId(businessId);
        TreeMap<String,Object> originData = JSON.parseObject(processDockPo.getOriginalFormContent(), new TypeReference<TreeMap<String, Object>>(){});
        TreeMap<String, Object> originFeeConfig = JSON.parseObject(String.valueOf(originData.get("feeConfig")), new TypeReference<TreeMap<String, Object>>() {});
        originFeeConfig.forEach((key, value) -> originFeeConfig.put(key, JSON.parseObject(String.valueOf(value), new TypeReference<TreeMap<String, Object>>() {})));
        originData.put("feeConfig",originFeeConfig);
        originData.put("taxConfig",JSON.parseArray(String.valueOf(originData.get("taxConfig"))));

        TreeMap<String,Object> modifiedData = JSON.parseObject(processDockPo.getFormContent(), new TypeReference<TreeMap<String, Object>>(){});
        TreeMap<String, Object> modifiedFeeConfig = JSON.parseObject(String.valueOf(modifiedData.get("feeConfig")), new TypeReference<TreeMap<String, Object>>() {});
        modifiedFeeConfig.forEach((key, value) -> modifiedFeeConfig.put(key, JSON.parseObject(String.valueOf(value), new TypeReference<TreeMap<String, Object>>() {})));
        modifiedData.put("feeConfig",modifiedFeeConfig);
        modifiedData.put("taxConfig",JSON.parseArray(String.valueOf(modifiedData.get("taxConfig"))));
        return new HashMap<String,Object>(4){{
            put("originData",originData);
            put("modifiedData",modifiedData);
        }};
    }

    /**
     * 根据业务主键查询结算修改信息
     */
    public Map<String,Object> querySettleDiffMsgByBusinessId(String businessId) {
        InstProcessDockPO processDockPo = processDockRepository.queryByBusinessId(businessId);
        TreeMap<String,Object> originData = JSON.parseObject(processDockPo.getOriginalFormContent(), new TypeReference<TreeMap<String, Object>>(){});
        originData.put("settleFeeConfig",JSON.parseObject(String.valueOf(originData.get("settleFeeConfig")), new TypeReference<TreeMap<String, Object>>(){}));
        originData.put("settleDateConfig",JSON.parseArray(String.valueOf(originData.get("settleDateConfig"))));
        originData.put("settlePaymentConfig",JSON.parseObject(String.valueOf(originData.get("settlePaymentConfig")), new TypeReference<TreeMap<String, Object>>(){}));

        TreeMap<String,Object> modifiedData = JSON.parseObject(processDockPo.getFormContent(), new TypeReference<TreeMap<String, Object>>(){});
        modifiedData.put("settleFeeConfig",JSON.parseObject(String.valueOf(modifiedData.get("settleFeeConfig")), new TypeReference<TreeMap<String, Object>>(){}));
        modifiedData.put("settleDateConfig",JSON.parseArray(String.valueOf(modifiedData.get("settleDateConfig"))));
        modifiedData.put("settlePaymentConfig",JSON.parseObject(String.valueOf(modifiedData.get("settlePaymentConfig")), new TypeReference<TreeMap<String, Object>>(){}));

        return new HashMap<String,Object>(4){{
            put("originData",originData);
            put("modifiedData",modifiedData);
        }};
    }

    /**
     * 费用修改-全量信息，包括费用维度
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean feeDimensionModified(String shareId, InstContractFeeItemRequestDTO request){
        // 拼接业务唯一键
        String feeItemBusinessKey = FeeItemConvertUtils.buildFeeItemBusinessKey(request, request.getInstOriginProductNo());
        // 校验唯一性
        AssertUtil.isTrue(instContractFeeItemRepository.checkFeeModifiedUnique(request.getInstContractFeeItemNo(), feeItemBusinessKey), "ERROR","费用唯一性校验不通过");
        // 查询数据
        InstContractFeeItemPO originData = instContractFeeItemRepository.queryByFeeItemNo(request.getInstContractFeeItemNo());
        InstContractFeeItemPO modifiedData = instContractProductAssembler.feeRequest2Po(request).setFeeBusinessKey(feeItemBusinessKey);
        // 记录日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeFeeOperateByAdmin(shareId, originData, modifiedData, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        contractOperateLogRepository.save(operateLog);
        return instContractFeeItemRepository.updateWithCheck(modifiedData);
    }


    /**
     * 根据支付币种添加新的费用信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String feeItemCopyByPayCurrency(String shareId, InstFeeQueryVO request) {
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstContractFeeItemNo()), "ERROR","费用信息编号不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPayCurrency()), "ERROR","支付币种不能为空");
        // 查询数据
        String feeItemNo = request.getInstContractFeeItemNo();
        InstContractFeeItemPO originData = instContractFeeItemRepository.queryByFeeItemNo(feeItemNo);
        InstContractFeeItemPO modifiedData = new InstContractFeeItemPO();
        // 拷贝数据，忽略多余维度
        modifiedData.setPayCurrency(request.getPayCurrency());
        modifiedData.setInstContractFeeItemNo(voucherAdapter.getInstProductFeeItemNo());
        modifiedData.setInstOriginProductNo(originData.getInstOriginProductNo());
        Optional.ofNullable(originData.getFeeConfig()).ifPresent(modifiedData::setFeeConfig);
        Optional.ofNullable(originData.getTaxConfig()).ifPresent(modifiedData::setTaxConfig);
        Optional.ofNullable(originData.getFxSpread()).ifPresent(modifiedData::setFxSpread);
        Optional.ofNullable(originData.getCurrencyExchangeTime()).ifPresent(modifiedData::setCurrencyExchangeTime);
        Optional.ofNullable(originData.getRoundingMode()).ifPresent(modifiedData::setRoundingMode);
        Optional.ofNullable(originData.getRoundingScale()).ifPresent(modifiedData::setRoundingScale);
        // 拼接业务唯一键
        String feeItemBusinessKey = FeeItemConvertUtils.buildFeeItemBusinessKey(modifiedData, originData.getInstOriginProductNo());
        // 校验唯一性
        AssertUtil.isTrue(instContractFeeItemRepository.checkFeeAddUnique(feeItemBusinessKey), "ERROR","费用唯一性校验不通过");
        // 新的 feeItemNo、费用唯一键
        modifiedData.setFeeBusinessKey(feeItemBusinessKey);
        // 记录日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeFeeOperateByAdmin(shareId, originData, modifiedData, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.ADD);
        contractOperateLogRepository.save(operateLog);
        instContractFeeItemRepository.save(modifiedData);
        return modifiedData.getInstContractFeeItemNo();
    }

    /**
     * 根据支付币种添加新的结算信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String settleItemCopyByPayCurrency(String shareId, InstFeeQueryVO request){
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstContractSettlementItemNo()), "ERROR","结算信息编号不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPayCurrency()), "ERROR","支付币种不能为空");
        // 查询数据
        String settleItemNo = request.getInstContractSettlementItemNo();
        InstContractSettlementItemPO originData = instContractSettlementItemRepository.getById(settleItemNo);
        InstContractSettlementItemPO modifiedData = new InstContractSettlementItemPO();
        modifiedData.setPayCurrency(request.getPayCurrency());
        modifiedData.setInstContractSettlementItemNo(voucherAdapter.getInstProductSettlementItemNo());
        modifiedData.setInstOriginProductNo(originData.getInstOriginProductNo());
        Optional.ofNullable(originData.getSettleFeeConfig()).ifPresent(modifiedData::setSettleFeeConfig);
        Optional.ofNullable(originData.getSettleDateConfig()).ifPresent(modifiedData::setSettleDateConfig);
        Optional.ofNullable(originData.getSettlePaymentConfig()).ifPresent(modifiedData::setSettlePaymentConfig);

        // 记录日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeSettleOperateByAdmin(shareId, originData, modifiedData, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.ADD);
        contractOperateLogRepository.save(operateLog);
        try{
            instContractSettlementItemRepository.save(modifiedData);
        } catch (DuplicateKeyException e) {
            log.warn("Duplicate entry", e);
            throw new BusinessException("ERROR","结算信息唯一性校验不通过");
        }
        return modifiedData.getInstContractSettlementItemNo();
    }

    /**
     * 根据 feeItemNo 删除费用信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String feeItemDel(String shareId, InstContractFeeItemVO feeItem){
        AssertUtil.isTrue(StringUtils.isNotBlank(feeItem.getInstContractFeeItemNo()), "ERROR","费用信息编号不能为空");
        InstContractFeeItemPO modifiedData = new InstContractFeeItemPO().setInstContractFeeItemNo(feeItem.getInstContractFeeItemNo());
        InstContractFeeItemPO originData = instContractFeeItemRepository.getById(feeItem.getInstContractFeeItemNo());
        AssertUtil.isTrue(ObjectUtil.isNotEmpty(originData), "ERROR", "费用信息不存在");
        // 记录日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeFeeOperateByAdmin(shareId, originData, modifiedData, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.DELETE);
        contractOperateLogRepository.save(operateLog);
        instContractFeeItemRepository.removeById(feeItem.getInstContractFeeItemNo());
        return feeItem.getInstContractFeeItemNo();
    }

    /**
     * 根据 settleItemNo 删除结算信息
     */
    @Transactional(rollbackFor = Exception.class)
    public String settleItemDel(String shareId, InstContractSettlementVO settleItem){
        AssertUtil.isTrue(StringUtils.isNotBlank(settleItem.getInstContractSettlementItemNo()), "ERROR","结算信息编号不能为空");
        InstContractSettlementItemPO modifiedData = new InstContractSettlementItemPO().setInstContractSettlementItemNo(settleItem.getInstContractSettlementItemNo());
        InstContractSettlementItemPO originData = instContractSettlementItemRepository.getById(settleItem.getInstContractSettlementItemNo());
        AssertUtil.isTrue(ObjectUtil.isNotEmpty(originData), "ERROR", "结算信息不存在");
        // 记录日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeSettleOperateByAdmin(shareId, originData, modifiedData, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.DELETE);
        contractOperateLogRepository.save(operateLog);
        instContractSettlementItemRepository.removeById(settleItem.getInstContractSettlementItemNo());
        return settleItem.getInstContractSettlementItemNo();
    }
}