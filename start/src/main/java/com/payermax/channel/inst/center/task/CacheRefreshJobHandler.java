package com.payermax.channel.inst.center.task;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.cache.CacheRegistry;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR> tracy
 * @version 2022-10-21 4:39 PM
 */
@Component
@Slf4j
@JobHandler(value = "cacheRefreshJobHandler")
public class CacheRefreshJobHandler extends IJobHandler {

    @Override
    public ReturnT<String> execute(String param) {
        if (StringUtils.isBlank(param)) {
            log.info("全量刷新本地缓存开始……");
            XxlJobLogger.log("全量刷新本地缓存开始……");
            Arrays.stream(CacheEnum.values()).parallel().forEach(item -> CacheRegistry.getCacheManager(item).refresh());
            XxlJobLogger.log("全量刷新本地缓存结束……");
            log.info("全量刷新本地缓存结束……");
        } else {
            log.info("指定缓存刷新本地缓存开始……[{}]", param);
            XxlJobLogger.log("指定缓存刷新本地缓存开始……");
            String[] caches = param.split(",");
            Arrays.stream(caches).parallel().forEach(item -> CacheRegistry.getCacheManager(CacheEnum.valueOf(item)).refresh());
            log.info("指定缓存刷新本地缓存结束……");
            XxlJobLogger.log("指定缓存刷新本地缓存结束……");
        }
        return ReturnT.SUCCESS;
    }
}
