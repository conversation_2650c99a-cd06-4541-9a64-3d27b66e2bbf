package com.payermax.channel.inst.center.service.matcher;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.service.FunnelModelMatchService;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class AbstractContractMatcher implements ContractMatcher {
    public AbstractContractMatcher() {
        log.info("Mater :[{}] register success!",getClass().getName());
        FunnelModelMatchService.register(this);
    }

    @Override
    public List<InstContractFeeItem> filterMatches(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        log.info("ContractMatcher :【{}】",getClass().getName());
        if (ignoreFilter(funnelMatchItem, feeItems)) {
            return feeItems;
        }
        List<InstContractFeeItem> preciseResult = preciseFilter(funnelMatchItem, feeItems);
        if (CollectionUtils.isNotEmpty(preciseResult)) {
            return preciseResult;
        } else {
            return fuzzyFilter(funnelMatchItem, feeItems);
        }
    }

    /**
     * 判定漏斗是否直接放行
     */
    protected abstract boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems);


    /**
     * 精准匹配
     */
    protected abstract List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems);


    /**
     * 模糊匹配
     */
    protected abstract List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems);
}
