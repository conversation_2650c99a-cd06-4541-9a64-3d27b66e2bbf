package com.payermax.channel.inst.center.service.finder;

import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractStandardProduct;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractQueryModeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstRiskFeeQueryRequest;
import com.payermax.channel.inst.center.facade.response.contract.InstRiskFeeInfoResponse;
import com.payermax.channel.inst.center.service.InstContractQueryServiceImpl;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/4
 * @DESC 风控产品费用查询器
 */
@Slf4j
@Component
public class InstRiskContractFinder extends AbstractInstContractInfoFinder {


    private final String CARD_ORG_WILDCARD = InstContractQueryServiceImpl.CARD_ORG_WILDCARD;
    private final String TARGET_ORG_WILDCARD = InstContractQueryServiceImpl.TARGET_ORG_WILDCARD;



    @PostConstruct
    public void init() {
        registryStrategy(ContractBizTypeEnum.R, this);
    }

    @Override
    protected void preProcessor(InstContractQueryContext context){
        InstRiskFeeQueryRequest riskRequest = (InstRiskFeeQueryRequest)context.getRequest();
        // 默认合约查询方式为 MID
        context.setContractQueryMode(ContractQueryModeEnum.MID);
        context.getRequest().setPaymentMethodType(riskRequest.getRiskProductType());
        context.getRequest().setTargetOrg(riskRequest.getRiskProductSpecificType());
        context.getRequest().setPayCurrency(riskRequest.getRiskTradeCurrency());
    }

    @Override
    protected void findSettle(InstContractQueryContext context) {
        // 风控产品无结算信息
        context.setSettleItem(null);
    }

    @Override
    protected InstRiskFeeInfoResponse composeResponse(InstContractQueryContext context) {

        InstRiskFeeQueryRequest request = (InstRiskFeeQueryRequest)context.getRequest();
        InstRiskFeeInfoResponse response = instContractRequestAssembler.convertReq2Resp(request);

        // 费用信息，风控产品无换汇、结算信息
        response.setFeeConfigMap(feeInfoConvertor.feeConfigConvert(context.getFeeItem()));

        // 风控信息无扣款信息
        response.setDeductConfig(null);
        feeInfoConvertor.responseDefaultFill(response, context);
        return response;
    }

    /**
     * 风控场景标准产品查询需要特殊实现
     */
    @Override
    protected InstContractStandardProduct queryStandardProduct(InstContractQueryContext context, List<InstContractStandardProduct> standardProducts, String paymentMethodType, String targetOrg, String cardOrg) {
        log.info("风控产品匹配逻辑, paymentMethodType: {}, targetOrg: {}, cardOrg: {}", paymentMethodType, targetOrg, cardOrg);
        AssertUtil.notEmpty(standardProducts, "ERROR", "合同绑定的风控产品为空");
        InstContractStandardProduct standardProduct = contractQueryService.preciseMatchingStandardProduct(paymentMethodType, targetOrg, cardOrg, standardProducts);
        if (standardProduct == null) {
            standardProduct = fuzzyMatchingStandardProduct(paymentMethodType, targetOrg, cardOrg, standardProducts);
        }
        AssertUtil.notNull(standardProduct, "ERROR", "精准/模糊匹配均查询不到风控产品");
        return standardProduct;
    }

    /**
     * 模糊匹配. 允许使用 target == '*' || cardOrg == 'CARDPAY' 兜底
     * 风控场景需要 目标机构及卡组 同时支持兜底查询
     */
    private InstContractStandardProduct fuzzyMatchingStandardProduct(String paymentMethodType, String targetOrg, String cardOrg, List<InstContractStandardProduct> standardProducts) {

        log.info("风控-标准产品模糊匹配逻辑");

        List<InstContractStandardProduct> standardProduct;

        // 获取 目标机构匹配的产品 / 卡组兜底产品 / 目标机构兜底产品
        standardProduct = standardProducts.stream().filter(item -> item.getPaymentMethodType().equalsIgnoreCase(paymentMethodType)
                        && (CARD_ORG_WILDCARD.equalsIgnoreCase(item.getCardOrg()) || TARGET_ORG_WILDCARD.equals(item.getTargetOrg()) || targetOrg.equalsIgnoreCase(item.getTargetOrg())))
                .collect(Collectors.toList());

        // 如果 targetOrg 有值, 先获取 targetOrg 匹配的产品，获取不到时使用 目标机构兜底产品
        if (StringUtils.isNotEmpty(targetOrg)) {
            standardProduct = standardProducts.stream().filter( item -> targetOrg.equalsIgnoreCase(item.getTargetOrg())).collect(Collectors.toList());
            // 获取不到时使用 目标机构兜底产品
            if(CollectionUtils.isEmpty(standardProduct)){
                standardProduct = standardProducts.stream().filter(item -> TARGET_ORG_WILDCARD.equals(item.getTargetOrg())).collect(Collectors.toList());
            }
        }

        // 如果 cardOrg 有值, cardOrg = 'CARDPAY' 去兜底
        if (StringUtils.isNotEmpty(cardOrg)) {
            standardProduct = standardProduct.stream().filter(item -> CARD_ORG_WILDCARD.equalsIgnoreCase(item.getCardOrg())).collect(Collectors.toList());
        }

        return standardProduct.stream().findFirst().orElse(null); //NO_CHECK 模糊匹配时，匹配到第一条标准产品信息则返回，符合预期
    }


}
