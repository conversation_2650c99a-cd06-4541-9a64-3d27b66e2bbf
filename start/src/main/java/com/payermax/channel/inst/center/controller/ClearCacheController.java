package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.infrastructure.client.RedisClientProxy;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> at 2022/10/11 18:26
 **/
@Api(tags = "清除缓存API")
@RestController
@RequestMapping("instCenter/clearCache")
@Slf4j
public class ClearCacheController {

    @Resource
    RedisClientProxy redisClientProxy;
    
    /**
     * cacheName支持指定和模糊删除
     * <p>
     * 示例：
     * 指定：机构账号：instFundsAccount:instFundsAccount，子级机构账号：instFundsAccount:instSubFundsAccount
     *
     **/
    @ApiOperation(value = "清除缓存", notes = "清除缓存")
    @PostMapping("/{cacheName}")
    @DigestLog(isRecord = true)
    public Long clearCache(@PathVariable("cacheName") String cacheName) {
        return redisClientProxy.clear(cacheName);
    }
}
