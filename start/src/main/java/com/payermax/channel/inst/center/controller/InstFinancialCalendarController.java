package com.payermax.channel.inst.center.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarDTO;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarInitTemplateDTO;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarManage;
import com.payermax.channel.inst.center.app.request.calendar.*;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.infra.ionia.fs.dto.UploadResponse;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Api(tags = "金融日历API")
@RestController
@Slf4j
@RequestMapping("instCenter/financialCalendar")
@RequiredArgsConstructor
public class InstFinancialCalendarController {

    private final InstFinancialCalendarManage instFinancialCalendarManage;

    /**
     * 查询日历列表
     */
    @PostMapping("queryCalendarList")
    @DigestLog(isRecord = true)
    public Result<Page<InstFinancialCalendarDTO>> queryCalendarList(@RequestBody InstFinancialCalendarRequest request) {
        return ResultUtils.success(instFinancialCalendarManage.queryCalendarList(request));
    }

    /**
     * 查询日历
     */
    @PostMapping("queryCalendarById")
    @DigestLog(isRecord = true)
    public Result<InstFinancialCalendarDTO> queryCalendarById(@RequestBody InstFinancialCalendarRequest request) {
        return ResultUtils.success(instFinancialCalendarManage.queryCalendarById(request));
    }

    /**
     * 查询节假日列表
     */
    @PostMapping("queryHolidayList")
    @DigestLog(isRecord = true)
    public Result<List<InstFinancialCalendarHoliday>> queryHolidayList(@RequestBody InstFinancialCalendarHolidayRequest request) {
        return ResultUtils.success(instFinancialCalendarManage.queryHolidayList(request));
    }


    /**
     * 初始化日历模板
     */
    @PostMapping("initCalendarTemplate")
    @DigestLog(isRecord = true)
    public Result<InstFinancialCalendarInitTemplateDTO> initCalendarTemplate(@RequestBody InstFinancialCalendarInitTemplateRequest request) {
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(request.getWeekendList()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "weekendList不能为空");
        AssertUtil.isTrue(Objects.nonNull(request.getCalendarYear()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "年度不能为空");
        AssertUtil.isTrue(Objects.nonNull(request.getCountry()) || Objects.nonNull(request.getCurrency()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "国家、币种不能同时为空");
        AssertUtil.isTrue(EnumUtils.isValidEnum(CalendarTypeEnum.class, request.getCalendarType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "日历类型错误");
        return ResultUtils.success(instFinancialCalendarManage.initCalendarTemplate(request));
    }

    /**
     * 新增日历流程发起
     */
    @PostMapping("startCalendarSaveProcess")
    @DigestLog(isRecord = true)
    public Result<Boolean> startCalendarSaveProcess(@RequestHeader("shareId") String shareId, @RequestBody @Valid InstFinancialCalendarSaveRequest request) {
        AssertUtil.isTrue(StringUtils.isNotBlank(shareId), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "shareId不能为空");
        request.setOwner(shareId);
        return ResultUtils.success(instFinancialCalendarManage.startCalendarSaveProcess(request));
    }

    /**
     * 更新日历流程发起
     */
    @PostMapping("startCalendarUpdateProcess")
    @DigestLog(isRecord = true)
    public Result<Boolean> startCalendarUpdateProcess(@RequestHeader("shareId") String shareId, @RequestBody InstFinancialCalendarSaveRequest request) {
        AssertUtil.isTrue(StringUtils.isNotBlank(shareId), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "shareId不能为空");
        request.setShareId(shareId);
        return ResultUtils.success(instFinancialCalendarManage.startCalendarUpdateProcess(request));
    }

    /**
     * 日历上下线流程发起
     */
    @PostMapping("startCalendarActivateProcess")
    @DigestLog(isRecord = true)
    public Result<Boolean> startCalendarActivateProcess(@RequestHeader("shareId") String shareId, @RequestBody InstFinancialCalendarActivateRequest request) {
        AssertUtil.isTrue(StringUtils.isNotBlank(shareId), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "shareId不能为空");
        request.setShareId(shareId);
        return ResultUtils.success(instFinancialCalendarManage.startCalendarActivateProcess(request));
    }


    /**
     * 日历导出
     */
    @PostMapping("calendarExport")
    @DigestLog(isRecord = true)
    public Result<UploadResponse> calendarExport(@RequestBody List<String> calendarList) {
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(calendarList), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "calendarList不能为空");
        return ResultUtils.success(instFinancialCalendarManage.calendarExport(calendarList));
    }

}
