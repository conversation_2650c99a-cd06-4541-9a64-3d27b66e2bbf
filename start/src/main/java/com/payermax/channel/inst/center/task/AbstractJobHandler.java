package com.payermax.channel.inst.center.task;

import com.payermax.channel.inst.center.common.enums.LockRegionEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/6 19:35
 **/
@Slf4j
public abstract class AbstractJobHandler<T> extends IJobHandler {

    public static final LockRegionEnum lockRegionEnum = LockRegionEnum.TASK_LOCK;

    @Override
    public ReturnT<String> execute(String param) {
        ReturnT<String> result = SUCCESS;
        String taskName = this.getClass().getSimpleName();
        log.info("{} task is start. param = {}", taskName, param);
        // 参数校验
        T t = validateParams(param);
        // 获取任务分布式锁
        String lockKey = buildTaskKey(taskName, t);
        if (getTaskLock(lockKey)) {
            try {
                // 执行业务逻辑
                executeBusiness(t);
                log.info("{} task is end.", taskName);
            } catch (Exception e) {
                log.error("AbstractJobHandler-executeBusiness：{}", e);
                XxlJobLogger.log("输出到xxl-批量补偿子级账号异常", e.getMessage(), param);
                result = FAIL;
            } finally {
                releaseTaskLock(lockKey);
            }
        }

        return result;
    }

    /**
     * 参数转换及校验
     *
     * @param param
     */
    public abstract T validateParams(String param);

    /**
     * 执行业务逻辑
     *
     * @param param
     */
    public abstract void executeBusiness(T param);

    /**
     * 构建任务锁
     *
     * @param taskName
     */
    public abstract String buildTaskKey(String taskName, T param);

    /**
     * 获取任务分布式锁（同时间仅有一个任务执行）
     *
     * @param lockKey
     */
    public abstract boolean getTaskLock(String lockKey);

    /**
     * 释放分布式锁
     *
     * @param lockKey
     */
    public abstract void releaseTaskLock(String lockKey);
}
