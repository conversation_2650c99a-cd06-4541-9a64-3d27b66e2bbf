package com.payermax.channel.inst.center.service.finder;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractOriginProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractQueryModeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstContractAllFxAndSettleResponse;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.request.contract.config.DeductConfig;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/16
 * @DESC 合约外汇&结算加点查询器，特殊实现
 *      查询合约下所有外汇加点及结算信息
 */
@Slf4j
@Component
public class InstContractAllFxAndSettleFinder extends AbstractInstContractInfoFinder {


    /**
     * 出款扣款币种暂时没,特殊场景使用nacos配置
     */
    @NacosValue(value = "#{${inst.funds.contract.payout.deduct.map}}", autoRefreshed = true)
    private HashMap<String, String> payoutDeductMap;



    /**
     * 前置处理
     */
    @Override
    protected void preProcessor(InstContractQueryContext context){
        context.setContractQueryMode(ContractQueryModeEnum.DIRECT);
    }

    @Override
    protected void paramCheck(InstContractQueryContext context) {
        InstInfoQueryRequest request =  context.getRequest();
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getBizType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "业务类型不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体编码不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstCode()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "机构编码不能为空");
        AssertUtil.isTrue(ObjectUtils.isNotNull(request.getTransactionTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "交易时间不能为空");
    }

    @Override
    protected InstInfoQueryResponse composeResponse(InstContractQueryContext context) {
        InstContractAllFxAndSettleResponse response = instContractRequestAssembler.convertRequest2AllFxAndSettleResponse(context.getRequest());
        // 处理原始产品下所有 FX 和 结算信息
        List<InstInfoQueryResponse> fxAndSettleList = context.getContractVersionInfo().getOriginProducts().stream()
                .filter( item -> CollectionUtils.isNotEmpty(item.getContractFeeItems()))
                .flatMap(originProduct ->
                        originProduct.getContractFeeItems().stream()
                                .map(feeItem -> fxAndSettleConvert(context, originProduct, feeItem))
                                .collect(Collectors.toList()).stream()).collect(Collectors.toList());
        response.setFxAndSettleList(fxAndSettleList);

        // 返回值通用填充
        feeInfoConvertor.responseDefaultFill(response, context);
        return response;
    }

    /**
     * 合约外汇&结算信息转换器
     */
    private InstInfoQueryResponse fxAndSettleConvert(InstContractQueryContext context, InstContractOriginProduct originProduct, InstContractFeeItem feeItem){
        InstInfoQueryRequest request = context.getRequest();
        InstInfoQueryResponse fxAndSettleItem = instContractRequestAssembler.convertRequest2Response(request);

        // 根据原始产品 获取 SettleItem
        InstContractSettlementItem settleItem = null;

        if (ContractBizTypeEnum.I.name().equalsIgnoreCase(request.getBizType())) {
            // 只有入款需要查询结算信息
            try {
                settleItem = contractQueryService.querySettleByOriProduct(originProduct, feeItem.getPayCurrency(), request.getChannelMerchantCode());
            }catch (Exception e){
                log.warn("Settle info query error!{}", e.getMessage());
            }
        } else {
            // 出款根据 Nacos 配置 或 填充本币
            DeductConfig deductConfig = new DeductConfig();
            String deductCurrency = payoutDeductMap.getOrDefault(feeItem.getInstContractFeeItemNo(), feeItem.getPayCurrency());
            deductConfig.setDeductCurrency(deductCurrency);
            fxAndSettleItem.setDeductConfig(deductConfig);
        }
        fxAndSettleItem.setPayCurrency(feeItem.getPayCurrency());

        // 返回值通用填充
        feeInfoConvertor.responseDefaultFill(fxAndSettleItem, context);

        // 结算信息
        feeInfoConvertor.settleConfigConvert(fxAndSettleItem, settleItem);
        // FX 信息
        fxAndSettleItem.setFxConfig(instContractRequestAssembler.convertInstFx2Response(feeItem));
        return fxAndSettleItem;
    }

    /**
     * 合约查询-此处仅需查询到机构维度，忽略主体
     */
    @Override
    protected void findContractAndProduct(InstContractQueryContext context){
        log.info("开始查询合约、产品信息");
        // 查询合约
        InstContractVersionInfo versionInfo = contractQueryByScene(context);
        context.setContractVersionInfo(versionInfo);
    }

    /**
     * 无需查询单条费用，空实现
     */
    @Override
    protected void findFee(InstContractQueryContext context){
        log.info("无需查询单条费用，空实现");
    }

    /**
     * 无需查询单条结算，空实现
     */
    @Override
    protected void findSettle(InstContractQueryContext context){
        log.info("无需查询单条结算，空实现");
    }



}
