package com.payermax.channel.inst.center.service;

import com.payermax.channel.inst.center.facade.InstNewInfoQueryFacadeImpl;
import com.payermax.channel.inst.center.facade.request.contract.config.SettlementConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.channel.inst.center.facade.util.ChannelSettleInfoCalculateUtil;
import com.payermax.channel.inst.center.service.convertor.DslEntity2SettleInfoVoConvertor;
import com.payermax.channel.inst.center.service.convertor.SettleInfoConvertor;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class InstContractProductManageService {

    private final SettleInfoConvertor settleInfoConvertor;

    /**
     * 结算时间计算
     *
     * @param settleDate 周期配置
     * @param dateStr 交易时间
     */
    public Map<String,String> settleDateCalculate(SettleDate settleDate, String dateStr) throws ParseException {
        // 初始化
        SettlementConfig settlementConfig = getSettlementConfig(settleDate);

        Map<String,String> result = new LinkedHashMap<>(4);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = format.parse(dateStr);
        // 结算日
        long settlementDay = ChannelSettleInfoCalculateUtil.calculateExchangeDate("SettlementDay"
                , settlementConfig.getSettleInfoForCalculate(), date.getTime());
        // 付款日
        long paymentDay = ChannelSettleInfoCalculateUtil.calculateExchangeDate("SettlementDay"
                , settlementConfig.getFundsPaymentForCalculate(), date.getTime());
        // 到账日
        long arriveDay = ChannelSettleInfoCalculateUtil.calculateExchangeDate("SettlementDay"
                , settlementConfig.getFundsArrivedForCalculate(), date.getTime());
        // 换汇日
        if(StringUtils.isNotBlank(settleDate.getExchangeDate())){
            long exchangeDate = ChannelSettleInfoCalculateUtil.calculateExchangeDate("SettlementDay"
                    , settlementConfig.getFundsExchangeForCalculate(), date.getTime());
            result.put("exchangeDate",format.format(new Date(exchangeDate)));
        }

        result.put("settlementDay",format.format(new Date(settlementDay)));
        result.put("paymentDay",format.format(new Date(paymentDay)));
        result.put("arriveDay",format.format(new Date(arriveDay)));

        return result;
    }

    /**
     * 获取结算信息
     */
    private SettlementConfig getSettlementConfig(SettleDate settleDate) {
        InstNewInfoQueryFacadeImpl instNewInfoQueryFacade = new InstNewInfoQueryFacadeImpl();
        DslEntity2SettleInfoVoConvertor dslEntity2SettleInfoVoConvertor = new DslEntity2SettleInfoVoConvertor();
        instNewInfoQueryFacade.setDslEntity2SettleInfoVoConvertor(dslEntity2SettleInfoVoConvertor);

        SettlementConfig settlementConfig = new SettlementConfig();
        List<SettleDate> settleDates = Collections.singletonList(settleDate);
        settlementConfig.setSettleDates(settleDates);
        settleInfoConvertor.convertSettleInfo(settlementConfig);
        return settlementConfig;
    }

}
