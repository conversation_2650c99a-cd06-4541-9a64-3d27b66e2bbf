package com.payermax.channel.inst.center.service.convertor.calculator;

import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundSignTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.enums.CycleUnit;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import org.springframework.stereotype.Service;

/**
 * 支持 D+1/D+N/D-N 这种简单模式
 */
@Service
public class DayRoundCalculator extends AbstractRoundCalculator {

    @Override
    public SettleInfoVo calculate(DSLEntity dslEntity, Integer cycleStart, Integer cycleEnd) {
        SettleInfoVo settleInfoVo = new SettleInfoVo();
        settleInfoVo.setSettleCycleUnit(CycleUnit.DAY.getUnit());
        int roundRelativeOffset = dslEntity.getRoundRelativeOffset();
        RoundSignTypeEnum signTypeEnum = dslEntity.getRoundRelativeSignType();
        if (RoundSignTypeEnum.MINUS == signTypeEnum) {
            roundRelativeOffset = -roundRelativeOffset;
        }
        settleInfoVo.setSettleCycle(String.valueOf(roundRelativeOffset));
        return settleInfoVo;
    }

    @Override
    public RoundTypeEnum getRoundType() {
        return RoundTypeEnum.DAY;
    }
}
