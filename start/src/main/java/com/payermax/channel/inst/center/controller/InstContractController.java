package com.payermax.channel.inst.center.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductParseContext;
import com.payermax.channel.inst.center.app.request.*;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportResponseDTO;
import com.payermax.channel.inst.center.app.response.InstContractInitResponseVo;
import com.payermax.channel.inst.center.app.response.InstProductItemVO;
import com.payermax.channel.inst.center.app.service.InstBankAccountService;
import com.payermax.channel.inst.center.app.service.InstContractServiceV2;
import com.payermax.channel.inst.center.app.service.InstFundsAccountService;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.instcenter.ActionType;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.facade.api.InstNewInfoManagerFacade;
import com.payermax.channel.inst.center.facade.api.InstNewInfoQueryFacade;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.DSLTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.*;
import com.payermax.channel.inst.center.facade.request.contract.config.mapping.InstChannelMerchantCodeRequest;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.channel.inst.center.facade.response.InstContractBatchVerifyResponse;
import com.payermax.channel.inst.center.facade.response.contract.*;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.ChannelSettleInfoCalculateUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractFeeItemMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.FundingChannelProductInfo;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBankAccountPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAccountPo;
import com.payermax.channel.inst.center.service.InstContractProductManageService;
import com.payermax.channel.inst.center.task.InstCodeSyncJobHandler;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Setter;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * InstContractController
 *
 * <AUTHOR>
 * @desc
 */
@Api(tags = "机构合同API")
@RestController
@RequestMapping("instCenter/contract")
@Setter
public class InstContractController {

    @Resource
    private InstContractServiceV2 instContractServiceV2;

    @Resource
    private InstNewInfoQueryFacade instNewInfoQueryFacade;

    @Resource
    private InstNewInfoManagerFacade instNewInfoManagerFacade;

    @Resource
    private InstCodeSyncJobHandler instCodeSyncJobHandler;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private InstContractFeeItemMapper instContractFeeItemMapper;

    @Resource
    InstFundsAccountService instFundsAccountService;

    @Resource
    InstContractProductManageService instContractProductManageService;

    @Resource
    InstBankAccountService instBankAccountService;


    @Deprecated
    @ApiOperation(value = "机构合同初始化", notes = "机构合同初始化")
    @PostMapping("init")
    @DigestLog(isRecord = true)
    public Result<InstContractImportResponseDTO> initInstContract(@RequestBody InstContractImportRequestDTO instContractInitRequest) {
        return ResultUtil.success(instContractServiceV2.initInstContract(instContractInitRequest));
    }

    @ApiOperation(value = "查询机构费用信息", notes = "查询机构费用信息")
    @PostMapping("query")
    @DigestLog(isRecord = true)
    public Result<InstInfoQueryResponse> query(@RequestBody InstInfoQueryRequest infoQueryRequest) {
        return instNewInfoQueryFacade.queryFeeInfo(infoQueryRequest);
    }

    @Deprecated
    @ApiOperation(value = "通过FEE_NO修改FEE", notes = "通过FEE_NO修改FEE")
    @PostMapping("feeFixByFeeItem")
    @DigestLog(isRecord = true)
    public Result<Integer> feeFixByFeeItem(@RequestParam(name = "feeItemNo") String feeItemNo, @RequestBody Map<FeeTypeEnum, InstFeeConfig> feeConfigs) {
        AssertUtil.isTrue(StringUtil.isNotEmpty(feeItemNo), "ERROR", "feeItemNo is empty");
        AssertUtil.isTrue(!CollectionUtils.isEmpty(feeConfigs), "ERROR", "feeConfigs is empty");
        InstContractFeeItemPO instContractFeeItemPO = new InstContractFeeItemPO();
        instContractFeeItemPO.setInstContractFeeItemNo(feeItemNo);
        instContractFeeItemPO.setFeeConfig(JSON.toJSONString(feeConfigs));
        UpdateWrapper<InstContractFeeItemPO> wrapper = new UpdateWrapper<>();
        wrapper.eq("inst_contract_fee_item_no", feeItemNo);
        int num = instContractFeeItemMapper.update(instContractFeeItemPO, wrapper);
        return ResultUtil.success(num);
    }

    @Deprecated
    @ApiOperation(value = "通过ORIGIN_PRODUCT修改FEE", notes = "通过ORIGIN_PRODUCT修改FEE")
    @PostMapping("feeFixByOriginProduct")
    @DigestLog(isRecord = true)
    public Result<Integer> feeFixByOriginProduct(@RequestParam(name = "originProductNo") String originProductNo, @RequestBody Map<FeeTypeEnum, InstFeeConfig> feeConfigs) {
        AssertUtil.isTrue(StringUtil.isNotEmpty(originProductNo), "ERROR", "originProductNo is empty");
        AssertUtil.isTrue(!CollectionUtils.isEmpty(feeConfigs), "ERROR", "feeConfigs is empty");
        InstContractFeeItemPO instContractFeeItemPO = new InstContractFeeItemPO();
        instContractFeeItemPO.setFeeConfig(JSON.toJSONString(feeConfigs));
        UpdateWrapper<InstContractFeeItemPO> wrapper = new UpdateWrapper<>();
        wrapper.eq("inst_origin_product_no", originProductNo);
        int num = instContractFeeItemMapper.update(instContractFeeItemPO, wrapper);
        return ResultUtil.success(num);
    }


    @ApiOperation(value = "查询机构费用信息", notes = "查询机构费用信息")
    @PostMapping("queryFx")
    @DigestLog(isRecord = true)
    public Result<InstInfoQueryResponse> queryFx(@RequestBody InstInfoQueryRequest infoQueryRequest) {
        return instNewInfoQueryFacade.querySettleAndFxInfo(infoQueryRequest);
    }


    @ApiOperation(value = "查询机构费用信息", notes = "查询机构费用信息")
    @PostMapping("queryAccumulation")
    @DigestLog(isRecord = true)
    public Result<List<AccumulationResponse>> queryAccumulationInfo() {
        return instNewInfoQueryFacade.queryAccumulationInfo();
    }


    @ApiOperation(value = "计算费用信息", notes = "计算费用信息")
    @PostMapping("calculate")
    @DigestLog(isRecord = true)
    public Result<Long> calculate(@RequestParam(name = "date") long date, @RequestBody SettleInfoVo settleInfoVo) {
        return ResultUtil.success(ChannelSettleInfoCalculateUtil.calculateExchangeDate("SettlementDay"
                , settleInfoVo, date));
    }

    @ApiOperation(value = "机构信息补充", notes = "机构信息补充")
    @PostMapping("fill")
    @DigestLog(isRecord = true)
    public Result<String> fill(@RequestBody InstChannelMerchantCodeRequest request) {
        return instNewInfoManagerFacade.fillInstChannelMerchantCode(request);
    }

    @ApiOperation(value = "机构信息同步", notes = "机构信息同步")
    @PostMapping("sync")
    @DigestLog(isRecord = true)
    public Result<String> sync() {
        instCodeSyncJobHandler.execute("");
        return ResultUtil.success("SUCCESS");
    }

    @ApiOperation(value = "查看出错情况", notes = "查看出错情况")
    @GetMapping("log")
    @DigestLog(isRecord = true)
    public Result<JSONObject> log(@RequestParam(name = "aa", required = false) String aa) {
        Set<String> keys = stringRedisTemplate.keys(CommonConstants.INST_NEW_QUERY_ERROR + "*");
        JSONObject object = new JSONObject();
        if (CollectionUtils.isNotEmpty(keys)) {
            for (String key : keys) {
                object.put(key.replace("INST_NEW_QUERY_ERROR:", ""), stringRedisTemplate.opsForHash().entries(key));
            }
        }
        return ResultUtil.success(object);
    }

    /**
     * 机构合同初始化V2
     */
    @ApiOperation(value = "机构合同初始化V2", notes = "机构合同初始化V2")
    @PostMapping("initV2")
    @DigestLog(isRecord = true)
    public Result<InstContractImportDTO> initInstContractV2(@Validated @RequestBody InstContractParseRequestDTO instContractInitReq) {
        return ResultUtil.success(instContractServiceV2.instContractParse(instContractInitReq));
    }

    /**
     * 机构合同初始化存入草稿
     */
    @ApiOperation(value = "机构合同初始化存入草稿", notes = "机构合同初始化存入草稿")
    @PostMapping("saveDraftList")
    @DigestLog(isRecord = true)
    public Result<InstContractInitResponseVo> initInstContractSaveDraft(@Validated @RequestBody InstContractInitRequestDTO request) {
        return ResultUtil.success(instContractServiceV2.instContractListSave(request));
    }


    /**
     * 根据shareId查询有权限的草稿列表
     *
     * @param shareId
     */
    @PostMapping("queryDraftListByShareID")
    @DigestLog(isRecord = true)
    public Result<List<InstProductItemVO>> queryDraftListByShareId(@RequestHeader("shareId") String shareId) {
        return ResultUtil.success(instContractServiceV2.queryDraftList(shareId));
    }

    /**
     * 根据机构、主体、状态、录入时间查询草稿权限
     *
     * @param request
     * @return
     */
    @PostMapping("queryDraftListByConditions")
    @DigestLog(isRecord = true)
    public Result<List<InstProductItemVO>> queryDraftListByConditions(@RequestBody InstProductQueryRequestDTO request) {
        return ResultUtil.success(instContractServiceV2.queryDraftByConditions(request));
    }

    /**
     * 根据草稿 ID 查询草稿内容
     *
     * @param param
     */
    @PostMapping("queryDraftByDraftId")
    @DigestLog(isRecord = true)
    public Result<InstContractDraft> queryDraftByDraftId(@RequestBody Map<String, String> param) {
        return ResultUtil.success(instContractServiceV2.queryDraftByDraftId(param.get("draftId")));
    }

    /**
     * 根据 草稿ID 删除草稿
     *
     * @param body
     */
    @PostMapping("delDraftById")
    @DigestLog(isRecord = true)
    public Result<Boolean> delDraftById(@RequestBody Map<String, String> body) {
        return ResultUtil.success(instContractServiceV2.delDraftById(body.get("draftId")));
    }

    /**
     * 根据支付类型查询支付方式列表
     *
     * @param paymentType 支付类型
     */
    @PostMapping("queryPaymentMethodByType")
    @DigestLog(isRecord = true)
    public Result<List<FundingChannelProductInfo>> queryPaymentMethodByType(@RequestParam String paymentType) {
        return ResultUtil.success(instContractServiceV2.queryPaymentMethodByType(paymentType));
    }

    /**
     * 保存结算信息
     *
     * @param request 结算信息
     */
    @PostMapping("saveSettlementInfo")
    @DigestLog(isRecord = true)
    public Result<Boolean> saveSettlementInfo(@Validated @RequestBody InstContractStandardRequestDTO.StandardSettleInfo request) {
        return ResultUtil.success(instContractServiceV2.saveSettlementInfoByDraftId(request));
    }


    /**
     * 保存产品标准化信息
     *
     * @param request 产品标准化信息
     */
    @PostMapping("saveStandardProductInfo")
    @DigestLog(isRecord = true)
    public Result<Boolean> saveStandardProductInfo(@Validated @RequestBody InstContractStandardRequestDTO.StandardProductInfo request) {
        return ResultUtil.success(instContractServiceV2.saveStandardProductInfoByDraftId(request));
    }


    /**
     * 保存换汇标准化信息
     *
     * @param request 换汇标准化信息
     */
    @PostMapping("saveStandardFxInfo")
    @DigestLog(isRecord = true)
    public Result<Boolean> saveStandardFxInfo(@Validated @RequestBody InstContractStandardRequestDTO.StandardFxInfo request) {
        return ResultUtil.success(instContractServiceV2.saveStandardFxInfo(request));
    }


    /**
     * 根据账户别名进行模糊搜索
     *
     * @param accountAlias 账户别名
     */
    @PostMapping("queryDetailByAccountAlias")
    @DigestLog(isRecord = true)
    public Result<List<InstFundsAccountPo>> queryDetailByAccountAlias(@RequestParam String accountAlias) {
        return ResultUtil.success(instFundsAccountService.queryDetailByAccountAlias(accountAlias));
    }


    /**
     * 根据账户别名模糊搜索银行账户
     *
     * @param accountAlias 账户名称
     */
    @PostMapping("queryBankByAccountAlias")
    @DigestLog(isRecord = true)
    public Result<List<InstBankAccountPO>> queryBankByAccountAlias(@RequestParam String accountAlias) {
        return ResultUtil.success(instBankAccountService.queryBankByAccountAlias(accountAlias));
    }


    /**
     * 表达式测试
     *
     * @param body
     */
    @PostMapping("dslTest")
    @DigestLog(isRecord = true)
    public Result<Object> dslTest(@RequestBody Map<String, String> body) {
        return ResultUtil.success(DSLTypeEnum.mateDslType(body.get("expression")));
    }

    /**
     * 根据传入结算信息计算结算日期
     *
     * @param body
     */
    @PostMapping("settleDateCalculate")
    @DigestLog(isRecord = true)
    public Result<Map<String, String>> settleDateCalculate(@RequestBody Map<String, Object> body) throws ParseException {
        SettleDate settleInfo = JSON.parseObject(JSON.toJSONString(body.get("settleInfo")), SettleDate.class);
        String dateStr = (String) body.get("date");
        return ResultUtil.success(instContractProductManageService.settleDateCalculate(settleInfo, dateStr));
    }


    /**
     * 机构产品提交复核
     */
    @PostMapping("productToAudit")
    @DigestLog(isRecord = true)
    public Result<Boolean> productToAudit(@RequestBody Map<String, String> body) {
        return ResultUtil.success(instContractServiceV2.productDraftStatusChange(body.get("draftId"), ActionType.AUDITING.name()));
    }

    /**
     * 机构产品复核通过
     */
    @PostMapping("productAuditAgree")
    @DigestLog(isRecord = true)
    public Result<InstProductParseContext> productAuditAgree(@RequestBody Map<String, String> body) {
        return ResultUtil.success(instContractServiceV2.productAuditSubmit(body.get("draftId")));
    }

    /**
     * 机构产品复核驳回
     */
    @PostMapping("productAuditReturn")
    @DigestLog(isRecord = true)
    public Result<Boolean> productAuditReturn(@RequestBody Map<String, String> body) {
        return ResultUtil.success(instContractServiceV2.productDraftStatusChange(body.get("draftId"), ActionType.AUDIT_RETURN.name()));
    }

    /**
     * 机构信息完整性校验
     */
    @ApiOperation(value = "查询机构费用信息", notes = "查询机构费用信息")
    @PostMapping("instInfoVerify")
    @DigestLog(isRecord = true)
    public Result<Boolean> instInfoVerify(@RequestBody InstInfoQueryRequest infoQueryRequest) {
        return instNewInfoQueryFacade.instInfoVerify(infoQueryRequest);
    }


    /**
     * 机构信息完整性批量校验
     */
    @PostMapping("instContractBatchVerify")
    public Result<InstContractBatchVerifyResponse> instContractBatchVerify(@RequestBody InstContractBatchVerifyRequest request) {
        return instNewInfoQueryFacade.instContractBatchVerify(request);
    }

    /**
     * 风控费用查询
     */
    @PostMapping("queryRiskFeeInfo")
    public Result<InstRiskFeeInfoResponse> queryRiskFeeInfo(@RequestBody InstRiskFeeQueryRequest request) {
        return instNewInfoQueryFacade.queryRiskFeeInfo(request);
    }

    /**
     * 争议费用查询
     */
    @PostMapping("queryDisputeFeeInfo")
    public Result<InstDisputeFeeResponse> queryDisputeFeeInfo(@RequestBody InstInfoQueryRequest request) {
        return instNewInfoQueryFacade.queryDisputeFeeInfo(request);
    }

    /**
     * 技术服务费查询
     */
    @PostMapping("queryTechnicalServiceFeeInfo")
    public Result<InstTechnicalServiceFeeResponse> queryTechnicalServiceFeeInfo(@RequestBody InstTechnicalServiceFeeQueryRequest request) {
        return instNewInfoQueryFacade.queryTechnicalServiceFeeInfo(request);
    }

    /**
     * 技术服务费查询
     */
    @PostMapping("queryAccountingType")
    public Result<InstAccountingTypeResponse> queryAccountingType(@RequestBody InstAccountingTypeQueryRequest request) {
        return instNewInfoQueryFacade.queryAccountingType(request);
    }

    /**
     * VA 费用查询
     */
    @PostMapping("queryVirtualAccountFeeInfo")
    public Result<InstVirtualAccountFeeResponse> queryVirtualAccountFeeInfo(@RequestBody InstInfoQueryRequest request) {
        return instNewInfoQueryFacade.queryVirtualAccountFeeInfo(request);
    }

}
