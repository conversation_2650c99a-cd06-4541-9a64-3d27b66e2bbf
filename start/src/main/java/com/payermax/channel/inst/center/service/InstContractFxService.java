package com.payermax.channel.inst.center.service;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractProductAssembler;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractBaseFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractFeeFormMsgDTO;
import com.payermax.channel.inst.center.app.manage.contract.InstContractBusinessValidator;
import com.payermax.channel.inst.center.app.manage.contract.processHandler.InstContractFxBatchSyncHandler;
import com.payermax.channel.inst.center.app.request.InstContractFxBatchModifiedReqDTO;
import com.payermax.channel.inst.center.app.request.InstContractProductQueryRequest;
import com.payermax.channel.inst.center.app.response.InstContractFxQueryVO;
import com.payermax.channel.inst.center.app.service.InstContractFeeWorkflowService;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.AsyncTaskUtils;
import com.payermax.channel.inst.center.common.utils.DiffUtil;
import com.payermax.channel.inst.center.common.utils.ListUtils;
import com.payermax.channel.inst.center.facade.request.contract.InstContractFxBatchSyncRequest;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractOriginProductPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractFeeItemRepository;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/15
 * @DESC 机构合约 FX 管理服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InstContractFxService {

    private final InstContractProductService contractProductService;
    private final InstContractFeeItemRepository feeItemRepository;
    private final InstContractFeeWorkflowService feeWorkflowService;
    private final InstContractProductAssembler instContractProductAssembler;
    private final AsyncTaskUtils asyncTaskUtils;
    private final InstContractFxBatchSyncHandler fxBatchSyncHandler;
    private final InstContractBusinessValidator businessValidator;



    /**
     * 查询 FX 信息列表
     */
    public List<InstContractFxQueryVO> queryFxList(InstContractProductQueryRequest request){

        // 1. 根据参数查询并构造原始产品列表
        List<InstContractOriginProductPO> originProductList = contractProductService.composeOriginProductList(request);

        originProductList = contractProductService.filterOriginProductList(request, originProductList);
        Map<String, InstContractOriginProductPO> originProductMap = originProductList.stream().collect(Collectors.toMap(InstContractOriginProductPO::getInstOriginProductNo, product -> product));

        // 2. 获取 feeItem 列表
        List<InstContractFeeItemPO> feeItemList = originProductList.stream().filter(product -> Objects.nonNull(product.getContractFeeItems())).flatMap(product -> product.getContractFeeItems().stream()).collect(Collectors.toList());

        // 3. 币种过滤
        if(StringUtil.isNotBlank(request.getPayCurrency())){
            feeItemList = feeItemList.stream().filter(feeItem -> feeItem.getPayCurrency().equals(request.getPayCurrency())).collect(Collectors.toList());
        }

        // 4. 数据处理并返回
        List<InstContractFxQueryVO.FxItem> fxItems = feeItemList.stream()
                .map(po -> instContractProductAssembler.feeItem2FxQueryVO(po, originProductMap.get(po.getInstOriginProductNo()).getContractBaseInfo()))
                // 填充标准化信息
                .peek(vo -> vo.setStandardProductMsgList(contractProductService.getMsgFromOriginMap(originProductMap, vo.getInstOriginProductNo())))
                .collect(Collectors.toList());
        // 根据机构、主体、币种、产品类型分组
        Map<String, List<InstContractFxQueryVO.FxItem>> fxItemMap = fxItems.stream().collect(Collectors.groupingBy(item ->
                ListUtils.quickBuildUnderlineKey(item.getInstCode(), item.getContractEntity(), item.getPayCurrency(), item.getInstProductType()), Collectors.toList()));

        return fxItemMap.keySet().stream().map(key -> {
            InstContractFxQueryVO vo = new InstContractFxQueryVO();
            fxItemMap.get(key).stream().findFirst().ifPresent( item -> instContractProductAssembler.fxItem2QueryVO(vo,item));
            vo.setChildren(fxItemMap.get(key));
            return vo;
        }).sorted(Comparator.comparing(InstContractFxQueryVO::getInstCode)).collect(Collectors.toList());
    }

    /**
     * 批量修改 FX 信息
     */
    public Boolean fxBatchModified(String shareId, InstContractFxBatchModifiedReqDTO request){
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(request.getFeeItemNoList()), "ERROR", "feeItemNoList is empty");
        AssertUtil.isTrue(ObjectUtils.isNotEmpty(request.getFxConfig()), "ERROR", "fxConfig is empty");
        // 查询原始数据
        List<InstContractFeeItemPO> originItemList = feeItemRepository.queryByNoList(request.getFeeItemNoList());

        // FX 修改前检查
        String businessKey = businessValidator.fxBatchModifyPreCheck(originItemList);

        // 组装费用数据
        List<InstContractFeeItemPO> modifiedItemList = originItemList.stream()
                .map(instContractProductAssembler::feePoDeepCopy)
                .peek(item -> instContractProductAssembler.fx2FeeItemPoAllowEmpty(item, request.getFxConfig()))
                .collect(Collectors.toList());

        Map<String, InstContractFeeItemPO> originItemMap = originItemList.stream().collect(Collectors.toMap(InstContractFeeItemPO::getInstContractFeeItemNo, v -> v));
        Map<String, InstContractFeeItemPO> modifiedItemMap = modifiedItemList.stream().collect(Collectors.toMap(InstContractFeeItemPO::getInstContractFeeItemNo, v -> v));

        // 组装审批信息
        List<InstContractFeeFormMsgDTO> formMsgList = request.getFeeItemNoList().stream().map(no -> {
            Map<String, Object> diffRes = new HashMap<>(4);
            diffRes.putAll(DiffUtil.diff2Map(JSON.toJSONString(originItemMap.get(no)), JSON.toJSONString(modifiedItemMap.get(no))));
            return composeFxModifiedProcessFormMsg(originItemMap.get(no).getInstOriginProductNo(), originItemMap.get(no), diffRes);
        }).collect(Collectors.toList());

        // 发起审批
        return feeWorkflowService.fxBatchModifiedProcessStart(shareId, formMsgList, originItemList, modifiedItemList,request.getFeeItemNoList(), businessKey);
    }


    /**
     * 渠道加点批量变更受理
     */
    public Boolean acceptChannelFxBatchSync(InstContractFxBatchSyncRequest request){
        log.info("开始受理FX加点批量同步");
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(request.getFxConfigSyncList()), ErrorCodeEnum.INST_CONTRACT_FX_VALIDATE_ERROR.getCode(), "fxConfigSyncList is empty");
        long transactionTime = System.currentTimeMillis();
        log.info("channelFxBatchSync request:{}", JSON.toJSONString(request));

        // 受理
        String draftId = fxBatchSyncHandler.acceptFxBatchSync(request, transactionTime);

        // 事务变更-异步
        log.info("异步执行 FX 批量同步");
        asyncTaskUtils.instCenterAsyncTaskExecutor("asyncChannelFxBatchSync", draftId, fxBatchSyncHandler::fxBatchSyncExecute);

        // 返回受理结果
        log.info("FX 加点批量同步受理成功");
        return Boolean.TRUE;
    }

    /**
     * 根据 feeItem 构造FX审批表单信息
     */
    private InstContractFeeFormMsgDTO composeFxModifiedProcessFormMsg(String originProductNo, InstContractFeeItemPO originData, Map<String, Object> diffRes) {
        InstContractBaseFormMsgDTO baseFormMsg = contractProductService.composeBaseModifiedProcessFormMsg(originProductNo);
        InstContractFeeFormMsgDTO formMsg = instContractProductAssembler.baseFormMsg2FeeFormMsg(baseFormMsg);
        formMsg.setFeeItemNo(originData.getInstContractFeeItemNo());
        formMsg.setPayCurrency(originData.getPayCurrency());
        formMsg.setOriginData(originData);
        formMsg.setDiffMsg(diffRes);
        return formMsg;
    }

}
