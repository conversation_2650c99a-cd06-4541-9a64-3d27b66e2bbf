package com.payermax.channel.inst.center.facade;

import com.payermax.channel.inst.center.app.manage.fundsAgreement.InstFundsAgreementQueryService;
import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.channel.inst.center.facade.api.InstFundsAgreementQueryFacade;
import com.payermax.channel.inst.center.facade.request.fundsAgreement.FundsAgreementQueryRequest;
import com.payermax.channel.inst.center.facade.response.fundsAgreement.FundsAgreementQueryResponse;
import com.payermax.common.lang.model.dto.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/7/5
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DubboService(version = "${dubbo.provider.version}", interfaceClass = InstFundsAgreementQueryFacade.class)
public class InstFundsAgreementQueryFacadeImpl implements InstFundsAgreementQueryFacade {

    private final InstFundsAgreementQueryService fundsAgreementQueryService;


    @Override
    public Result<FundsAgreementQueryResponse> queryByFundsAgreementNo(FundsAgreementQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() -> fundsAgreementQueryService.queryByFundsAgreementNo(request));
    }
}
