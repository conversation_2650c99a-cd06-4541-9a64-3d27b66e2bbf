package com.payermax.channel.inst.center.service.convertor.calculator;

import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundSignTypeEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.enums.CycleUnit;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import org.springframework.stereotype.Service;

/**
 * 例：
 * 1） M.1 ~ M.-1 | (M+1).2
 * 2) M.15 ~ (M+1).14 | (M+1).16 --
 */
@Service
public class MonthRoundCalculator extends AbstractRoundCalculator {

    @Override
    public SettleInfoVo calculate(DSLEntity dslEntity, Integer cycleStart, Integer cycleEnd) {
        SettleInfoVo settleInfoVo = new SettleInfoVo();
        settleInfoVo.setSettleCycleUnit(CycleUnit.MONTH.getUnit());
        settleInfoVo.setCycleStart(cycleStart);
        settleInfoVo.setCycleEnd(cycleEnd);
        // 相对部分--例如(W+1).2中的 +1 即为相对
        if (dslEntity.getRoundRelativeOffset() != null) {
            int roundRelativeOffset = dslEntity.getRoundRelativeOffset();
            RoundSignTypeEnum signTypeEnum = dslEntity.getRoundRelativeSignType();
            if (RoundSignTypeEnum.MINUS == signTypeEnum) {
                roundRelativeOffset = -roundRelativeOffset;
            }
            settleInfoVo.setSettleCycle(String.valueOf(roundRelativeOffset));
        } else {
            settleInfoVo.setSettleCycle(String.valueOf(0));
        }

        // 绝对部分--例如(M+1).2中的 .2 即为绝对
        int roundDefiniteOffset = dslEntity.getRoundDefiniteOffset();
        RoundSignTypeEnum signTypeEnum2 = dslEntity.getRoundDefiniteSignType();
        if (RoundSignTypeEnum.MINUS == signTypeEnum2) {
            roundDefiniteOffset = -roundDefiniteOffset;
        }
        settleInfoVo.setSettleCycleOtherLimit(String.valueOf(roundDefiniteOffset));
        return settleInfoVo;
    }

    @Override
    public RoundTypeEnum getRoundType() {
        return RoundTypeEnum.MONTH;
    }
}
