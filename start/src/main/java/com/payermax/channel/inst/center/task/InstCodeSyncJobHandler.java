package com.payermax.channel.inst.center.task;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfoEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBaseInfoDao;
import com.payermax.channel.inst.center.infrastructure.mapper.InstBrandDao;
import com.payermax.channel.inst.center.infrastructure.repository.repo.ods.OdsRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2022-10-21 4:39 PM
 */
@Component
@Slf4j
@Setter
@JobHandler(value = "instCodeSyncJobHandler")
public class InstCodeSyncJobHandler extends IJobHandler {

    @Resource
    private OdsRepository odsRepository;


    @Resource
    private InstBaseInfoDao instBaseInfoDao;

    @Resource
    private InstBrandDao instBrandDao;

    @NacosValue("${allow.sync.env:local,dev,test,staging,uat")
    private String allowEnv;

    @NacosValue("${sentry.environment}")
    private String env;

    @Override
    public ReturnT<String> execute(String param) {
        AssertUtil.isTrue(allowEnv.contains(env), "", "不允许当前环境执行:" + env);
        List<InstBaseInfoEntity> instBaseInfoEntities = odsRepository.queryAllInstFormOds();
        List<InstBrandEntity> instBrandEntities = odsRepository.queryAllInstBrandFormOds();
        instBaseInfoEntities.forEach(item -> {
            try {
                instBaseInfoDao.insert(item);
                log.info("sync success!");
            } catch (Exception e) {
                log.warn("sync fail!:{}", e.getMessage());
            }
        });

        instBrandEntities.forEach(item -> {
            try {
                instBrandDao.insertDirect(item);
                log.info("sync brand success!");
            } catch (Exception e) {
                log.warn("sync fail!:{}", e.getMessage());
            }
        });
        return ReturnT.SUCCESS;
    }
}
