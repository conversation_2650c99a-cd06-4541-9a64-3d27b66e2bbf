package com.payermax.channel.inst.center.service.matcher;

import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/13
 * @DESC
 */
@Service
@DependsOn("paymentCcyMatcher")
public class CardTypeMatcher extends AbstractContractMatcher {
    @Override
    protected boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        // 所有的记录的 cardType 都为空时，直接放行
        return feeItems.stream().allMatch( item -> StringUtil.isBlank(item.getCardType()));
    }

    @Override
    protected List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String cardType = funnelMatchItem.getCardType();
        return feeItems.stream()
                .filter(item -> StringUtil.isNotBlank(cardType)
                        && cardType.equalsIgnoreCase(item.getCardType()))
                .collect(Collectors.toList());
    }

    @Override
    protected List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        return feeItems.stream()
                .filter(item -> StringUtil.isBlank(item.getCardType()))
                .collect(Collectors.toList());
    }
}
