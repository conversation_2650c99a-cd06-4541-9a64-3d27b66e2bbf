package com.payermax.channel.inst.center.service.matcher;

import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/28
 * @DESC
 */
@Service
@DependsOn("paymentCcyMatcher")
public class CustomerTypeMatcher extends AbstractContractMatcher {

    /**
     * 判定漏斗是否直接放行
     */
    @Override
    protected boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        // 所有的记录的 customerType 都为空时，直接放行
        return feeItems.stream().allMatch( item -> StringUtil.isBlank(item.getCustomerType()));
    }

    /**
     * 精准匹配
     */
    @Override
    protected List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String customerType = funnelMatchItem.getCustomerType();
        return feeItems.stream()
                .filter(item -> StringUtil.isNotBlank(customerType)
                        && customerType.equalsIgnoreCase(item.getCustomerType()))
                .collect(Collectors.toList());
    }

    /**
     * 模糊匹配
     */
    @Override
    protected List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        return feeItems.stream()
                .filter(item -> StringUtil.isBlank(item.getCustomerType()))
                .collect(Collectors.toList());
    }
}
