package com.payermax.channel.inst.center.service.finder;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractStandardProduct;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.enums.contract.PaymentBusinessTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstTechnicalServiceFeeQueryRequest;
import com.payermax.channel.inst.center.facade.response.contract.InstTechnicalServiceFeeResponse;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.AssertUtil;
import com.ushareit.fintech.common.model.constants.CommonConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC 技术服务费查询器
 */
@Slf4j
@Component
public class InstTechnicalServiceContractFinder extends AbstractInstContractInfoFinder {


    @PostConstruct
    public void init() {
        registryStrategy(ContractBizTypeEnum.TS, this);
    }


    @Override
    protected void preProcessor(InstContractQueryContext context) {
        setDirectQueryAndCheck(context);
    }

    @Override
    protected void paramCheck(InstContractQueryContext context) {

        InstTechnicalServiceFeeQueryRequest techServiceReq = requestConvert(context.getRequest());
        InstInfoQueryRequest request = context.getRequest();
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPaymentMethodType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付方式类型不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPayCurrency()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付币种不能为空");
        AssertUtil.isTrue(ObjectUtils.isNotNull(request.getTransactionTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "交易时间不能为空");
        AssertUtil.isTrue(EnumUtils.isValidEnum(PaymentBusinessTypeEnum.class, techServiceReq.getPaymentBusinessType()) , ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付业务类型必须为枚举值");
        AssertUtil.isTrue( StringUtils.isNotBlank(request.getTargetOrg()) || StringUtils.isNotBlank(request.getCardOrg() ),
                ErrorCodeEnum.PARAMETER_INVALID.getCode(), "目标机构和卡组不能全部为空");

    }

    @Override
    protected InstTechnicalServiceFeeResponse composeResponse(InstContractQueryContext context) {

        InstTechnicalServiceFeeQueryRequest request = requestConvert(context.getRequest());
        InstTechnicalServiceFeeResponse response = instContractRequestAssembler.convertReq2Resp(request);
        InstContractFeeItem feeItem = context.getFeeItem();
        InstContractSettlementItem settleItem = context.getSettleItem();
        AssertUtil.isTrue(Objects.nonNull(feeItem), "Error", "feeItem is null!");

        // 费用信息
        response.setFeeConfigMap(feeInfoConvertor.feeConfigConvert(feeItem));

        // 结算信息
        if(Objects.nonNull(settleItem)){
            response.setSettlementConfig(instContractRequestAssembler.convertInstSettle2Response(settleItem));
            settleInfoConvertor.convertSettleInfo(response.getSettlementConfig());
        }

        // 换汇信息
        response.setFxConfig(instContractRequestAssembler.convertInstFx2Response(feeItem));

        // 技术服务费无扣款信息
        response.setDeductConfig(null);


        // 默认填充
        feeInfoConvertor.responseDefaultFill(response, context);
        return response;
    }



    /**
     * 技术服务费产品查询需要特殊实现
     */
    @Override
    protected InstContractStandardProduct queryStandardProduct(InstContractQueryContext context, List<InstContractStandardProduct> standardProducts, String paymentMethodType, String targetOrg, String cardOrg) {

        // 技术服务费，支付方式类型 = 业务类型 + 支付方式类型
        InstTechnicalServiceFeeQueryRequest techServiceReq = (InstTechnicalServiceFeeQueryRequest)context.getRequest();
        String newPaymentMethodType = String.join(CommonConstants.SYMBOL_UNDERLINE, techServiceReq.getPaymentBusinessType(), paymentMethodType);

        return super.queryStandardProduct(context, standardProducts, newPaymentMethodType, targetOrg, cardOrg);
    }

    private InstTechnicalServiceFeeQueryRequest requestConvert(InstInfoQueryRequest request){
        return (InstTechnicalServiceFeeQueryRequest)request;
    }

}
