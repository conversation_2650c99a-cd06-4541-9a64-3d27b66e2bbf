package com.payermax.channel.inst.center.config;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> at 2022/9/29 10:52
 **/
@Configuration
public class FeignAutoConfig {

    private int connectTimeOut = 12;

    private int socketTimeOut = 12;

    @Bean
    public Request.Options options() {
        return new Request.Options(connectTimeOut * 1000, TimeUnit.MILLISECONDS, socketTimeOut * 1000, TimeUnit.MILLISECONDS, true);
    }

}

