package com.payermax.channel.inst.center.service.finder.context;

import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractQueryModeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/5
 * @DESC 机构合约查询上下文
 */
@Data
public class InstContractQueryContext {


    /**
     * 合约查询模式
     */
    private ContractQueryModeEnum contractQueryMode;

    /**
     * 合约版本
     */
    private InstContractVersionInfo contractVersionInfo;

    /**
     * 原始产品
     */
    private InstContractOriginProduct originProduct;

    /**
     * 标准产品
     */
    private InstContractStandardProduct standardProduct;

    /**
     * 费用项
     */
    private InstContractFeeItem feeItem;

    /**
     * 结算信息
     */
    private InstContractSettlementItem settleItem;

    /**
     * 请求参数
     */
    private InstInfoQueryRequest request;

    /**
     * 响应参数
     */
    private InstInfoQueryResponse response;

}
