package com.payermax.channel.inst.center.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.valid.annon.impl.CompanyEntityValid;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.facade.api.InstNewInfoManagerFacade;
import com.payermax.channel.inst.center.facade.request.contract.InstContractFxBatchSyncRequest;
import com.payermax.channel.inst.center.facade.request.contract.config.mapping.InstChannelMerchantCodeRequest;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.entity.InstBaseInfo;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractMidMappingRepository;
import com.payermax.channel.inst.center.service.InstContractFxService;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 新版机构中心信息管理接口
 *
 * <AUTHOR> tracy
 * @version 2023-08-11 5:23 PM
 */
@DubboService
@Slf4j
public class InstNewInfoManagerFacadeImpl implements InstNewInfoManagerFacade {

    @Resource
    private InstContractMidMappingRepository instContractMidMappingRepository;

    @Resource
    private InstContractBaseInfoRepository instContractBaseInfoRepository;

    @Resource
    private ContractVersionPOAssembler contractVersionPOAssembler;

    @Resource
    private InstBaseInfoRepository instBaseInfoRepository;

    @Resource
    private CompanyEntityValid companyEntityValid;

    @Resource
    private InstContractFxService fxService;



    @Override
    public Result<String> fillInstChannelMerchantCode(InstChannelMerchantCodeRequest request) {

        // 前置校验
        preCheck(request);

        InstContractBaseInfoPO instContractBaseInfoPO = null;
        try {
            InstContractBaseInfoPO contractBaseInfoPO = contractVersionPOAssembler
                    .convertRequest2BaseInfoPO(request);
            contractBaseInfoPO.setStatus(ContractStatusEnum.ACTIVATED.name());
            instContractBaseInfoPO = instContractBaseInfoRepository.queryActiveBaseInfoByBusinessKey(contractBaseInfoPO);
        } catch (Exception e) {
            log.error("query contract error!", e);
        }

        InstContractMidMappingPO mappingPO = contractVersionPOAssembler.convertRequest2MidMappingPO(request);
        if (instContractBaseInfoPO != null) {
            mappingPO.setContractNo(instContractBaseInfoPO.getContractNo());
        }
        try {
            instContractMidMappingRepository.initInstContractMidMapping(mappingPO);
        } catch (DuplicateKeyException e) {
            log.warn("initInstContractMidMapping error DuplicateKey", e);
        }
        return ResultUtils.success("SUCCESS");
    }

    @Override
    public Result<Boolean> instContractFxBatchSync(InstContractFxBatchSyncRequest request) {
        return ExceptionUtils.commonTryCatch(() -> fxService.acceptChannelFxBatchSync(request));
    }

    /**
     * 前置检查
     */
    private void preCheck(InstChannelMerchantCodeRequest request) {

        AssertUtil.isTrue(EnumUtils.isValidEnum(ContractBizTypeEnum.class, request.getInstType()), ErrorCodeEnum.INST_CONTRACT_PRODUCT_TYPE_ERROR.getCode(), "机构产品类型错误，I/O");

        List<InstBaseInfo> instBaseInfos = instBaseInfoRepository.queryByInstCodeList(Collections.singletonList(request.getInstCode()), true);
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(instBaseInfos), ErrorCodeEnum.INST_INFO_QUERY_ERROR.getCode(), "机构不存在");

        AssertUtil.isTrue(StringUtils.isNotBlank(request.getEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体编码为空");
        Set<String> entitySet = companyEntityValid.queryData().stream().map(CompanyEntityValid.SimpleCompanyEntity::getEntityBizCode).collect(Collectors.toSet());
        AssertUtil.isTrue(entitySet.contains(request.getEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体编码不存在");

    }

}
