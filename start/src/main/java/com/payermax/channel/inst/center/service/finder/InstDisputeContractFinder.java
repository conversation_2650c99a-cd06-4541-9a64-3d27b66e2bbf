package com.payermax.channel.inst.center.service.finder;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.response.contract.InstDisputeFeeResponse;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/14
 * @DESC 争议费用查询
 */
@Slf4j
@Component
public class InstDisputeContractFinder extends AbstractInstContractInfoFinder {


    private static final String DISPUTE_PAYMENT_METHOD_TYPE = "DISPUTE";

    @PostConstruct
    public void init() {
        registryStrategy(ContractBizTypeEnum.DISP, this);
    }

    @Override
    protected void preProcessor(InstContractQueryContext context) {
        // 合约查询方式为直接查询
        setDirectQueryAndCheck(context);
        // 设置支付方式类型为 DISPUTE
        context.getRequest().setPaymentMethodType(DISPUTE_PAYMENT_METHOD_TYPE);
    }

    @Override
    protected void paramCheck(InstContractQueryContext context) {
        InstInfoQueryRequest request = context.getRequest();
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPaymentMethodType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付方式类型不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPayCurrency()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付币种不能为空");
        AssertUtil.isTrue(ObjectUtils.isNotNull(request.getTransactionTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "交易时间不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getCardOrg()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "卡组不能为空");

    }

    @Override
    protected void findSettle(InstContractQueryContext context) {
        // 争议 无结算信息
        context.setSettleItem(null);
    }

    @Override
    protected InstInfoQueryResponse composeResponse(InstContractQueryContext context) {

        InstInfoQueryRequest request = context.getRequest();
        InstDisputeFeeResponse response = instContractRequestAssembler.convertReq2DisputeResp(request);
        InstContractFeeItem feeItem = context.getFeeItem();
        AssertUtil.isTrue(Objects.nonNull(feeItem), "Error", "feeItem is null!");

        // 通用信息填充
        feeInfoConvertor.responseDefaultFill(response, context);

        // 费用信息
        response.setFeeConfigMap(feeInfoConvertor.feeConfigConvert(feeItem));
        // 换汇信息
        response.setFxConfig(instContractRequestAssembler.convertInstFx2Response(feeItem));

        // 争议 无税费、扣款信息、结算信息
        response.setTaxConfig(null);
        response.setDeductConfig(null);
        response.setSettlementConfig(null);
        return response;
    }
}
