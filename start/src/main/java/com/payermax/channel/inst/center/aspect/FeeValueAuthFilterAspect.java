package com.payermax.channel.inst.center.aspect;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.model.contract.desensitizer.FeeValueDesensitizeFilter;
import com.payermax.channel.inst.center.app.model.contract.desensitizer.SensitiveType;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemVO;
import com.payermax.channel.inst.center.app.response.InstContractSettlementVO;
import com.payermax.channel.inst.center.app.service.SysUserPermissionService;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/1/13
 * @DESC
 */
@Aspect
@Component
@Slf4j
public class FeeValueAuthFilterAspect {

    /**
     * 序列化 Filter
     */
    FeeValueDesensitizeFilter feeValueFilter = new FeeValueDesensitizeFilter(SensitiveType.FEE_VALUE);
    FeeValueDesensitizeFilter taxValueFilter = new FeeValueDesensitizeFilter(SensitiveType.TAX_VALUE);
    FeeValueDesensitizeFilter fxValueFilter = new FeeValueDesensitizeFilter(SensitiveType.FX_VALUE);
    FeeValueDesensitizeFilter allValueFilter = new FeeValueDesensitizeFilter(SensitiveType.ALL_VALUE);

    @Resource
    private SysUserPermissionService sysUserPermissionService;

    @Pointcut(value = "execution(* com.payermax.channel.inst.center.controller..*.*(..)) && @annotation(com.payermax.channel.inst.center.aspect.FeeValueAuthFilter)")
    public void pointcut() {
    }

    @Around(value = "pointcut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        // 线上环境请求时会自动带上 shareId
        // 1. 获取请求数据及返回数据
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        AtomicReference<String> shareId = new AtomicReference<>(null);
        Optional.ofNullable(request.getHeader("shareId")).ifPresent(item -> shareId.set(request.getHeader("shareId")));

        // 2. 判断注解的数据类型：fee、settle
        MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
        FeeValueAuthFilter annotation = signature.getMethod().getAnnotation(FeeValueAuthFilter.class);
        switch (annotation.feeType()){
            case FEE_ITEM_LIST:
                return feeItemListValueHandle(shareId.get(), proceedingJoinPoint.proceed());
            case FEE_ITEM:
                return feeItemValueHandle(shareId.get(), proceedingJoinPoint.proceed());
            case SETTLEMENT_ITEM_LIST:
                return settlementItemListValueHandle(shareId.get(), proceedingJoinPoint.proceed());
            case SETTLEMENT_ITEM:
                return settlementItemValueHandle(shareId.get(), proceedingJoinPoint.proceed());
            default:
                return ResultUtils.success(JSON.parseObject(JSON.toJSONString(proceedingJoinPoint.proceed(),allValueFilter), Objects.class));
        }
    }

    /**
     * 费用列表过滤逻辑
     */
    public Result<List<InstContractFeeItemVO>> feeItemListValueHandle(String shareId, Object object){
        if(object instanceof Result){
            return (Result)object;
        }
        List<InstContractFeeItemVO> originFeeItemList = (List<InstContractFeeItemVO>) object;
        if(StringUtils.isNotBlank(shareId)){
            // 2. 根据权限判断是否需要进行数据脱敏
            // FEE 权限
            if(!sysUserPermissionService.hasValuePermission(shareId, SensitiveType.FEE_VALUE)){
                originFeeItemList = JSON.parseArray(JSON.toJSONString(originFeeItemList, feeValueFilter), InstContractFeeItemVO.class);
            }
            // TAX 权限
            if(!sysUserPermissionService.hasValuePermission(shareId,SensitiveType.TAX_VALUE)){
                originFeeItemList = JSON.parseArray(JSON.toJSONString(originFeeItemList, taxValueFilter), InstContractFeeItemVO.class);
            }
            // FX 权限
            if(!sysUserPermissionService.hasValuePermission(shareId,SensitiveType.FX_VALUE)){
                originFeeItemList = JSON.parseArray(JSON.toJSONString(originFeeItemList, fxValueFilter), InstContractFeeItemVO.class);
            }
            // 返回数据
            return ResultUtils.success(originFeeItemList);
        }else{
            return ResultUtils.success(JSON.parseArray(JSON.toJSONString(originFeeItemList,allValueFilter), InstContractFeeItemVO.class));
        }
    }

    /**
     * 单条费用过滤逻辑
     */
    public Result<InstContractFeeItemVO> feeItemValueHandle(String shareId, Object object){
        if(object instanceof Result){
            return (Result)object;
        }
        InstContractFeeItemVO originFeeItem = (InstContractFeeItemVO) object;
        if(StringUtils.isNotBlank(shareId)){
            // 2. 根据权限判断是否需要进行数据脱敏
            // FEE 权限
            if(!sysUserPermissionService.hasValuePermission(shareId, SensitiveType.FEE_VALUE)){
                originFeeItem = JSON.parseObject(JSON.toJSONString(originFeeItem, feeValueFilter), InstContractFeeItemVO.class);
            }
            // TAX 权限
            if(!sysUserPermissionService.hasValuePermission(shareId,SensitiveType.TAX_VALUE)){
                originFeeItem = JSON.parseObject(JSON.toJSONString(originFeeItem, taxValueFilter), InstContractFeeItemVO.class);
            }
            // FX 权限
            if(!sysUserPermissionService.hasValuePermission(shareId,SensitiveType.FX_VALUE)){
                originFeeItem = JSON.parseObject(JSON.toJSONString(originFeeItem, fxValueFilter), InstContractFeeItemVO.class);
            }
            // 返回数据
            return ResultUtils.success(originFeeItem);
        }else{
            return ResultUtils.success(JSON.parseObject(JSON.toJSONString(originFeeItem,allValueFilter), InstContractFeeItemVO.class));
        }
    }

    /**
     * 结算费用列表过滤逻辑
     */
    public Result<List<InstContractSettlementVO>> settlementItemListValueHandle(String shareId, Object object){
        if(object instanceof Result){
            return (Result)object;
        }
        List<InstContractSettlementVO> originSettlementItemList = (List<InstContractSettlementVO>) object;
        if(StringUtils.isNotBlank(shareId)){
            // 2. 根据权限判断是否需要进行数据脱敏
            // 结算信息权限
            if(!sysUserPermissionService.hasValuePermission(shareId, SensitiveType.SETTLE_VALUE)){
                originSettlementItemList = JSON.parseArray(JSON.toJSONString(originSettlementItemList, feeValueFilter), InstContractSettlementVO.class);
            }
            return ResultUtils.success(originSettlementItemList);
        }else{
            return ResultUtils.success(JSON.parseArray(JSON.toJSONString(originSettlementItemList,allValueFilter), InstContractSettlementVO.class));
        }
    }

    /**
     * 单条结算费用过滤逻辑
     */
    public Result<InstContractSettlementVO> settlementItemValueHandle(String shareId, Object object){
        if(object instanceof Result){
            return (Result)object;
        }
        InstContractSettlementVO originSettlementItem = (InstContractSettlementVO) object;
        if(StringUtils.isNotBlank(shareId)){
            // 2. 根据权限判断是否需要进行数据脱敏
            // FEE 权限
            if(!sysUserPermissionService.hasValuePermission(shareId, SensitiveType.SETTLE_VALUE)){
                originSettlementItem = JSON.parseObject(JSON.toJSONString(originSettlementItem, feeValueFilter), InstContractSettlementVO.class);
            }
            return ResultUtils.success(originSettlementItem);
        }else{
            return ResultUtils.success(JSON.parseObject(JSON.toJSONString(originSettlementItem,allValueFilter), InstContractSettlementVO.class));
        }
    }

    }
