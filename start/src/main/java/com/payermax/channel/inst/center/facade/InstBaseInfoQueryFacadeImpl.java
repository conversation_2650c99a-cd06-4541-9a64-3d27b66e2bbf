package com.payermax.channel.inst.center.facade;

import cn.hutool.core.collection.CollectionUtil;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.assembler.ResDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstBankAccountManager;
import com.payermax.channel.inst.center.app.manage.InstBaseInfoManger;
import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.domain.subaccount.request.QueryInstBrandRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.QueryInstBrandResponseDO;
import com.payermax.channel.inst.center.facade.api.InstBaseInfoQueryFacade;
import com.payermax.channel.inst.center.facade.request.InstBankAccountRequest;
import com.payermax.channel.inst.center.facade.request.QueryBrandByChannelCodeRequest;
import com.payermax.channel.inst.center.facade.request.QueryInstBrandRequest;
import com.payermax.channel.inst.center.facade.response.InstBankAccountResponse;
import com.payermax.channel.inst.center.facade.response.InstBaseInfoWithBrandResponse;
import com.payermax.channel.inst.center.facade.response.QueryInstBrandResponse;
import com.payermax.channel.inst.center.validator.InstBaseInfoFacadeValidator;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/5/22  8:07 PM
 */
@DubboService(version = "${dubbo.provider.version}", interfaceClass = InstBaseInfoQueryFacade.class)
@Slf4j
public class InstBaseInfoQueryFacadeImpl implements InstBaseInfoQueryFacade {

    @Resource
    private InstBaseInfoManger instBaseInfoManger;

    @Resource
    private ReqDoAssembler reqDoAssembler;

    @Resource
    private ResDoAssembler resDoAssembler;

    @Resource
    private InstBankAccountManager instBankAccountManager;

    @Override
    public Result<List<String>> listInstCode() {
        try {
            List<String> allInstCodeList = instBaseInfoManger.listInstCode();
            return ResultUtils.success(allInstCodeList);
        } catch (BizException e) {
            return ResultUtils.bizExceptionFail(e);
        } catch (Throwable t) {
            return ResultUtils.unknownFail();
        }
    }

    @Override
    public Result<List<QueryInstBrandResponse>> listInstBrand(QueryInstBrandRequest queryInstBrandRequest) {
        if(Objects.isNull(queryInstBrandRequest)) {
            queryInstBrandRequest = new QueryInstBrandRequest();
        }
        QueryInstBrandRequestDO queryInstBrandRequestDO = reqDoAssembler.toQueryInstBrandRequestDO(queryInstBrandRequest);
        List<QueryInstBrandResponseDO> queryInstBrandResponseDOList = instBaseInfoManger.listInstBrand(queryInstBrandRequestDO);
        if(CollectionUtils.isEmpty(queryInstBrandResponseDOList)) {
            return ResultUtils.success(Collections.emptyList());
        }
        return ResultUtils.success(resDoAssembler.toQueryInstBrandResponseList(queryInstBrandResponseDOList.stream()
                .filter(item -> StringUtil.isNotEmpty(item.getBrandCode())).collect(Collectors.toList())));
    }

    @Override
    public Result<List<InstBankAccountResponse>> queryInstBankAccount(InstBankAccountRequest req) {
        // useType入口检验
        InstBaseInfoFacadeValidator.validateInstAccountReqUseType(req.getUseType());

        InstBankAccountReqDTO reqDTO = reqDoAssembler.toInstBankAccountRequest(req);
        List<InstBankAccountVO> instBankAccountVOList = instBankAccountManager.queryByCondition(reqDTO);
        if(CollectionUtils.isEmpty(instBankAccountVOList)) {
            return ResultUtils.success(Collections.emptyList());
        }

        return ResultUtils.success(resDoAssembler.toQueryInstBankAccountResponseDOList(instBankAccountVOList));
    }

    /**
     * 根据渠道编码查询机构品牌，channelCode 不存在时直接返回原值
     */
    @Override
    public Result<List<InstBaseInfoWithBrandResponse>> queryByChannelCodeList(QueryBrandByChannelCodeRequest request) {
        AssertUtil.isTrue(CollectionUtil.isNotEmpty(request.getChannelCodeList()), "ERROR","渠道编码不能为空");
        return ResultUtils.success(instBaseInfoManger.listByChannelCode(request));
    }
}
