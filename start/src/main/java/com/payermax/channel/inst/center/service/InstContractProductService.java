package com.payermax.channel.inst.center.service;

import com.payermax.channel.inst.center.app.assembler.domain.InstContractProductAssembler;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractBaseFormMsgDTO;
import com.payermax.channel.inst.center.app.factory.OperateLogFactory;
import com.payermax.channel.inst.center.app.request.InstContractProductQueryRequest;
import com.payermax.channel.inst.center.app.response.InstContractBaseResponse;
import com.payermax.channel.inst.center.app.response.InstContractProductQueryVO;
import com.payermax.channel.inst.center.common.enums.operatelog.LogOperateResTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.infrastructure.adapter.VoucherAdapter;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import com.payermax.channel.inst.center.infrastructure.repository.repo.*;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ObjectUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/18
 * @DESC 机构合约产品查询服务
 */
@Service
@Slf4j
@AllArgsConstructor
public class InstContractProductService{

    private final InstNewContractCacheManager instNewContractCacheManager;
    private final InstContractBaseInfoRepository instContractBaseInfoRepository;
    private final InstContractStandardProductRepository contractStandardProductRepository;
    private final InstContractOperateLogRepository contractOperateLogRepository;
    private final InstContractOriginProductRepository contractOriginProductRepository;
    private final InstBusinessDraftRepository businessDraftRepository;
    private final OperateLogFactory operateLogFactory;
    private final VoucherAdapter voucherAdapter;
    private final InstContractProductAssembler contractProductAssembler;
    private final InstProcessDockRepository processDockRepository;

    /**
     * 根据参数查询并构造当前时间生效的原始产品列表
     */
    public List<InstContractOriginProductPO> composeOriginProductList(InstContractProductQueryRequest request){
        return composeOriginProductListByVersionFetcher(request,
                contract -> instNewContractCacheManager.getEffectiveInstVersionContract(contract.getContractNo(), System.currentTimeMillis()));
    }

    /**
     * 根据参数查询并构造最新版本的原始产品列表
     */
    public List<InstContractOriginProductPO> composeLatestVersionOriginProductList(InstContractProductQueryRequest request){
        return composeOriginProductListByVersionFetcher(request,
                contract -> instNewContractCacheManager.getLatestEffectiveVersionContract(contract.getContractNo()));
    }

    /**
     * 通过传入的版本查询器构造原始产品列表
     * @param request 请求信息
     * @param versionFetcher 版本查询器
     */
    private List<InstContractOriginProductPO> composeOriginProductListByVersionFetcher(InstContractProductQueryRequest request, Function<InstContractBaseInfoPO, InstContractVersionInfoPO> versionFetcher){
        // 1. 根据机构、主体、产品类型查询合同
        List<InstContractBaseInfoPO> contractBaseInfoList = instContractBaseInfoRepository.queryActiveBaseInfoNullable(request.getInstCode(), request.getContractEntity(), request.getInstProductType(), null);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(contractBaseInfoList), "ERROR","合同不存在");

        //2. 根据合同查询合同版本
        List<InstContractVersionInfoPO> contractVersionInfoList = contractBaseInfoList.stream()
                .map(versionFetcher).collect(Collectors.toList());
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(contractVersionInfoList), "ERROR", "合同版本不存在");

        // 分组备用
        Map<String, InstContractBaseInfoPO> contractBaseInfoMap = contractBaseInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(InstContractBaseInfoPO::getContractNo, contract -> contract));
        Map<String, InstContractVersionInfoPO> contractVersionMap = contractVersionInfoList.stream().filter(Objects::nonNull).collect(Collectors.toMap(InstContractVersionInfoPO::getContractVersion, contract -> contract));

        // 3. 根据合同版本查询原始产品列表、标准产品列表
        List<InstContractOriginProductPO> originProductList = contractVersionInfoList.stream().filter(Objects::nonNull).filter(contractVersion -> CollectionUtils.isNotEmpty(contractVersion.getOriginProducts())).flatMap(contractVersion -> contractVersion.getOriginProducts().stream()).collect(Collectors.toList());
        List<InstContractStandardProductPO> standardProductList = contractVersionInfoList.stream().filter(Objects::nonNull).filter(contractVersion -> CollectionUtils.isNotEmpty(contractVersion.getStandardProducts())).flatMap(contractVersion -> contractVersion.getStandardProducts().stream()).collect(Collectors.toList());
        Map<String, List<InstContractStandardProductPO>> groupingStandardProduct = standardProductList.stream().collect(Collectors.groupingBy(InstContractStandardProductPO::getInstOriginProductNo));

        // 数据填充
        originProductList.forEach(originProductPo -> originProductPo.setStandardProducts(groupingStandardProduct.get(originProductPo.getInstOriginProductNo())));
        originProductList.forEach(originProductPo -> originProductPo.setContractBaseInfo(contractBaseInfoMap.get(originProductPo.getContractNo())));
        originProductList.forEach(originProductPo -> originProductPo.setContractVersionInfo(contractVersionMap.get(originProductPo.getContractVersion())));
        return originProductList;

    }


    /**
     * 查询产品列表
     */
    public List<InstContractProductQueryVO> queryProductList(InstContractProductQueryRequest request){
        // 1. 根据参数查询并构造原始产品列表
        List<InstContractOriginProductPO> originProductPoList = composeOriginProductList(request);
        Map<String, InstContractOriginProductPO> originProductMap = originProductPoList.stream().collect(Collectors.toMap(InstContractOriginProductPO::getInstOriginProductNo, product -> product));
        // 2. 根据币种过滤
        if(StringUtil.isNotBlank(request.getPayCurrency())){
            originProductPoList = originProductPoList.stream()
                    .filter(product -> product.getContractFeeItems().stream().anyMatch(feeItem -> feeItem.getPayCurrency().equals(request.getPayCurrency())))
                    .collect(Collectors.toList());
        }
        // 3. 处理数据
        List<InstContractProductQueryVO> productList = originProductPoList.stream().map(contractProductAssembler::originProductPO2VO).collect(Collectors.toList());
        return productList.stream().peek(vo -> {
            // 币种列表，无费用信息时默认空数组
            InstContractOriginProductPO originProduct = originProductMap.get(vo.getInstOriginProductNo());
            vo.setPayCurrencyList(new ArrayList<>());
            if (Objects.nonNull(originProduct.getContractFeeItems())) {
                vo.setPayCurrencyList(originProduct.getContractFeeItems().stream().map(InstContractFeeItemPO::getPayCurrency).distinct().collect(Collectors.toList()));
            }
        }).peek(vo -> vo.setStandardProductMsgList(getMsgFromOriginMap(originProductMap, vo.getInstOriginProductNo())))
        .collect(Collectors.toList());
    }

    /**
     * 查询标准产品列表
     */
    public List<InstContractStandardProductPO> queryStandardProductList(InstContractProductQueryRequest request){
        // 根据参数查询并构造原始产品列表
        List<InstContractOriginProductPO> originProductList = composeOriginProductList(request);
        // 标准产品列表
        List<InstContractStandardProductPO> standardProductList = originProductList.stream()
                .map(InstContractOriginProductPO::getStandardProducts)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        // 过滤并返回
        return filterStandardProductList(request, standardProductList);
    }

    /**
     * 修改标准化产品信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean standardProductModified(String shareId, InstContractStandardProductPO request){
        AssertUtil.isTrue(StringUtil.isNotBlank(request.getInstStandardProductNo()), "ERROR", "标准产品编号不能为空");
        // 查询标准产品信息
        InstContractStandardProductPO originData = contractStandardProductRepository.getById(request.getInstStandardProductNo());
        // 保存日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeStandardProductOperateByAdmin(shareId, originData, request, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.UPDATE);
        contractOperateLogRepository.save(operateLog);
        // 更新标准化产品
        try{
            return contractStandardProductRepository.updateById(request);
        } catch (DuplicateKeyException e) {
            log.warn("Duplicate entry", e);
            throw new BusinessException("ERROR","标准化产品机构卡组重复，请检查标准化产品");
        }

    }

    /**
     * 添加标准化产品
     */
    @Transactional(rollbackFor = Exception.class)
    public String standardProductAdd(String shareId, InstContractStandardProductPO standardProduct){
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getContractNo()), "ERROR", "合同编号不能为空");
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getInstOriginProductNo()), "ERROR", "原始产品编号不能为空");
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getContractVersion()), "ERROR", "合同版本不能为空");
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getPaymentMethodType()), "ERROR", "支付方式类型不能为空");
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getTargetOrg()), "ERROR", "目标机构不能为空");
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getCardOrg()), "ERROR", "卡组不能为空");
        // 保存日志
        standardProduct.setInstStandardProductNo(voucherAdapter.getStandardProductCode());
        InstContractOperateLogPO operateLog = operateLogFactory.composeStandardProductOperateByAdmin(shareId, null, standardProduct, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.ADD);
        contractOperateLogRepository.save(operateLog);
        // 保存标准化产品
        try{
            contractStandardProductRepository.save(standardProduct);
        } catch (DuplicateKeyException e) {
            log.warn("Duplicate entry", e);
            throw new BusinessException("ERROR","标准化产品机构卡组重复，请检查标准化产品");
        }
        return standardProduct.getInstStandardProductNo();
    }

    /**
     * 删除标准化产品
     */
    @Transactional(rollbackFor = Exception.class)
    public String standardProductDel(String shareId, InstContractStandardProductPO standardProduct){
        AssertUtil.isTrue(StringUtil.isNotBlank(standardProduct.getInstStandardProductNo()), "ERROR", "标准产品编号不能为空");
        InstContractStandardProductPO originData = contractStandardProductRepository.getById(standardProduct.getInstStandardProductNo());
        AssertUtil.isTrue(ObjectUtil.isNotEmpty(originData), "ERROR", "标准产品不存在");
        // 保存日志
        InstContractOperateLogPO operateLog = operateLogFactory.composeStandardProductOperateByAdmin(shareId, originData, standardProduct, LogOperateResTypeEnum.SUCCESS, OperateTypeEnum.DELETE);
        contractOperateLogRepository.save(operateLog);
        // 删除标准化产品
        contractStandardProductRepository.removeById(standardProduct.getInstStandardProductNo());
        return standardProduct.getInstStandardProductNo();
    }




    /**
     * 根据目标机构、卡组、支付方式类型过滤原始产品列表
     */
    public List<InstContractOriginProductPO> filterOriginProductList(InstContractProductQueryRequest request, List<InstContractOriginProductPO> originProductList){
        // 涉及标准产品查询时才进行过滤，避免无标准化产品的原始产品被过滤
        if(StringUtil.isNotBlank(request.getTargetOrg())){
            originProductList = originProductList.stream()
                    .filter(originProduct -> Objects.nonNull(originProduct.getStandardProducts()))
                    .filter(originProduct -> originProduct.getStandardProducts().stream()
                            .filter(standardProduct -> Objects.nonNull(standardProduct.getTargetOrg()))
                            .anyMatch(standardProduct -> standardProduct.getTargetOrg().equals(request.getTargetOrg())))
                    .collect(Collectors.toList());
        }
        if(StringUtil.isNotBlank(request.getCardOrg())){
            originProductList = originProductList.stream()
                    .filter(originProduct -> Objects.nonNull(originProduct.getStandardProducts()))
                    .filter(originProduct -> originProduct.getStandardProducts().stream()
                            .filter(standardProduct -> Objects.nonNull(standardProduct.getCardOrg()))
                            .anyMatch(standardProduct -> standardProduct.getCardOrg().equals(request.getCardOrg())))
                    .collect(Collectors.toList());
        }
        if(StringUtil.isNotBlank(request.getPaymentMethodType())){
            originProductList = originProductList.stream()
                    .filter(originProduct -> Objects.nonNull(originProduct.getStandardProducts()))
                    .filter(originProduct -> originProduct.getStandardProducts().stream()
                            .filter(standardProduct -> Objects.nonNull(standardProduct.getPaymentMethodType()))
                            .anyMatch(standardProduct -> standardProduct.getPaymentMethodType().equalsIgnoreCase(request.getPaymentMethodType())))
                    .collect(Collectors.toList());
        }
        return originProductList;
    }

    /**
     * 根据目标机构、卡组、支付方式类型过滤标准产品列表
     */
    public List<InstContractStandardProductPO> filterStandardProductList(InstContractProductQueryRequest request, List<InstContractStandardProductPO> standardProductList){
        // 涉及标准产品查询时才进行过滤，避免无标准化产品的原始产品被过滤
        if(StringUtil.isNotBlank(request.getTargetOrg())){
            standardProductList = standardProductList.stream()
                    .filter(standardProduct -> Objects.nonNull(standardProduct.getTargetOrg()))
                    .filter(standardProduct -> standardProduct.getTargetOrg().equals(request.getTargetOrg()))
                    .collect(Collectors.toList());
        }
        if(StringUtil.isNotBlank(request.getCardOrg())){
            standardProductList = standardProductList.stream()
                    .filter(standardProduct -> Objects.nonNull(standardProduct.getCardOrg()))
                    .filter(standardProduct -> standardProduct.getCardOrg().equals(request.getCardOrg()))
                    .collect(Collectors.toList());
        }
        if(StringUtil.isNotBlank(request.getPaymentMethodType())){
            standardProductList = standardProductList.stream()
                    .filter(standardProduct -> Objects.nonNull(standardProduct.getPaymentMethodType()))
                    .filter(standardProduct -> standardProduct.getPaymentMethodType().equalsIgnoreCase(request.getPaymentMethodType()))
                    .collect(Collectors.toList());
        }
        return standardProductList;
    }

    /**
     * 从原始产品 Map 中获取标准化产品列表
     * @param originProductMap 原始产品 Map
     * @param originProductNo 原始产品编号
     */
    public List<InstContractBaseResponse.StandardProductMsg> getMsgFromOriginMap(Map<String, InstContractOriginProductPO> originProductMap, String originProductNo) {
        // 无标准化产品时默认空数组
        return Optional.ofNullable(originProductMap.get(originProductNo))
                .filter(originProduct -> Objects.nonNull(originProduct.getStandardProducts()))
                .map(originProduct -> originProduct.getStandardProducts().stream().map(contractProductAssembler::standardProductPO2QueryMsg).collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }


    /**
     * 构建修改流程基础表单信息
     */
    public InstContractBaseFormMsgDTO composeBaseModifiedProcessFormMsg(String originProductNo) {
        InstContractBaseFormMsgDTO formMsg = new InstContractBaseFormMsgDTO();
        // 1. 查询原始产品
        InstContractOriginProductPO originProduct = contractOriginProductRepository.queryByOriginProductNo(originProductNo);
        // 2. 查询合同
        InstContractBaseInfoPO contractBaseInfo = instContractBaseInfoRepository.queryOneByNo(originProduct.getContractNo());
        AssertUtil.isTrue(Objects.nonNull(contractBaseInfo), "ERROR", "合同不存在");
        // 3. 查询合同版本
        InstContractVersionInfoPO contractVersionInfo = instNewContractCacheManager.getEffectiveInstVersionContract(originProduct.getContractNo(), System.currentTimeMillis());
        AssertUtil.isTrue(Objects.nonNull(contractVersionInfo), "ERROR", "合同版本不存在");
        // 4. 产品标准化信息
        String standardProductMsgStr = null;
        if (Objects.nonNull(contractVersionInfo.getStandardProducts())) {
            standardProductMsgStr = contractVersionInfo.getStandardProducts().stream()
                    .filter(standardProduct -> standardProduct.getInstOriginProductNo().equals(originProductNo))
                    .map(contractProductAssembler::standardProductPO2QueryMsg)
                    .map(msg -> String.format("支付方式类型：%s，目标机构：%s，卡组：%s", msg.getPaymentMethodType(), msg.getTargetOrg(), Optional.ofNullable(msg.getCardOrg()).orElse("/")))
                    .collect(Collectors.joining("\n"));
        }

        // 4. 返回信息
        return formMsg.setInstCode(contractBaseInfo.getInstCode())
                .setContractEntity(contractBaseInfo.getContractEntity())
                .setInstProductType(contractBaseInfo.getInstProductType())
                .setInstProductName(originProduct.getInstProductName())
                .setStandardProductMsgList(standardProductMsgStr);
    }
}
