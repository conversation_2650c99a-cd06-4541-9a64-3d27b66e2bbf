package com.payermax.channel.inst.center;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowHandlerRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Spring Boot Starter
 *  应用内命名规范文档：https://shimo.im/docs/WlArz4ro8nSDXwA2
 *  脚手架项目介绍文档：https://shimo.im/docs/cx6Gs8EGTGcTH7ls
 *
 * <AUTHOR> yuanzhe
 */
@SpringBootApplication(scanBasePackages = { "com.payermax.channel.inst.center","com.payermax.infra.ionia.fs" }, exclude = {DruidDataSourceAutoConfigure.class})
@Slf4j
@EnableDubbo
@EnableTransactionManagement
@EnableFeignClients(basePackages = "com.payermax.channel.inst.center.infrastructure.client")
@MapperScan(value = {"com.payermax.channel.inst.center.infrastructure.mapper", "com.payermax.channel.inst.center.infrastructure.repository.mapper"})
@Import({WorkflowHandlerRegistry.class})
public class ChannelInstCenterApplication {

    public static void main(String[] args) {
        log.info("Service started...");
        SpringApplication.run(ChannelInstCenterApplication.class, args);
        log.info("Service started successful!");
    }
}
