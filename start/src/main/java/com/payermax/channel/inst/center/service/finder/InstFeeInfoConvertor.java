package com.payermax.channel.inst.center.service.finder;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractRequestAssembler;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.SettlementConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/2/21
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class InstFeeInfoConvertor {

    private final InstContractRequestAssembler instContractRequestAssembler;

    /**
     * MCC 未知时的默认值 | 目标机构默认值 | 卡组默认值
     */
    public static final String UNKNOWN_MCC = "UNKNOWN";
    public static final String DEFAULT_TARGET_ORG = "*";
    public static final String DEFAULT_CARD_ORG = "CARDPAY";

    /**
     * 费用转换
     * 包括 {@link FeeTypeEnum} 中所有费用种类
     */
    public Map<String, FeeConfig> feeConfigConvert(InstContractFeeItem feeItem){

        Map<FeeTypeEnum, InstFeeConfig> feeConfigs = feeItem.getFeeConfigs();
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(feeItem.getFeeConfigs()), "Error", "feeConfigs is empty!");

        // 费用
        Map<String, FeeConfig> feeConfigMap = new HashMap<>(feeConfigs.size());
        feeConfigs.forEach((key, value) -> {
            FeeConfig feeConfig = instContractRequestAssembler.convertInstFee2Response(value);
            if (FeeTypeEnum.TRADE == key) {
                if (feeItem.getAccumulationCycle() != null) {
                    feeConfig.setAccumulationCycle(feeItem.getAccumulationCycle().name());
                }
                if (feeItem.getAccumulationType() != null) {
                    feeConfig.setAccumulationType(feeItem.getAccumulationType().name());
                }
                if (feeItem.getAccumulationMethod() != null) {
                    feeConfig.setAccumulationMethod(feeItem.getAccumulationMethod().name());
                }
                if (feeItem.getAccumulationRange() != null) {
                    feeConfig.setAccumulationRange(feeItem.getAccumulationRange().name());
                }
                if (feeItem.getAccumulationDeductTime() != null) {
                    feeConfig.setAccumulationDeductTime(feeItem.getAccumulationDeductTime().name());
                }

                Optional.ofNullable(feeItem.getAccumulationMode()).ifPresent(feeConfig::setAccumulationMode);

                // 封顶、保底费用币种
                if(StringUtils.isBlank(feeConfig.getPercentMaxAmountCurrency())){
                    feeConfig.setPercentMaxAmountCurrency(feeItem.getPayCurrency());
                }
                if(StringUtils.isBlank(feeConfig.getPercentMinAmountCurrency())){
                    feeConfig.setPercentMinAmountCurrency(feeItem.getPayCurrency());
                }
                feeConfig.setAccumulationJoin(feeItem.getAccumulationJoin());
                feeConfig.setAccumulationKey(feeItem.getAccumulationKey());
                feeConfig.setAccumulation(StringUtils.isNotEmpty(feeItem.getAccumulationKey()));
            }
            feeConfigMap.put(key.name(), feeConfig);
        });
        return feeConfigMap;
    }


    /**
     * 税费转换
     */
    public List<TaxConfig> taxConfigConvert(InstContractFeeItem feeItem){
        return instContractRequestAssembler.convertInstTax2ResponseList(feeItem.getTaxConfigs());
    }

    /**
     * 结算配置转换
     */
    public void settleConfigConvert(InstInfoQueryResponse response, InstContractSettlementItem settleItem){
        Optional.ofNullable(settleItem).ifPresent( settleData -> {
            SettlementConfig settlementConfig = instContractRequestAssembler.convertInstSettle2Response(settleData);
            response.setSettlementConfig(settlementConfig);
        });
    }


    /**
     * 合约及产品参数补全
     */
    protected void contractAndProductParamFill(InstInfoQueryRequest request){
        // 把交易链路兜底类数据替换掉
        if (StringUtil.isNotEmpty(request.getTargetOrg())) {
            request.setTargetOrg(request.getTargetOrg().replace(DEFAULT_TARGET_ORG, ""));
        }

        if (StringUtil.isNotEmpty(request.getCardOrg())) {
            request.setCardOrg(request.getCardOrg().replace(DEFAULT_CARD_ORG, ""));
        }
    }

    /**
     * 费用项参数填充
     */
    protected void feeItemParamFill(InstInfoQueryRequest request){
        // MCC 不存在时，使用 UNKNOWN 替换
        if (StringUtil.isEmpty(request.getMcc())) {
            request.setMcc(UNKNOWN_MCC);
        }
    }

    /**
     * 返回值通用填充
     */
    public void responseDefaultFill(InstInfoQueryResponse response, InstContractQueryContext context){
        InstContractFeeItem feeItem = context.getFeeItem();
        InstContractVersionInfo versionInfo = context.getContractVersionInfo();

        // 费用信息
        Optional.ofNullable(feeItem).ifPresent( feeData -> {
            Optional.ofNullable(feeItem.getRoundingScale()).ifPresent(response::setRoundingScale);
            Optional.ofNullable(feeItem.getRoundingMode()).ifPresent(data -> response.setRoundingMode(data.name()));
            Optional.ofNullable(feeData.getInstContractFeeItemNo()).ifPresent(response::setInstContractFeeItemNo);
        });

        // 合同信息
        response.setInstCode(versionInfo.getInstCode());
        response.setContractNo(versionInfo.getContractNo());
        response.setContractVersion(versionInfo.getContractVersion());
    }
}
