package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.facade.api.InstFinancialCalendarQueryFacade;
import com.payermax.channel.inst.center.facade.request.calendar.HolidayMultipleCheckRequest;
import com.payermax.channel.inst.center.facade.request.calendar.HolidayQueryRequest;
import com.payermax.channel.inst.center.facade.request.calendar.NextWorkdayCalculationRequest;
import com.payermax.channel.inst.center.facade.request.calendar.NumOfWorkdaysCalculationRequest;
import com.payermax.channel.inst.center.facade.response.calendar.HolidayMultipleCheckResponse;
import com.payermax.channel.inst.center.facade.response.calendar.HolidayQueryResponse;
import com.payermax.channel.inst.center.facade.response.calendar.NextWorkdayCalculationResponse;
import com.payermax.channel.inst.center.facade.response.calendar.NumOfWorkdaysCalculationResponse;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2024/11/12 17:07
 */
@Api(tags = "测试用的API")
@Slf4j
@RestController
@RequestMapping("holidays")
@RequiredArgsConstructor
@Setter
public class IndexController {

    @DubboReference(version = "1.0")
    private InstFinancialCalendarQueryFacade instFinancialCalendarQueryFacade;

    /**
     * 查询日期间隔多少工作日
     */
    @PostMapping("numOfWorkdays")
    @DigestLog(isRecord = true)
    public Result<List<NumOfWorkdaysCalculationResponse>> calculateNumOfWorkdays(@RequestBody List<NumOfWorkdaysCalculationRequest> request) {
        return instFinancialCalendarQueryFacade.calculateNumOfWorkdays(request);
    }

    /**
     * 查询过N个工作日是哪一天
     */
    @PostMapping("nextWorkdays")
    @DigestLog(isRecord = true)
    public Result<List<NextWorkdayCalculationResponse>> calculateNextWorkdays(@RequestBody List<NextWorkdayCalculationRequest> request) {
        return instFinancialCalendarQueryFacade.calculateNextWorkdays(request);
    }

    /**
     * 查询过N个工作日是哪一天
     */
    @PostMapping("nextWorkdaysMultiple")
    @DigestLog(isRecord = true)
    public Result<HolidayMultipleCheckResponse> nextWorkdaysMultiple(@RequestBody HolidayMultipleCheckRequest request) {
        return instFinancialCalendarQueryFacade.calculateNextWorkdaysOfMultipleLimited(request);
    }

    /**
     * 查询日期间隔多少工作日
     */
    @PostMapping("query")
    @DigestLog(isRecord = true)
    public Result<HolidayQueryResponse> queryAllHolidaysByDate(@RequestBody HolidayQueryRequest request) {
        return instFinancialCalendarQueryFacade.queryAllHolidaysByDate(request);
    }

}
