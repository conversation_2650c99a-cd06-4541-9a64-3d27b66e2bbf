package com.payermax.channel.inst.center.service.convertor.calculator;

import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;

public interface RoundCalculator {
    SettleInfoVo calculate(DSLEntity dslEntity, Integer cycleStart, Integer cycleEnd);

    RoundTypeEnum getRoundType();
}
