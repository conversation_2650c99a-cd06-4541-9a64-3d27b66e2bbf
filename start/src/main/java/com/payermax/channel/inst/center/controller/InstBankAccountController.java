package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.manage.account.InstBankAccountNewManager;
import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.request.account.InstBankAccountSaveOrUpdateRequest;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.channel.inst.center.common.utils.ValidationUtils;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.common.lang.model.dto.Result;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/27
 * @DESC
 */
@Api(tags = "银行账户API")
@Slf4j
@RestController
@RequestMapping("instCenter/bankAccount")
@RequiredArgsConstructor
public class InstBankAccountController {

    private final InstBankAccountNewManager instBankAccountNewManager;

    /**
     * 新增银行账户流程发起
     */
    @PostMapping("/startSaveProcess")
    public Result<Boolean> startSaveProcess(@RequestHeader("shareId") String shareId, @RequestBody InstBankAccountSaveOrUpdateRequest request) {
        return ExceptionUtils.commonTryCatch(() -> {
            ValidationUtils.validate(request);
            return instBankAccountNewManager.startSaveProcess(request, shareId);
        });
    }

    /**
     * 修改银行账户流程发起
     */
    @PostMapping("/startUpdateProcess")
    public Result<Boolean> startUpdateProcess(@RequestHeader("shareId") String shareId, @RequestBody InstBankAccountSaveOrUpdateRequest request) {
        return ExceptionUtils.commonTryCatch(() -> {
            ValidationUtils.validate(request);
            return instBankAccountNewManager.startUpdateProcess(request, shareId);
        });
    }

    /**
     * 查询账户信息
     */
    @PostMapping("/queryAccountById")
    public Result<InstBankAccountVO> queryAccountById(@RequestBody InstBankAccountReqDTO request){
        return ExceptionUtils.commonTryCatch(() -> {
            AssertUtil.isTrue(ObjectUtils.isNotEmpty(request.getId()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "id不能为空");
            return instBankAccountNewManager.queryAccountById(request);
        });
    }

    /**
     * 查询账户列表
     */
    @PostMapping("/queryByInstAllowDelete")
    public Result<List<InstBankAccountVO>> queryByInstAllowDelete(@RequestBody InstBankAccountReqDTO request){
        return ExceptionUtils.commonTryCatch(() -> {
            AssertUtil.isTrue(ObjectUtils.isNotEmpty(request.getInstId()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "instId 不能为空");
            return instBankAccountNewManager.queryAllowDelete(request);
        });
    }


}
