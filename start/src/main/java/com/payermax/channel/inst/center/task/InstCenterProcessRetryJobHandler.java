package com.payermax.channel.inst.center.task;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.payermax.channel.inst.center.app.assembler.domain.InstBusinessDraftAssembler;
import com.payermax.channel.inst.center.app.manage.workflow.AbstractWorkflowHandler;
import com.payermax.channel.inst.center.app.manage.workflow.WorkflowHandlerRegistry;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstBusinessDraftRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/19
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
@JobHandler(value = "instCenterProcessRetryJobHandler")
public class InstCenterProcessRetryJobHandler extends IJobHandler {

    private final InstBusinessDraftRepository businessDraftRepository;
    private final WorkflowHandlerRegistry workflowHandlerRegistry;

    @Override
    public ReturnT<String> execute(String businessId) {
        if (StringUtils.isNotBlank(businessId)) {
            log.info("重试指定流程: {}", businessId);
            InstBusinessDraft draft = InstBusinessDraftAssembler.INSTANCE.po2Domain(businessDraftRepository.getById(businessId));

            AssertUtil.isTrue(Objects.nonNull(draft), ErrorCodeEnum.INST_CENTER_PROCESS_RETRY_ERROR.getCode(), String.format("不存在该流程: %s", businessId));
            retryProcess(draft);
            log.info("流程重试完成");
        } else {
            log.info("捞取所有流程进行重试");
            List<InstBusinessDraft> waitingRetryList = InstBusinessDraftAssembler.INSTANCE.po2DomainList(businessDraftRepository.getWaitingRetryList());
            log.info("待重试流程数: {}", waitingRetryList.size());
            waitingRetryList.forEach(this::retryProcess);
            log.info("所有流程重试完成");
        }
        return ReturnT.SUCCESS;
    }

    private void retryProcess(InstBusinessDraft draft) {
        try {
            // 获取回调处理器
            log.info("获取回调处理器: {}, {}, {}", draft.getBusinessType(), draft.getModuleName(), draft.getOperateType());
            String handleName = workflowHandlerRegistry.getHandlerName(draft.getBusinessType().name(), draft.getModuleName().name(), draft.getOperateType().name());
            AbstractWorkflowHandler handler = workflowHandlerRegistry.getHandler(handleName);
            // 执行回调
            handler.retryHandler(draft);
        }catch (Exception e){
            log.error("流程重试失败",e);
        }
    }

}
