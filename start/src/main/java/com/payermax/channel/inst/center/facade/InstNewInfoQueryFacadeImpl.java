package com.payermax.channel.inst.center.facade;

import cn.hutool.core.text.StrPool;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractRequestAssembler;
import com.payermax.channel.inst.center.app.manage.contract.InstContractIntegrityBatchValidator;
import com.payermax.channel.inst.center.app.manage.contract.InstContractIntegrityValidator;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.common.utils.ValidationUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.facade.api.InstNewInfoQueryFacade;
import com.payermax.channel.inst.center.facade.request.contract.*;
import com.payermax.channel.inst.center.facade.request.contract.config.DeductConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.response.InstContractBatchVerifyResponse;
import com.payermax.channel.inst.center.facade.response.contract.*;
import com.payermax.channel.inst.center.service.InstContractQueryServiceImpl;
import com.payermax.channel.inst.center.service.InstContractSceneQueryService;
import com.payermax.channel.inst.center.service.convertor.DslEntity2SettleInfoVoConvertor;
import com.payermax.channel.inst.center.service.convertor.SettleInfoConvertor;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.DateUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> tracy
 * @version 2023-08-11 5:36 PM
 */
@DubboService
@Slf4j
@Setter
public class InstNewInfoQueryFacadeImpl implements InstNewInfoQueryFacade {

    @Resource
    private InstContractQueryServiceImpl instContractQueryService;

    @Resource
    private InstContractRequestAssembler instContractRequestAssembler;

    @Resource
    private DslEntity2SettleInfoVoConvertor dslEntity2SettleInfoVoConvertor;

    @Resource
    private SettleInfoConvertor settleInfoConvertor;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private InstContractIntegrityValidator contractIntegrityValidator;

    @Resource
    private InstContractSceneQueryService instContractSceneQueryService;

    @Resource
    private InstContractIntegrityBatchValidator contractIntegrityBatchValidator;


    /**
     * 出款扣款币种暂时没,特殊场景使用nacos配置
     */
    @NacosValue(value = "#{${inst.funds.contract.payout.deduct.map}}", autoRefreshed = true)
    private HashMap<String, String> payoutDeductMap;

    @Override
    public Result<InstInfoQueryResponse> queryFeeInfo(InstInfoQueryRequest request) {
        try {
            ValidationUtils.validate(request);

            InstInfoQueryResponse response = instContractRequestAssembler.convertRequest2Response(request);

            //0. 参数校验
            paramsCheck(request);

            //1. 根据mid+bizType+交易时间查询合同
            InstContractVersionInfo contractInfo = instContractQueryService.queryActiveContract(request.getChannelMerchantCode(), request.getBizType(), request.getTransactionTime());
            response.setContractNo(contractInfo.getContractNo());
            response.setContractVersion(contractInfo.getContractVersion());

            //2. 根据合同查询标准产品
            InstContractStandardProduct standardProduct = instContractQueryService.queryStandardProduct(contractInfo.getStandardProducts()
                    , request.getPaymentMethodType(), request.getTargetOrg(), request.getCardOrg());

            //3. 根据标准产品查询原始产品
            InstContractOriginProduct instContractOriginProduct = instContractQueryService.queryOriginProductByNo(standardProduct.getInstOriginProductNo(), contractInfo);

            //4. 根据原始产品查询Fee信息
            InstContractFeeItem feeItem = instContractQueryService.queryFeeByOriProduct(instContractOriginProduct, request);

            //5. 根据原始产品查询SettleItem
            InstContractSettlementItem settleItem = null;
            try {
                // 只有入款需要查询结算信息
                if ("I".equalsIgnoreCase(request.getBizType())) {
                    settleItem = instContractQueryService.querySettleByOriProduct(instContractOriginProduct, request.getPayCurrency()
                            , request.getChannelMerchantCode());
                }
            } catch (Exception e) {
                // 结算信息不存在不影响费用查询
                log.warn("Settle info query error!", e);
                channelErrInfoNotify(standardProduct, contractInfo.getInstCode(), request.getChannelMerchantCode(), request.getPayCurrency());
            }

            //6. 组装返回
            assemblerResponse(feeItem, settleItem, contractInfo, response);

            return ResultUtils.success(response);
        } catch (BizException e) {
            log.error("biz Exception", e);
            return ResultUtils.bizExceptionFail(e);
        } catch (BusinessException | IllegalArgumentException e) {
            log.error("BusinessException Exception", e);
            return ResultUtils.bizException(e);
        } catch (Exception e1) {
            log.error("unknown Exception", e1);
            return ResultUtils.unknownFail();
        }

    }

    @Override
    public Result<InstInfoQueryResponse> querySettleAndFxInfo(InstInfoQueryRequest request) {

        try {
            //0. 参数校验
            ValidationUtils.validate(request);
            paramsCheck(request);

            InstInfoQueryResponse response = instContractRequestAssembler.convertRequest2Response(request);

            //1. 根据mid+bizType+交易时间查询合同
            InstContractVersionInfo contractInfo = instContractQueryService.queryActiveContract(request.getChannelMerchantCode(), request.getBizType(), request.getTransactionTime());
            response.setContractNo(contractInfo.getContractNo());
            response.setContractVersion(contractInfo.getContractVersion());

            //2. 根据合同查询标准产品
            List<InstContractStandardProduct> standardProductList = instContractQueryService.queryStandardProductForSettleQuery(contractInfo.getStandardProducts()
                    , request.getPaymentMethodType(), request.getTargetOrg(), request.getCardOrg());

            for (InstContractStandardProduct standardProduct : standardProductList) {
                try {
                    //3. 根据标准产品查询原始产品
                    InstContractOriginProduct instContractOriginProduct = instContractQueryService.queryOriginProductByNo(standardProduct.getInstOriginProductNo(), contractInfo);

                    //4. 根据原始产品查询Fee信息
                    InstContractFeeItem feeItem = instContractQueryService.queryFeeByOriProduct(instContractOriginProduct, request);

                    //5. 根据原始产品查询SettleItem
                    InstContractSettlementItem settleItem = null;
                    // 只有入款需要查询结算信息
                    if ("I".equalsIgnoreCase(request.getBizType())) {
                        settleItem = instContractQueryService.querySettleByOriProduct(instContractOriginProduct, request.getPayCurrency()
                                , request.getChannelMerchantCode());
                    } else {
                        DeductConfig deductConfig = new DeductConfig();
                        deductConfig.setDeductCurrency(payoutDeductMap.get(feeItem.getInstContractFeeItemNo()));
                        response.setDeductConfig(deductConfig);
                    }

                    //6. 组装返回
                    assemblerResponse(null, settleItem, contractInfo, response);

                    //7. 处理FX
                    response.setFxConfig(instContractRequestAssembler.convertInstFx2Response(feeItem));

                    return ResultUtils.success(response);
                } catch (Exception e) {
                    log.warn("粗略过滤查找失败!");
                }
            }
            throw new BusinessException("ERROR", "结算最终没有找到信息");
        } catch (BizException e) {
            log.error("biz Exception", e);
            return ResultUtils.bizExceptionFail(e);
        } catch (BusinessException | IllegalArgumentException e) {
            log.error("BusinessException Exception", e);
            return ResultUtils.bizException(e);
        } catch (Exception e1) {
            log.error("unknown Exception", e1);
            return ResultUtils.unknownFail();
        }
    }

    @Override
    public Result<InstContractAllFxAndSettleResponse> queryContractAllFxAndSettleInfo(InstInfoQueryRequest request) {
        return instContractSceneQueryService.queryContractAllFxAndSettleInfo(request);
    }

    @Override
    public Result<InstRiskFeeInfoResponse> queryRiskFeeInfo(InstRiskFeeQueryRequest request) {
        return instContractSceneQueryService.queryRiskFeeInfo(request);
    }

    @Override
    public Result<InstDisputeFeeResponse> queryDisputeFeeInfo(InstInfoQueryRequest request) {
        return instContractSceneQueryService.queryDisputeFeeInfo(request);
    }

    @Override
    public Result<InstTechnicalServiceFeeResponse> queryTechnicalServiceFeeInfo(InstTechnicalServiceFeeQueryRequest request) {
        return instContractSceneQueryService.queryTechnicalServiceFeeInfo(request);
    }

    @Override
    public Result<InstVirtualAccountFeeResponse> queryVirtualAccountFeeInfo(InstInfoQueryRequest request) {
        return instContractSceneQueryService.queryVirtualAccountFeeInfo(request);
    }

    @Override
    public Result<InstAccountingTypeResponse> queryAccountingType(InstAccountingTypeQueryRequest request) {
        return instContractSceneQueryService.queryAccountingType(request);
    }

    @Override
    public Result<List<AccumulationResponse>> queryAccumulationInfo() {
        return ResultUtils.success(instContractQueryService.queryAllAccumulationFee());
    }

    /**
     * 机构信息完整性校验
     */
    @Override
    public Result<Boolean> instInfoVerify(InstInfoQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() -> {
            // 设置交易时间为当前
            request.setTransactionTime(System.currentTimeMillis());
            // 查询费用、结算信息
            Result<InstInfoQueryResponse> instInfoQueryResponseResult = queryFeeInfo(request);
            // 抛出原有异常信息
            if (!instInfoQueryResponseResult.isSuccess()){
                throw new BusinessException(instInfoQueryResponseResult.getCode(), instInfoQueryResponseResult.getMsg());
            }
            AssertUtil.isTrue(ObjectUtils.isNotEmpty(instInfoQueryResponseResult.getData()), ErrorCodeEnum.INST_CONTRACT_FEE_VALIDATE_ERROR.getCode(), "查询费用信息为空");
            return contractIntegrityValidator.validate(instInfoQueryResponseResult.getData());
        });
    }

    @Override
    public Result<InstContractBatchVerifyResponse> instContractBatchVerify(InstContractBatchVerifyRequest request) {
        return ExceptionUtils.commonTryCatch(() -> contractIntegrityBatchValidator.validate(request));
    }


    @Override
    public Result<InstContractMidQueryResponse> queryMidInfo(InstContractMidQueryRequest request) {
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getChannelMerchantCode()), "ERROR", "channelMerchantCode is empty");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getBizType()), "ERROR", "bizType is empty");
        return ExceptionUtils.commonTryCatch(() -> instContractQueryService.queryMidInfo(request));
    }

    private void paramsCheck(InstInfoQueryRequest request) {

        AssertUtil.isTrue(!(StringUtil.isEmpty(request.getTargetOrg()) && StringUtil.isEmpty(request.getCardOrg()))
                , "ERROR", "目标机构和卡组不能全部为空");

        AssertUtil.isTrue("I".equals(request.getBizType()) || "O".equals(request.getBizType())
                , "ERROR", "BizType 只能为I/O");

        // 把交易链路兜底类数据替换掉
        if (StringUtil.isNotEmpty(request.getTargetOrg())) {
            request.setTargetOrg(request.getTargetOrg().replace("*", ""));
        }

        if (StringUtil.isNotEmpty(request.getCardOrg())) {
            request.setCardOrg(request.getCardOrg().replace("CARDPAY", ""));
        }

        if (StringUtil.isEmpty(request.getMcc())) {
            request.setMcc("UNKNOWN");
        }

    }

    private void assemblerResponse(InstContractFeeItem feeItem, InstContractSettlementItem settleItem, InstContractVersionInfo contractInfo,  InstInfoQueryResponse response) {

        if (feeItem != null) {
            Map<FeeTypeEnum, InstFeeConfig> feeConfigs = feeItem.getFeeConfigs();
            AssertUtil.notEmpty(feeConfigs, "Error", "feeConfigs is empty!");
            // 如果没有争议费用，需要补全
            if(!feeConfigs.containsKey(FeeTypeEnum.CHARGEBACK)){
                feeConfigs.put(FeeTypeEnum.CHARGEBACK, new InstFeeConfig());
            }

            //1. 费用
            Map<String, FeeConfig> feeConfigMap = new HashMap<>(feeConfigs.size());
            feeConfigs.forEach((key, value) -> {
                FeeConfig feeConfig = instContractRequestAssembler.convertInstFee2Response(value);
                if (FeeTypeEnum.TRADE == key) {
                    if (feeItem.getAccumulationCycle() != null) {
                        feeConfig.setAccumulationCycle(feeItem.getAccumulationCycle().name());
                    }
                    if (feeItem.getAccumulationType() != null) {
                        feeConfig.setAccumulationType(feeItem.getAccumulationType().name());
                    }
                    if (feeItem.getAccumulationMethod() != null) {
                        feeConfig.setAccumulationMethod(feeItem.getAccumulationMethod().name());
                    }
                    if (feeItem.getAccumulationRange() != null) {
                        feeConfig.setAccumulationRange(feeItem.getAccumulationRange().name());
                    }
                    if (feeItem.getAccumulationDeductTime() != null) {
                        feeConfig.setAccumulationDeductTime(feeItem.getAccumulationDeductTime().name());
                    }

                    Optional.ofNullable(feeItem.getAccumulationMode()).ifPresent(feeConfig::setAccumulationMode);

                    // 封顶、保底费用币种
                    if(StringUtils.isBlank(feeConfig.getPercentMaxAmountCurrency())){
                        feeConfig.setPercentMaxAmountCurrency(feeItem.getPayCurrency());
                    }
                    if(StringUtils.isBlank(feeConfig.getPercentMinAmountCurrency())){
                        feeConfig.setPercentMinAmountCurrency(feeItem.getPayCurrency());
                    }
                    feeConfig.setAccumulationJoin(feeItem.getAccumulationJoin());
                    feeConfig.setAccumulationKey(feeItem.getAccumulationKey());
                    feeConfig.setAccumulation(StringUtil.isNotEmpty(feeItem.getAccumulationKey()));
                }
                feeConfigMap.put(key.name(), feeConfig);

            });
            response.setFeeConfigMap(feeConfigMap);
            response.setRoundingScale(feeItem.getRoundingScale());
            response.setRoundingMode(feeItem.getRoundingMode().name());
            response.setInstContractFeeItemNo(feeItem.getInstContractFeeItemNo());

            //2. 税
            response.setTaxConfig(instContractRequestAssembler.convertInstTax2ResponseList(feeItem.getTaxConfigs()));
        }

        //3. 结算
        if (settleItem != null) {
            response.setSettlementConfig(instContractRequestAssembler.convertInstSettle2Response(settleItem));
            settleInfoConvertor.convertSettleInfo(response.getSettlementConfig());
        }

        //4. 换汇信息
        response.setFxConfig(instContractRequestAssembler.convertInstFx2Response(feeItem));

        //5. 合同信息
        response.setInstCode(contractInfo.getInstCode());
        response.setEntity(contractInfo.getEntity());

    }


    private void channelErrInfoNotify(InstContractStandardProduct standardProduct, String instCode, String mid, String ccy) {
        CompletableFuture.supplyAsync(() -> {
            String channelCodeToErrCodesKey = Joiner.on(StrPool.COLON).join(CommonConstants.INST_NEW_QUERY_ERROR, DateUtil.formatCnDate(new Date()));
            String key = Joiner.on(StrPool.COLON).join(instCode, standardProduct.getPaymentMethodType()
                    , standardProduct.getTargetOrg(), standardProduct.getCardOrg(), mid, ccy);
            stringRedisTemplate.opsForHash().put(channelCodeToErrCodesKey, key, String.valueOf(System.currentTimeMillis()));
            return true;
        }).exceptionally(e -> {
            log.warn("channelErrInfoNotify errNotify exception:", e);
            return true;
        });
    }

}
