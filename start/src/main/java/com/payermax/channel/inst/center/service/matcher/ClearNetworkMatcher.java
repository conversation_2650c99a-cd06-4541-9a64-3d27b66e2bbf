package com.payermax.channel.inst.center.service.matcher;

import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@DependsOn("paymentCcyMatcher")
public class ClearNetworkMatcher extends AbstractContractMatcher {

    @Override
    protected boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        boolean allEmpty = true;
        for (InstContractFeeItem item : feeItems) {
            if (StringUtil.isNotEmpty(item.getClearNetwork())) {
                allEmpty = false;
                break;
            }
        }
        return allEmpty;
    }

    @Override
    protected List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String clearingNetwork = funnelMatchItem.getClearingNetwork();
        return feeItems.stream().filter(item -> StringUtil.isNotEmpty(clearingNetwork) &&
                clearingNetwork.equals(item.getClearNetwork())).collect(Collectors.toList());
    }

    @Override
    protected List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        return feeItems.stream().filter(item -> StringUtil.isEmpty(item.getClearNetwork())).collect(Collectors.toList());
    }
}
