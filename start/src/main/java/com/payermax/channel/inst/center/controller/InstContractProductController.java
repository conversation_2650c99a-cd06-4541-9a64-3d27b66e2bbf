package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.request.InstContractProductQueryRequest;
import com.payermax.channel.inst.center.app.response.InstContractProductQueryVO;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.cache.InstContractCacheClear;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractStandardProductPO;
import com.payermax.channel.inst.center.service.InstContractProductService;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/18
 * @DESC
 */
@Api(tags = "机构合同产品API")
@RestController
@Slf4j
@RequestMapping("instCenter/contract/product")
@AllArgsConstructor
public class InstContractProductController {


    private final InstContractProductService contractProductService;

    /**
     * 查询产品列表
     */
    @PostMapping("queryProductList")
    @DigestLog(isRecord = true)
    public Result<List<InstContractProductQueryVO>> queryProductList(@Validated @RequestBody InstContractProductQueryRequest request){
        return ResultUtil.success(contractProductService.queryProductList(request));
    }

    /**
     * 查询标准产品列表
     */
    @PostMapping("queryStandardProductList")
    @DigestLog(isRecord = true)
    public Result<List<InstContractStandardProductPO>> queryStandardProductList(@Validated @RequestBody InstContractProductQueryRequest request){
        return ResultUtil.success(contractProductService.queryStandardProductList(request));
    }

    /**
     * 修改标准化产品信息
     */
    @PostMapping("standardProductModified")
    @DigestLog(isRecord = true)
    @InstContractCacheClear(cacheName = CacheEnum.INST_NEW_CONTRACT_STANDARD_PROD)
    public Result<Boolean> standardProductModified(@RequestHeader("shareId") String shareId, @RequestBody InstContractStandardProductPO request){
        return ResultUtil.success(contractProductService.standardProductModified(shareId, request));
    }

    /**
     * 添加标准化产品
     */
    @PostMapping("standardProductAdd")
    @DigestLog(isRecord = true)
    @InstContractCacheClear(cacheName = CacheEnum.INST_NEW_CONTRACT_STANDARD_PROD)
    public Result<String> standardProductAdd(@RequestHeader("shareId") String shareId, @RequestBody InstContractStandardProductPO request){
        return ResultUtil.success(contractProductService.standardProductAdd(shareId, request));
    }

    /**
     * 删除标准化产品
     */
    @PostMapping("standardProductDel")
    @DigestLog(isRecord = true)
    public Result<String> standardProductDel(@RequestHeader("shareId") String shareId, @RequestBody InstContractStandardProductPO request){
        return ResultUtil.success(contractProductService.standardProductDel(shareId, request));
    }
}
