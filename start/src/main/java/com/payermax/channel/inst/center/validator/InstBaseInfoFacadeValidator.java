package com.payermax.channel.inst.center.validator;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.facade.enums.UseTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023/8/28  10:53 AM
 */
public class InstBaseInfoFacadeValidator {

    public static void validateInstAccountReqUseType(String useTypeReq) {
        if (StringUtils.isBlank(useTypeReq)) {
            return;
        }

        UseTypeEnum[] useTypeArr = UseTypeEnum.values();
        if (useTypeArr.length == 0) {
            throw new BizException(ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR, "useType枚举列表为空，请求useType不为空！！！");
        }

        List<String> useTypeList = Arrays.stream(useTypeArr).map(UseTypeEnum::name).collect(Collectors.toList());
        if (!useTypeList.contains(useTypeReq)) {
            throw new BizException(ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR, "请求useType不在枚举列表！！！");
        }
    }
}
