package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.manage.SubAccountCompensateManage;
import com.payermax.channel.inst.center.facade.api.FundsAccountFacade;
import com.payermax.channel.inst.center.facade.request.*;
import com.payermax.channel.inst.center.facade.response.*;
import com.payermax.channel.inst.center.task.BatchSubAccountCompensateJobHandler;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.logging.log4j.core.config.plugins.validation.constraints.Required;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR> at 2022/10/8 20:39
 **/
@Api(tags = "字典API")
@RestController
@RequestMapping("instCenter/fundsAccountFacade")
public class FundsAccountFacadeController {

    @Autowired
    FundsAccountFacade facade;
    @Autowired
    SubAccountCompensateManage subAccountCompensateManager;
    @Autowired
    BatchSubAccountCompensateJobHandler compensateJobHandler;

    @ApiOperation(value = "机构账号查询", notes = "机构账号查询")
    @PostMapping("queryFundsAccounts")
    @DigestLog(isRecord = true)
    public Result<List<QueryAccountsResponse>> queryFundsAccounts(@RequestBody QueryAccountsRequest request) {
        Result<List<QueryAccountsResponse>> result = facade.queryFundsAccounts(request);
        return result;
    }

    @ApiOperation(value = "创建机构VA账号", notes = "创建机构VA账号")
    @PostMapping("createSubAccount")
    @DigestLog(isRecord = true)
    public Result<CreateSubAccountResponse> createSubAccount(@RequestBody CreateSubAccountRequest request) {
        Result<CreateSubAccountResponse> result = facade.createSubAccount(request);
        return result;
    }

    @ApiOperation(value = "关闭机构VA账号", notes = "关闭机构VA账号")
    @PostMapping("closeSubAccount")
    @DigestLog(isRecord = true)
    public Result<CloseSubAccountResponse> closeSubAccount(@RequestBody @NotBlank String businessKey) {
        return facade.closeSubAccount(businessKey);
    }

    @ApiOperation(value = "根据机构主体、账号号码、子账号号码（可选）查询账号详情", notes = "根据机构主体、账号号码、子账号号码（可选）查询账号详情")
    @PostMapping("queryAccountDetail")
    @DigestLog(isRecord = true)
    public Result<QueryAccountDetailResponse> queryAccountDetail(@RequestBody QueryAccountDetailRequest request) {
        Result<QueryAccountDetailResponse> result = facade.queryAccountDetail(request);
        return result;
    }

    @ApiOperation(value = "根据国家、币种、机构主体、账号号码、子账号号码（可选），子账号号码B（可选）查询账号详情", notes = "根据国家、币种、机构主体、账号号码、子账号号码（可选），子账号号码B（可选）查询账号详情")
    @PostMapping("queryAccountDetailInfo")
    @DigestLog(isRecord = true)
    public Result<QueryAccountDetailResponse> queryAccountDetailInfo(@RequestBody QueryAccountDetailInfoRequest request) {
        Result<QueryAccountDetailResponse> result = facade.queryAccountDetailInfo(request);
        return result;
    }

    @ApiOperation(value = "根据机构主体、账号号码、子账号号码（可选）查询账号详情", notes = "根据机构主体、账号号码、子账号号码（可选）查询账号详情")
    @PostMapping("queryAccountDetailById")
    @DigestLog(isRecord = true)
    public Result<QueryAccountDetailByIdResponse> queryAccountDetailById(@RequestBody QueryAccountDetailByIdRequest request) {
        Result<QueryAccountDetailByIdResponse> result = facade.queryAccountDetailById(request);
        return result;
    }

    @ApiOperation(value = "根据子级账号ID查询详情", notes = "根据子级账号ID查询详情")
    @PostMapping("querySubAccountDetailById")
    @DigestLog(isRecord = true)
    public Result<QuerySubAccountDetailByIdResponse> querySubAccountDetailById(@RequestBody QuerySubAccountDetailByIdRequest request) {
        Result<QuerySubAccountDetailByIdResponse> result = facade.querySubAccountDetailById(request);
        return result;
    }

    @ApiOperation(value = "补偿MQ", notes = "补偿MQ")
    @PostMapping("compensateSubAccountNoMQ")
    @DigestLog(isRecord = true)
    public Result<Integer> compensateSubAccountNoMq(@RequestBody List<String> subAccountIds) {
        return facade.compensateSubAccountNoMq(subAccountIds);
    }
}
