package com.payermax.channel.inst.center.facade;

import cn.hutool.core.text.StrPool;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.app.assembler.ReqDtoAssembler;
import com.payermax.channel.inst.center.app.assembler.RespVoAssembler;
import com.payermax.channel.inst.center.app.manage.InstFxSettleManage;
import com.payermax.channel.inst.center.app.request.InstFxSettleReqDTO;
import com.payermax.channel.inst.center.app.request.context.InstFxSettleQueryErrorInfoContext;
import com.payermax.channel.inst.center.app.response.InstFxSettleInfoVO;
import com.payermax.channel.inst.center.app.response.InstSettleInfoVO;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.facade.api.ChannelFxInfoQueryFacade;
import com.payermax.channel.inst.center.facade.request.FxInfoQueryRequest;
import com.payermax.channel.inst.center.facade.response.FxInfoQueryResponse;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.common.lang.model.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.Optional.ofNullable;

/**
 * <AUTHOR> tracy
 * @version 2022-10-19 9:01 PM
 */
@DubboService(version = "${dubbo.provider.version}")
@Slf4j
public class ChannelFxInfoQueryFacadeImpl implements ChannelFxInfoQueryFacade {

    public static final String INST_FX_SETTLE_REDIS_PREFIX = "instCenter:fxSettle";

    @NacosValue(value = "${channel.fxInfo.query.oldVersion:true}",autoRefreshed = true)
    public boolean isUseOldVersionQuery;

    @NacosValue(value = "${channel.fxInfo.query.newVersion:true}",autoRefreshed = true)
    public boolean isUseNewVersionQuery;

    @NacosValue(value = "${channel.fxInfo.query.DSLVersion:true}",autoRefreshed = true)
    public boolean isUseDSLVersionQuery;

    @NacosValue(value = "${channel.fxInfo.query.errIsNotify:false}",autoRefreshed = true)
    public boolean errIsNotify;

    @Resource
    private ReqDtoAssembler reqDtoAssembler;

    @Resource
    private InstFxSettleManage instFxSettleManage;

    @Resource
    private RespVoAssembler respVoAssembler;

    @Resource
    private DingAlertClient dingAlertClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Validated
    public Result<FxInfoQueryResponse> queryChanelFxInfo(FxInfoQueryRequest request) {
        List<CompletableFuture<InstFxSettleInfoVO>> allCompletableFutureList = new ArrayList<>();
        InstFxSettleReqDTO reqDTO = reqDtoAssembler.toInstFxReqDTO(request);
        log.info("queryChanelFxInfo isUseOldVersionQuery:{},isUseNewVersionQuery:{}",isUseOldVersionQuery,isUseNewVersionQuery);
        try {
            //老版本查询
            this.useOldVersionQuery(reqDTO,allCompletableFutureList);
            //新版本查询
            this.useNewVersionQuery(reqDTO,allCompletableFutureList);
            //DSL版本查询
            this.useDSLVersionQuery(reqDTO,allCompletableFutureList);

            //获取查询结果
            List<InstFxSettleInfoVO> instFxSettleInfoVOList = allCompletableFutureList.stream()
                    .map(CompletableFuture::join).collect(Collectors.toList());
            log.info("queryChanelFxInfo query result:{}",JSONObject.toJSONString(instFxSettleInfoVOList));
            //新老查询结果对比不一致通知
            this.asyncCheckFxSettleQueryResult(reqDTO,instFxSettleInfoVOList);
            InstFxSettleInfoVO instFxSettleInfoVO = this.getQueryResult(instFxSettleInfoVOList);
            SettleInfoVo settleInfoVo = respVoAssembler.toSettleInfoVO(instFxSettleInfoVO.getInstSettleInfoVO());
            FxInfoQueryResponse response = new FxInfoQueryResponse();
            response.setExchangeType(instFxSettleInfoVO.getExchangeType());
            response.setSettleInfoVo(settleInfoVo);
            log.info("queryChanelFxInfo response:{}", JSONObject.toJSONString(response));
            return ResultUtils.success(response);
        } catch (Exception e) {
            log.error("queryChanelFxInfo catch error!", e);
            return ResultUtils.unknownFail();
        } finally {
            log.info("queryChanelFxInfo errIsNotify:{}",errIsNotify);
            if(errIsNotify){
                this.channelErrInfoNotify(reqDTO);
            }
        }
    }

    private void channelErrInfoNotify(InstFxSettleReqDTO reqDTO) {
        CompletableFuture.supplyAsync(() -> {
                String channelCodeToErrCodesKey = Joiner.on(StrPool.COLON).join(INST_FX_SETTLE_REDIS_PREFIX, reqDTO.getChanelCode());
                Set<String> errorCodes = stringRedisTemplate.opsForSet().members(channelCodeToErrCodesKey);
                StringBuilder message = new StringBuilder();
                if(CollectionUtils.isNotEmpty(errorCodes)){
                    String title = "【" + CommonConstants.FX_SETTLE_QUERY + "】" ;
                    String params = getFxSettleNotifyParams(reqDTO);
                    message.append(params);
                    for (String errorCode : errorCodes) {
                        String key = Joiner.on(StrPool.COLON).join(INST_FX_SETTLE_REDIS_PREFIX, errorCode,reqDTO.getChanelCode());
                        String fxSettleErrorInfo = stringRedisTemplate.opsForValue().get(key);
                        InstFxSettleQueryErrorInfoContext context = JSONObject.parseObject(fxSettleErrorInfo, InstFxSettleQueryErrorInfoContext.class);
                        message.append(String.format("\n异常信息\n- 错误码: [%s]\n- 错误信息：[%s]",errorCode,context.getErrMsg()));
                    }
                    log.info("渠道查询结算换汇信息异常告警信息：{}",message);
                    dingAlertClient.sendMsg(CommonConstants.PUSH_FX_SETTLE_QUERY_RESULT_GROUP, title, message.toString());
                }
                return true;
             }).exceptionally(e -> {
                log.warn("queryChanelFxInfo errNotify exception:",e);
                return true;
            });
    }

    /**
     * 获取查询结果
     * 1、新老开关同时开启时，以老版本结果为准（观察阶段）
     * 2、新版本结果稳定后，关闭老查询开关，以新版本结果为准（稳定后）
     * @param instFxSettleInfoVOList
     * @return
     */
    private InstFxSettleInfoVO getQueryResult(List<InstFxSettleInfoVO> instFxSettleInfoVOList) {
        InstFxSettleInfoVO instFxSettleInfoVO = new InstFxSettleInfoVO();
        if(isUseNewVersionQuery){
            instFxSettleInfoVO = instFxSettleInfoVOList.stream().filter(obj -> obj.getIsUseNewVersionQuery()).findFirst().get(); // NO_CHECK
        }
        if(isUseOldVersionQuery){
            instFxSettleInfoVO = instFxSettleInfoVOList.stream().filter(obj -> !obj.getIsUseNewVersionQuery()).findFirst().get(); // NO_CHECK
        }
        return instFxSettleInfoVO;
    }

    /**
     * 新合同版本基于DSL的结算换汇查询
     * @param reqDTO
     * @param allCompletableFutureList
     */
    private void useDSLVersionQuery(InstFxSettleReqDTO reqDTO, List<CompletableFuture<InstFxSettleInfoVO>> allCompletableFutureList) {
//        if(isUseDSLVersionQuery){
//            CompletableFuture<InstFxSettleInfoVO> instFxSettleInfoVOCompletableFuture = CompletableFuture.supplyAsync(() -> {
//                InstFxSettleInfoVO fxSettleInfoVO = instFxSettleManage.queryFxSettleInfoNewV3(reqDTO);
//                fxSettleInfoVO.setIsUseNewVersionQuery(Boolean.TRUE);
//                log.info("queryChanelFxInfo newVersion query fxSettleInfoVO:{}",JSONObject.toJSONString(fxSettleInfoVO));
//                return fxSettleInfoVO;
//            }).exceptionally(e->{
//                log.warn("queryChanelFxInfo newVersion query error:",e);
//                InstFxSettleInfoVO instFxSettleInfoVO = new InstFxSettleInfoVO();
//                instFxSettleInfoVO.setIsUseNewVersionQuery(Boolean.TRUE);
//                return instFxSettleInfoVO;
//            });
//            allCompletableFutureList.add(instFxSettleInfoVOCompletableFuture);
//        }
    }

    /**
     * 新版本结算换汇查询
     * @param reqDTO
     * @param allCompletableFutureList
     */
    private void useNewVersionQuery(InstFxSettleReqDTO reqDTO, List<CompletableFuture<InstFxSettleInfoVO>> allCompletableFutureList) {
        if(isUseNewVersionQuery){
            CompletableFuture<InstFxSettleInfoVO> instFxSettleInfoVOCompletableFuture = CompletableFuture.supplyAsync(() -> {
                InstFxSettleInfoVO fxSettleInfoVO = instFxSettleManage.queryFxSettleInfoV2(reqDTO);
                fxSettleInfoVO.setIsUseNewVersionQuery(Boolean.TRUE);
                log.info("queryChanelFxInfo newVersion query fxSettleInfoVO:{}",JSONObject.toJSONString(fxSettleInfoVO));
                return fxSettleInfoVO;
            }).exceptionally(e->{
                log.warn("queryChanelFxInfo newVersion query error:",e);
                InstFxSettleInfoVO instFxSettleInfoVO = new InstFxSettleInfoVO();
                instFxSettleInfoVO.setIsUseNewVersionQuery(Boolean.TRUE);
                return instFxSettleInfoVO;
            });
            allCompletableFutureList.add(instFxSettleInfoVOCompletableFuture);
        }
    }

    /**
     * 老版本结算换汇查询
     * @param reqDTO
     * @param allCompletableFutureList
     */
    private void useOldVersionQuery(InstFxSettleReqDTO reqDTO, List<CompletableFuture<InstFxSettleInfoVO>> allCompletableFutureList) {
        if(isUseOldVersionQuery){
            CompletableFuture<InstFxSettleInfoVO> instFxSettleInfoVOCompletableFuture = CompletableFuture.supplyAsync(() -> {
                InstFxSettleInfoVO fxSettleInfoVO = instFxSettleManage.queryFxSettleInfo(reqDTO);
                log.info("queryChanelFxInfo oldVersion query fxSettleInfoVO:{}",JSONObject.toJSONString(fxSettleInfoVO));
                return fxSettleInfoVO;
            });
            allCompletableFutureList.add(instFxSettleInfoVOCompletableFuture);
        }
    }

    /**
     * 新老版本查询结果对比通知
     * @param instFxSettleInfoVOList
     */
    public void asyncCheckFxSettleQueryResult(InstFxSettleReqDTO reqDTO,List<InstFxSettleInfoVO> instFxSettleInfoVOList){
        if(isUseOldVersionQuery && isUseNewVersionQuery){
            CompletableFuture.supplyAsync(() -> {
                InstFxSettleInfoVO instFxSettleInfoOldVersionVO = instFxSettleInfoVOList.stream().filter(o -> !o.getIsUseNewVersionQuery()).findFirst().orElse(new InstFxSettleInfoVO()); // NO_CHECK
                InstFxSettleInfoVO instFxSettleInfoNewVersionVO = instFxSettleInfoVOList.stream().filter(o -> o.getIsUseNewVersionQuery()).findFirst().orElse(new InstFxSettleInfoVO()); // NO_CHECK
                InstSettleInfoVO instSettleInfoOldVersionVO = ofNullable(instFxSettleInfoOldVersionVO.getInstSettleInfoVO()).orElse(new InstSettleInfoVO());
                InstSettleInfoVO instSettleInfoNewVersionVO = ofNullable(instFxSettleInfoNewVersionVO.getInstSettleInfoVO()).orElse(new InstSettleInfoVO());
                //新老查询结果不一致时钉钉告警通知
                if(!StringUtils.equals(instFxSettleInfoOldVersionVO.getExchangeType(),instFxSettleInfoNewVersionVO.getExchangeType())
                    || !instSettleInfoOldVersionVO.equals(instSettleInfoNewVersionVO)){
                    String title = "【" + CommonConstants.FX_SETTLE_QUERY + "】" ;
                    //查询参数
                    String params = getFxSettleNotifyParams(reqDTO);
                    //查询结果
                    String message = params.concat(String.format("\n查询结果[v1][v2]\n- 换汇类型：[%s][%s]\n- 结算方式：[%s][%s]\n- 结算模式：[%s][%s]\n- 结算周期：[%s][%s]\n- 结算单位：[%s][%s]\n- 周期限制：[%s][%s]\n- 结算节假日国家：[%s][%s]\n- 节假日处理方式：[%s][%s]\n- 结算币种：[%s][%s]\n"
                            , instFxSettleInfoOldVersionVO.getExchangeType(),instFxSettleInfoNewVersionVO.getExchangeType(),
                            instSettleInfoOldVersionVO.getSettleMethod(),instSettleInfoNewVersionVO.getSettleMethod(),
                            instSettleInfoOldVersionVO.getSettleMode(),instSettleInfoNewVersionVO.getSettleMode(),
                            instSettleInfoOldVersionVO.getSettleCycle(),instSettleInfoNewVersionVO.getSettleCycle(),
                            instSettleInfoOldVersionVO.getSettleCycleUnit(),instSettleInfoNewVersionVO.getSettleCycleUnit(),
                            instSettleInfoOldVersionVO.getSettleCycleOtherLimit(),instSettleInfoNewVersionVO.getSettleCycleOtherLimit(),
                            instSettleInfoOldVersionVO.getSettleHolidayCountry(),instSettleInfoNewVersionVO.getSettleHolidayCountry(),
                            instSettleInfoOldVersionVO.getSettleForHoliday(),instSettleInfoNewVersionVO.getSettleForHoliday(),
                            instSettleInfoOldVersionVO.getSettleCurrency(),instSettleInfoNewVersionVO.getSettleCurrency()));
                    log.info("新老查询结果不一致告警信息：{}",message);
                    dingAlertClient.sendMsg(CommonConstants.PUSH_FX_SETTLE_QUERY_RESULT_GROUP, title, message);
                }
                return true;
            }).exceptionally(e -> {
                log.warn("queryChanelFxInfo asyncCheckFxSettleQueryResult exception:",e);
                return true;
            });
        }
    }

    private String getFxSettleNotifyParams(InstFxSettleReqDTO reqDTO) {
        return String.format("\n查询参数 \n- channelCode:[%s]\n- channelMethodCode:[%s]\n- channelMerchantCode:[%s]\n- entity:[%s]\n- paymentMethodType:[%s]\n- targetOrg:[%s]\n- cardOrg:[%s]\n- paymentCurrency:[%s]\n",
                reqDTO.getChanelCode(),reqDTO.getChannelMethodCode(),reqDTO.getChannelMerchantCode(),
                reqDTO.getEntity(),reqDTO.getPaymentMethodType(),reqDTO.getTargetOrg(),
                reqDTO.getCardOrg(),reqDTO.getPaymentCcy());
    }
}
