package com.payermax.channel.inst.center.aspect;

import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.utils.ValidationUtils;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.ushareit.fintech.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 全局异常拦截
 *
 * <AUTHOR>
 * @date 2021/12/14 13:37
 */
@Aspect
@Component
@Slf4j
public class GlobalExceptionAspect {

    @Pointcut(value = "execution(* com.payermax.channel.inst.center.controller..*.*(..))")
    public void anyDubboProvider() {
    }

    @Pointcut(value = "execution(* com.payermax.channel.inst.center.controller.CommonController.upLoadApi(..))")
    public void fileApi() {
    }

    @Pointcut(value = "execution(* com.payermax.channel.inst.center.controller..*.*(..)) && @annotation(com.payermax.channel.inst.center.aspect.AroundLog)")
    public void anyControllerAroundLog() {
    }

    @Pointcut(value = "((anyDubboProvider() || anyControllerAroundLog())&&!fileApi())")
    public void pointCut() {
    }

    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        Object request = Optional.ofNullable(args).orElse(new Object[]{});

        Object result;
        try {
            log.info("Facade execute start.【RequestBody】:{}", JsonUtils.toJson(request));
            ValidationUtils.notNullCheck(request, "input is null");
            ValidationUtils.validate(request);

            result = joinPoint.proceed();
        } catch (Exception e) {
            result = this.exceptionHandler(joinPoint, e);
        }

        log.info("Facade execute end.【ResponseBody】:{}", JsonUtils.toJson(result));
        return result;
    }

    /**
     * 全局异常捕捉处理
     *
     * @param e 异常对象
     * @return Result
     */
    private Result exceptionHandler(ProceedingJoinPoint joinPoint, Exception e) {
        if (e instanceof BusinessException) {
            BusinessException t = (BusinessException) e;
            return ResultUtil.fail(t.getErrCode(), t.getMessage());
        }
        log.error("{} invoke catch exception! ", this.currentAbsoluteName(joinPoint), e);
        if (e instanceof IllegalArgumentException) {
            return ResultUtil.fail(ErrorCodeEnum.PARAMETER_INVALID.getCode(), e.getMessage());
        } else {
            return ResultUtil.fail(ErrorCodeEnum.INNER_ERROR.getCode(), ErrorCodeEnum.INNER_ERROR.getMsg());
        }
    }

    /**
     * 组装当前执行类全限定路径
     *
     * @param joinPoint
     * @return String
     */
    private String currentAbsoluteName(ProceedingJoinPoint joinPoint) {
        return joinPoint.getSignature().getDeclaringTypeName().concat(".")
                .concat(((MethodInvocationProceedingJoinPoint) joinPoint).getSignature().getName());
    }

}
