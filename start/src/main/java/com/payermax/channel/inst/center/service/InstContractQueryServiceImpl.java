package com.payermax.channel.inst.center.service;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.assembler.contract.InstContractInfoAssembler;
import com.payermax.channel.inst.center.app.assembler.po.ContractVersionPOAssembler;
import com.payermax.channel.inst.center.app.manage.contract.InstContractQueryService;
import com.payermax.channel.inst.center.common.enums.LogicKeyEnum;
import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.facade.request.contract.AccumulationResponse;
import com.payermax.channel.inst.center.facade.request.contract.InstContractMidQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.response.contract.InstContractMidQueryResponse;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractBaseInfoCacheManager;
import com.payermax.channel.inst.center.infrastructure.cache.item.InstNewContractCacheManager;
import com.payermax.channel.inst.center.infrastructure.repository.mapper.InstContractFeeItemMapper;
import com.payermax.channel.inst.center.infrastructure.repository.po.AccumulationResponsePO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractMidMappingRepository;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> tracy
 * @version 2023-08-11 5:43 PM
 */
@Service
@Setter
@Slf4j
public class InstContractQueryServiceImpl implements InstContractQueryService {


    /**
     * 目标机构/卡组兜底通配符
     */
    public static final String CARD_ORG_WILDCARD = "CARDPAY";
    public static final String TARGET_ORG_WILDCARD = "*";

    @Resource
    private InstNewContractCacheManager instNewContractCacheManager;

    @Resource
    private InstNewContractBaseInfoCacheManager contractBaseInfoCacheManager;

    @Resource
    private ContractVersionPOAssembler contractVersionPOAssembler;

    @Resource
    private InstContractMidMappingRepository instContractMidMappingRepository;

    @Resource
    private FunnelModelMatchService funnelModelMatchService;

    @Resource
    private InstContractFeeItemMapper instContractFeeItemMapper;




    /**
     * 根据渠道商户号获取合同版本,包括所有原始和标准合同
     */
    @Override
    public InstContractVersionInfo queryActiveContract(String mid, String bizType, long transactionTime) {
        InstContractMidMappingPO mappingPO = instContractMidMappingRepository.queryMappingByMid(mid, bizType);
        AssertUtil.notNull(mappingPO, "ERROR", "mid+bizType not exist! [" + mid + "] [" + bizType + "]");
        String contractNo = mappingPO.getContractNo();
        AssertUtil.notEmpty(contractNo, "ERROR", "mid+bizType has no contract! [" + mid + "] [" + bizType + "]");
        InstContractVersionInfoPO contractVersionInfoPO = instNewContractCacheManager.getEffectiveInstVersionContract(contractNo, transactionTime);
        AssertUtil.notNull(contractVersionInfoPO, "ERROR", "未找到交易时间内的生效合同！" + new Date(transactionTime));
        InstContractVersionInfo versionInfo = contractVersionPOAssembler.convertVersionInfoDomain(contractVersionInfoPO);
        versionInfo.setInstCode(mappingPO.getInstCode());
        versionInfo.setEntity(mappingPO.getEntity());
        return versionInfo;
    }

    /**
     * 根据 bizType、instCode、entity查询合同版本
     */
    @Override
    public InstContractVersionInfo queryActiveContract(String bizType, String instCode, String entity, long transactionTime) {
        log.info("{}/{}/{}", bizType, instCode, entity);
        // 查询合约
        InstContractBaseInfoPO contractInfo = contractBaseInfoCacheManager.getContractInfo(bizType, instCode, entity);
        // 查询合约版本
        InstContractVersionInfoPO versionInfoPo = instNewContractCacheManager.getEffectiveInstVersionContract(contractInfo.getContractNo(), transactionTime);
        log.info("{}",versionInfoPo);
        AssertUtil.notNull(versionInfoPo, "ERROR", "未找到交易时间内的生效合同！" + new Date(transactionTime));
        InstContractVersionInfo versionInfo = contractVersionPOAssembler.convertVersionInfoDomain(versionInfoPo);
        versionInfo.setInstCode(contractInfo.getInstCode());
        return versionInfo;
    }

    @Override
    public InstContractVersionInfo queryLatestContract(String bizType, String instCode, String entity) {
        // 查询合约
        InstContractBaseInfoPO contractInfo = contractBaseInfoCacheManager.getContractInfo(bizType, instCode, entity);
        // 查询合约版本
        InstContractVersionInfoPO versionInfoPo = instNewContractCacheManager.getLatestEffectiveVersionContract(contractInfo.getContractNo());
        AssertUtil.notNull(versionInfoPo, "ERROR", "未找到最新合约");
        InstContractVersionInfo versionInfo = contractVersionPOAssembler.convertVersionInfoDomain(versionInfoPo);
        versionInfo.setInstCode(contractInfo.getInstCode());
        return versionInfo;
    }

    @Override
    public List<InstContractVersionInfo> queryInstActiveContract(String bizType, String instCode, long transactionTime) {
        // 查询合约
        List<InstContractBaseInfoPO> contractInfoList = contractBaseInfoCacheManager.getContractInfoList(bizType, instCode);

        // 查询合约版本
        return contractInfoList.stream().map( item -> {
            InstContractVersionInfoPO versionInfoPo = instNewContractCacheManager.getEffectiveInstVersionContract(item.getContractNo(), transactionTime);
            AssertUtil.notNull(versionInfoPo, "ERROR", "未找到交易时间内的生效合同！" + new Date(transactionTime));
            InstContractVersionInfo versionInfo = contractVersionPOAssembler.convertVersionInfoDomain(versionInfoPo);
            versionInfo.setInstCode(item.getInstCode());
            return versionInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 查询 MID 信息
     */
    public InstContractMidQueryResponse queryMidInfo(InstContractMidQueryRequest request) {
        return InstContractInfoAssembler.INSTANCE.po2Response(
                instContractMidMappingRepository.queryMappingByMid(request.getChannelMerchantCode(), request.getBizType()));
    }

    public InstContractStandardProduct queryStandardProduct(List<InstContractStandardProduct> standardProducts, String paymentMethodType, String targetOrg, String cardOrg) {
        AssertUtil.notEmpty(standardProducts, "ERROR", "合同绑定的标准产品为空");
        InstContractStandardProduct standardProduct = preciseMatchingStandardProduct(paymentMethodType, targetOrg, cardOrg, standardProducts);
        if (standardProduct == null) {
            standardProduct = fuzzyMatchingStandardProduct(paymentMethodType, targetOrg, cardOrg, standardProducts);
        }
        AssertUtil.notNull(standardProduct, "ERROR", "标准产品没找到");
        return standardProduct;
    }

    public List<InstContractStandardProduct> queryStandardProductForSettleQuery(List<InstContractStandardProduct> standardProducts, String paymentMethodType, String targetOrg, String cardOrg) {
        AssertUtil.notEmpty(standardProducts, "ERROR", "合同绑定的标准产品为空");
        //结算如果没有传目标机构和卡组,走大兜底
        if (StringUtil.isEmpty(targetOrg) && StringUtil.isEmpty(cardOrg)) {
            // 再为空就值用支付方式类型(因为绝大部分结算只和paymentMethodType+币种相关)
            //粗略匹配的时候,也就是接口完全传兜底,这个时候只要paymentMethodType匹配即可,取第一个
            List<InstContractStandardProduct> fuzzyProduct = standardProducts.stream()
                    .filter(item -> item.getPaymentMethodType().equalsIgnoreCase(paymentMethodType)).collect(Collectors.toList());
            AssertUtil.notEmpty(fuzzyProduct, "ERROR", "结算时--标准产品兜底没找到");
            return fuzzyProduct;
        } else {
            // 否则走正常模式
            InstContractStandardProduct standardProduct = preciseMatchingStandardProduct(paymentMethodType, targetOrg, cardOrg, standardProducts);
            if (standardProduct == null) {
                standardProduct = fuzzyMatchingStandardProduct(paymentMethodType, targetOrg, cardOrg, standardProducts);
            }
            AssertUtil.notNull(standardProduct, "ERROR", "标准产品没找到");
            List<InstContractStandardProduct> result = new ArrayList<>();
            result.add(standardProduct);
            return result;
        }
    }


    public InstContractOriginProduct queryOriginProductByNo(String originProductNo, InstContractVersionInfo versionInfo) {
        List<InstContractOriginProduct> originProducts = versionInfo.getOriginProducts();
        AssertUtil.notEmpty(originProducts, "ERROR", "originProducts 为空:" + originProductNo);
        InstContractOriginProduct originProduct = originProducts.stream().filter(item -> originProductNo.equals(item.getInstOriginProductNo())).findFirst().orElse(null); //NO_CHECK instOriginProductNo 为唯一键，不存在重复，所以直接取第一个
        AssertUtil.notNull(originProduct, "ERROR", "根据标准产品没找到原始产品");
        return originProduct;
    }

    public InstContractFeeItem queryFeeByOriProduct(InstContractOriginProduct originProduct, InstInfoQueryRequest request) {
        List<InstContractFeeItem> contractFeeItems = originProduct.getContractFeeItems();
        AssertUtil.notEmpty(contractFeeItems, "ERROR", "费用信息没找到 OriginProductNo:" + originProduct.getInstOriginProductNo());
        FunnelMatchItem funnelMatchItem = getFunnelMatchItem(request);
        InstContractFeeItem finalFeeItem = funnelModelMatchService.matchFee(funnelMatchItem, contractFeeItems);
        AssertUtil.notNull(finalFeeItem, "ERROR", "兜底配置都没找到 OriginProductNo:" + originProduct.getInstOriginProductNo() + " paymentCcy:" + funnelMatchItem.getPaymentCcy());
        return finalFeeItem;
    }

    private FunnelMatchItem getFunnelMatchItem(InstInfoQueryRequest request) {
        FunnelMatchItem funnelMatchItem = new FunnelMatchItem();
        funnelMatchItem.setPaymentCcy(request.getPayCurrency());
        funnelMatchItem.setMcc(request.getMcc());
        funnelMatchItem.setSubMerchantNo(request.getSubMerchantNo());
        funnelMatchItem.setChannelMerchantCode(request.getChannelMerchantCode());
        funnelMatchItem.setFundingSource(request.getFundingSource());
        funnelMatchItem.setTransactionCountry(request.getTransactionCountry());
        funnelMatchItem.setClearingNetwork(request.getClearingNetwork());
        funnelMatchItem.setFeeBearer(request.getFeeBearer());
        funnelMatchItem.setCustomerType(request.getCustomerType());
        funnelMatchItem.setCardType(request.getCardType());
        return funnelMatchItem;
    }

    public InstContractSettlementItem querySettleByOriProduct(InstContractOriginProduct originProduct, String paymentCcy, String channelMerchantCode) {
        List<InstContractSettlementItem> settlementItems = originProduct.getSettlementItems();
        AssertUtil.notEmpty(settlementItems, "ERROR", "结算信息没找到 OriginProductNo:" + originProduct.getInstOriginProductNo());

        // 先精准匹配一下
        InstContractSettlementItem finalSettleItem = preciseMatchingSettle(paymentCcy, channelMerchantCode, settlementItems);

        if (finalSettleItem == null) {
            // 模糊匹配,只要币种匹配就行。其他都是空的
            finalSettleItem = fuzzyMatchingSettle(paymentCcy, channelMerchantCode, settlementItems);
        }
        AssertUtil.notNull(finalSettleItem, "ERROR", "兜底配置都没找到 OriginProductNo:" + originProduct.getInstOriginProductNo() + " paymentCcy:" + paymentCcy);
        return finalSettleItem;
    }

    public List<AccumulationResponse> queryAllAccumulationFee() {

        List<AccumulationResponsePO> responsePOS = instContractFeeItemMapper.queryAllAccumulation();
        List<AccumulationResponse> responses = new ArrayList<>(responsePOS.size());
        responsePOS.forEach(item -> {
            AccumulationResponse response = contractVersionPOAssembler.convertAccumulationResponsePO(item);
            response.setFeeDeductCurrency(JSON.parseObject(item.getFeeConfig()).getJSONObject("TRADE").getString("feeDeductCurrency"));
            responses.add(response);
        });
        return responses;
    }

    /**
     * 模糊匹配.只要求币种匹配,其他可以是都为空的兜底配置
     */
    private InstContractSettlementItem fuzzyMatchingSettle(String paymentCcy, String channelMerchantCode, List<InstContractSettlementItem> settlementItems) {
        // 1. 先捞出来币种匹配
        List<InstContractSettlementItem> contractSettle = settlementItems.stream().filter(item -> paymentCcy.equals(item.getPayCurrency())).collect(Collectors.toList());

        //2. 过滤channelMerchantNo为空或者subMerchantNo匹配的
        contractSettle = contractSettle.stream().filter(item -> StringUtil.isEmpty(item.getChannelMerchantNo()) || item.getChannelMerchantNo().equals(channelMerchantCode)).collect(Collectors.toList());

        return contractSettle.stream().findFirst().orElse(null); //NO_CHECK 模糊匹配时，匹配到第一条结算信息则返回，符合预期
    }

    /**
     * 模糊匹配. 允许使用 target == '*' || cardOrg='CARDPAY' 兜底
     */
    protected InstContractStandardProduct fuzzyMatchingStandardProduct(String paymentMethodType, String targetOrg, String cardOrg, List<InstContractStandardProduct> standardProducts) {

        List<InstContractStandardProduct> standardProduct;

        //1.先找全兜底--两个都是空的时候
        standardProduct = standardProducts.stream().filter(item -> item.getPaymentMethodType().equalsIgnoreCase(paymentMethodType)
                && ("CARDPAY".equals(item.getCardOrg()) || "*".equals(item.getTargetOrg()))).collect(Collectors.toList());

        // 2.如果targetOrg有值,则必须要targetOrg='*'去兜底
        if (StringUtil.isNotEmpty(targetOrg)) {
            standardProduct = standardProduct.stream().filter(item -> item.getTargetOrg().equals("*")).collect(Collectors.toList());
        }

        // 3.如果cardOrg有值,cardOrg='CARDPAY'去兜底
        if (StringUtil.isNotEmpty(cardOrg)) {
            standardProduct = standardProduct.stream().filter(item -> "CARDPAY".equalsIgnoreCase(item.getCardOrg())).collect(Collectors.toList());
        }
        return standardProduct.stream().findFirst().orElse(null); //NO_CHECK 模糊匹配时，匹配到第一条标准产品信息则返回，符合预期
    }

    /**
     * 精准匹配,必须所有参数全部映射
     */
    private InstContractFeeItem preciseMatchingFee(String paymentCcy, String mcc, String subMerchantNo, String channelMerchantCode, List<InstContractFeeItem> contractFeeItems) {
        InstContractFeeItem finalFeeItem = null;
        for (InstContractFeeItem item : contractFeeItems) {
            if (!paymentCcy.equals(item.getPayCurrency())) {
                //币种一定要匹配
                continue;
            }
            if (StringUtil.isNotEmpty(subMerchantNo) && !subMerchantNo.equals(item.getSubMerchantNo())) {
                // 接口里传了二级商户号,但没完全匹配
                continue;
            }
            if (StringUtil.isEmpty(subMerchantNo) && StringUtil.isNotEmpty(item.getSubMerchantNo())) {
                // 接口里没传,但这个配置是特定商户的
                continue;
            }
            if (StringUtil.isNotEmpty(channelMerchantCode) && !channelMerchantCode.equals(item.getChannelMerchantNo())) {
                // 接口里传了渠道商户号,但没完全匹配
                continue;
            }
            if (StringUtil.isEmpty(channelMerchantCode) && StringUtil.isNotEmpty(item.getChannelMerchantNo())) {
                // 接口里没传,但这个配置是特定渠道商户号
                continue;
            }
            LogicKeyEnum logicKeyEnum = item.getMccLogic();
            if (StringUtil.isNotEmpty(mcc)) {
                if (logicKeyEnum != null) {
                    if (logicKeyEnum == LogicKeyEnum.INCLUDE) {
                        if (item.getStandardMcc().contains(mcc)) {
                            finalFeeItem = item;
                            break;
                        }
                    } else if (logicKeyEnum == LogicKeyEnum.EXCLUDE) {
                        if (!item.getStandardMcc().contains(mcc)) {
                            finalFeeItem = item;
                            break;
                        }
                    }
                }
            } else {
                //如果没传MCC,那么只能匹配没配置MCC的
                if (logicKeyEnum == null) {
                    finalFeeItem = item;
                    break;
                }
            }
        }
        return finalFeeItem;
    }

    /**
     * 精准匹配标准产品
     */
    public InstContractStandardProduct preciseMatchingStandardProduct(String paymentMethodType, String targetOrg, String cardOrg, List<InstContractStandardProduct> standardProducts) {
        InstContractStandardProduct standardProduct = null;
        for (InstContractStandardProduct item : standardProducts) {
            if (!paymentMethodType.equalsIgnoreCase(item.getPaymentMethodType())) {
                //支付方式类型
                continue;
            }
            if (StringUtil.isNotEmpty(targetOrg) && !targetOrg.equals(item.getTargetOrg())) {
                // 接口里传了目标机构
                continue;
            }
            if (StringUtil.isEmpty(targetOrg) && !"*".equals(item.getTargetOrg())) {
                // 接口里没传,但是数据库有值.只要有就跳过. (因为数据库*是目标机构默认值,所以一定不为空)
                continue;
            }
            if (StringUtil.isNotEmpty(cardOrg) && !cardOrg.equals(item.getCardOrg())) {
                // 接口里传了卡组织
                continue;
            }
            if (StringUtil.isEmpty(cardOrg) && !"CARDPAY".equalsIgnoreCase(item.getCardOrg()) && StringUtil.isNotEmpty(item.getCardOrg())) {
                // 接口里没传,但是数据库有值.只要有就跳过,(历史原因,卡组可能为空、可能是CARDPAY,他们代表的含义一致,都是属于卡组兜底)
                continue;
            }
            standardProduct = item;
            break;
        }
        return standardProduct;
    }

    /**
     * 结算精准匹配,必须所有参数全部映射. 结算不考虑MCC等信息,只关注支付币种或者渠道商户号
     */
    private InstContractSettlementItem preciseMatchingSettle(String paymentCcy, String channelMerchantCode, List<InstContractSettlementItem> settlementItems) {
        InstContractSettlementItem finalFeeItem = null;
        for (InstContractSettlementItem item : settlementItems) {
            if (!paymentCcy.equals(item.getPayCurrency())) {
                //币种一定要匹配
                continue;
            }
            if (StringUtil.isNotEmpty(channelMerchantCode) && !channelMerchantCode.equals(item.getChannelMerchantNo())) {
                // 接口里传了渠道商户号,但没完全匹配
                continue;
            }
            finalFeeItem = item;
        }
        return finalFeeItem;
    }

}
