package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.facade.api.ChannelFxInfoQueryFacade;
import com.payermax.channel.inst.center.facade.api.FundsAccountFacade;
import com.payermax.channel.inst.center.facade.request.FxInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.InstFundsAccountRequest;
import com.payermax.channel.inst.center.facade.response.FxInfoQueryResponse;
import com.payermax.channel.inst.center.facade.response.InstFundsAccountResponse;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> at 2022/10/8 20:39
 **/
@Api(tags = "字典API")
@RestController
@RequestMapping("instCenter/fxSettle")
public class FxSettleFacadeController {

    @Resource
    private ChannelFxInfoQueryFacade channelFxInfoQueryFacade;

    @Resource
    private FundsAccountFacade fundsAccountFacade;

    @ApiOperation(value = "机构账号查询", notes = "机构账号查询")
    @PostMapping("queryChanelFxInfo")
    @DigestLog(isRecord = true)
    public Result<FxInfoQueryResponse> queryFundsAccounts(@RequestBody FxInfoQueryRequest request) {
        return channelFxInfoQueryFacade.queryChanelFxInfo(request);
    }

    @ApiOperation(value = "新版机构账号查询", notes = "机构账号查询")
    @PostMapping("fundsQuery")
    @DigestLog(isRecord = true)
    public Result<List<InstFundsAccountResponse>> fundsQuery(@RequestBody InstFundsAccountRequest request) {
        return fundsAccountFacade.queryInstAccountDetailList(request);
    }
}
