package com.payermax.channel.inst.center.service;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.service.matcher.ContractMatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 漏斗模型匹配机制
 */
@Slf4j
@Service
public class FunnelModelMatchService {

    private static final List<ContractMatcher> MATCHERS = new ArrayList<>();

    public synchronized static void register(ContractMatcher matcher) {
        MATCHERS.add(matcher);
    }

    public InstContractFeeItem matchFee(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> contractFeeItems) {
        if (CollectionUtils.isEmpty(contractFeeItems)) {
            return null;
        }
        List<InstContractFeeItem> feeItems = contractFeeItems;
        for (ContractMatcher item : MATCHERS) {
            feeItems = item.filterMatches(funnelMatchItem, feeItems);
        }
        AssertUtil.isTrue(!CollectionUtils.isEmpty(feeItems), "", "无匹配数据");
        AssertUtil.isTrue(feeItems.size() == 1, "", "匹配到多条:" + JSON.toJSONString(feeItems));
        return feeItems.get(0); //CHECKED
    }
}
