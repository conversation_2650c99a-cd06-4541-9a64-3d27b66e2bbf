package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.manage.workflow.OmcWorkflowCallbackService;
import com.payermax.channel.inst.center.common.constants.SymbolConstants;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.facade.response.CommonS3UploadResponse;
import com.payermax.channel.inst.center.infrastructure.adapter.FileUploadService;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.fs.dto.UploadResponse;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR> at 2022/10/8 20:39
 **/
@Api(tags = "公共API")
@RestController
@RequestMapping("instCenter/common")
public class CommonController {

    @Resource
    private FileUploadService fileUploadAdapter;

    @Resource
    private OmcWorkflowCallbackService workflowCallbackService;

    @ApiOperation(value = "s3文件上传", notes = "s3文件上传")
    @RequestMapping(value = "/upload/api", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8;"})
    public Result<CommonS3UploadResponse> upLoadApi(@RequestPart("file") MultipartFile file) throws IOException {
        String filename = UUID.randomUUID() + SymbolConstants.SYMBOL_UNDERLINE + file.getOriginalFilename();
        UploadResponse uploadResponse = fileUploadAdapter.upLoadToS3(file, filename);
        return ResultUtils.success(new CommonS3UploadResponse(filename,uploadResponse.getUrl()));
    }

    /**
     * 通用工作流回调
     */
    @PostMapping("/workflow/callback")
    public Result<Boolean> workflowCallback(@RequestBody WfProcessEventInfo eventInfo) {
        return ResultUtils.success(workflowCallbackService.callback(eventInfo));
    }

}
