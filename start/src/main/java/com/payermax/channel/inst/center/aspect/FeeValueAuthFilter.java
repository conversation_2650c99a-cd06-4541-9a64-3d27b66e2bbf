package com.payermax.channel.inst.center.aspect;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.payermax.channel.inst.center.app.model.contract.desensitizer.AuthFeeTypeEnum;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * <AUTHOR>
 * @date 2024/1/13
 * @DESC
 */
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
public @interface FeeValueAuthFilter {

    AuthFeeTypeEnum feeType();
}
