package com.payermax.channel.inst.center.service.matcher;

import com.google.common.collect.Lists;
import com.payermax.channel.inst.center.common.enums.LogicKeyEnum;
import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@DependsOn("paymentCcyMatcher")
public class MccMatcher extends AbstractContractMatcher {
    @Override
    protected boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        boolean allEmpty = true;
        for (InstContractFeeItem item : feeItems) {
            if (item.getMccLogic() != null) {
                allEmpty = false;
                break;
            }
        }
        return allEmpty;
    }

    @Override
    protected List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String mcc = funnelMatchItem.getMcc();
        if ("UNKNOWN".equals(mcc) || StringUtil.isEmpty(mcc)) {
            return Lists.newArrayList();
        }
        List<InstContractFeeItem> preciseFee = new ArrayList<>();
        for (InstContractFeeItem feeItem : feeItems) {
            LogicKeyEnum logicKeyEnum = feeItem.getMccLogic();
            if (logicKeyEnum != null) {
                if (logicKeyEnum == LogicKeyEnum.INCLUDE) {
                    if (feeItem.getStandardMcc().contains(mcc)) {
                        preciseFee.add(feeItem);
                    }
                } else if (logicKeyEnum == LogicKeyEnum.EXCLUDE) {
                    if (!feeItem.getStandardMcc().contains(mcc)) {
                        preciseFee.add(feeItem);
                    }
                }
            }
        }
        return preciseFee;
    }

    @Override
    protected List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        List<InstContractFeeItem> fuzzyFee = new ArrayList<>();
        for (InstContractFeeItem feeItem : feeItems) {
            LogicKeyEnum logicKeyEnum = feeItem.getMccLogic();
            if (logicKeyEnum == null) {
                fuzzyFee.add(feeItem);
            }
        }
        return fuzzyFee;
    }
}
