package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.dto.fundsAgreement.InstFundsAgreementContextDTO;
import com.payermax.channel.inst.center.app.manage.fundsAgreement.InstFundsAgreementManageService;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementQueryRequestDTO;
import com.payermax.channel.inst.center.app.response.InstFundsAgreementQueryVO;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.common.utils.ValidationUtils;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC 机构资金协议API
 */
@Api(tags = "机构资金协议API")
@RestController
@Slf4j
@RequestMapping("instCenter/fundsAgreement")
@AllArgsConstructor
public class InstFundsAgreementController {

    private final InstFundsAgreementManageService fundsAgreementService;

    /**
     * 保存资金协议
     */
    @PostMapping("fundsAgreementSave")
    @DigestLog(isRecord = true)
    public Result<InstFundsAgreementContextDTO> fundsAgreementSave(@RequestBody InstFundsAgreementContextDTO request){
        try {
            ValidationUtils.validate(request);
            return ResultUtils.success(fundsAgreementService.fundsAgreementSave(request));
        } catch (BizException e) {
            log.error("biz Exception", e);
            return ResultUtils.bizExceptionFail(e);
        } catch (BusinessException | IllegalArgumentException e) {
            log.error("BusinessException Exception", e);
            return ResultUtils.bizException(e);
        }  catch (Exception e1) {
            log.error("unknown Exception", e1);
            return ResultUtils.unknownFail();
        }

    }

    /**
     * 查询资金协议
     */
    @PostMapping("fundsAgreementQuery")
    @DigestLog(isRecord = true)
    public Result<List<InstFundsAgreementQueryVO>> fundsAgreementQuery(@RequestParam("pageNum") int pageNum, @RequestParam("pageSize") int pageSize, @RequestBody InstFundsAgreementQueryRequestDTO request){
        return ResultUtil.success(fundsAgreementService.fundsAgreementQuery(pageNum, pageSize, request));
    }



    /**
     * 查询清算规则
     */
    @PostMapping("getFundsAgreementById")
    @DigestLog(isRecord = true)
    public Result<InstFundsAgreementPO> getFundsAgreementById(@RequestParam("id") String id){
        return ResultUtil.success(fundsAgreementService.getFundsAgreementById(id));
    }

    /**
     * 添加清算规则
     */
    @PostMapping("settleRuleAdd")
    @DigestLog(isRecord = true)
    public Result<Boolean> settleRuleAdd(@RequestHeader("shareId") String shareId, @RequestBody InstFundsAgreementContextDTO.FundsSettleRule request){
        return ResultUtil.success(fundsAgreementService.settleRuleAdd(shareId, request));
    }

    /**
     * 修改业务协议
     */
    @PostMapping("bizAgreementModify")
    @DigestLog(isRecord = true)
    public Result<Boolean> bizAgreementModify(@RequestHeader("shareId") String shareId, @RequestBody InstFundsAgreementContextDTO.BizAgreement request){
        return ResultUtil.success(fundsAgreementService.bizAgreementModify(shareId, request));
    }

    /**
     * 修改资金协议
     */
    @PostMapping("fundsAgreementModify")
    @DigestLog(isRecord = true)
    public Result<Boolean> fundsAgreementModify(@RequestHeader("shareId") String shareId, @RequestBody InstFundsAgreementContextDTO.FundsAgreement request){
        return ResultUtil.success(fundsAgreementService.fundsAgreementModify(shareId, request));
    }


    /**
     * 修改资金协议生效状态
     */
    @PostMapping("fundsAgreementStatusChange")
    @DigestLog(isRecord = true)
    public Result<Boolean> fundsAgreementStatusChange(@RequestHeader("shareId") String shareId, @RequestBody InstFundsAgreementContextDTO.FundsAgreement request){
        return ResultUtil.success(fundsAgreementService.fundsAgreementStatusChange(shareId, request));
    }


    /**
     * 修改清算规则
     */
    @PostMapping("settleRuleModify")
    @DigestLog(isRecord = true)
    public Result<Boolean> settleRuleModify(@RequestHeader("shareId") String shareId, @RequestBody InstFundsAgreementContextDTO.FundsSettleRule request){
        return ResultUtil.success(fundsAgreementService.settleRuleModify(shareId, request));
    }
}
