package com.payermax.channel.inst.center.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.manage.SubAccountCompensateManage;
import com.payermax.channel.inst.center.app.service.InstSubFundsAccountService;
import com.payermax.channel.inst.center.common.utils.ValidationUtils;
import com.payermax.channel.inst.center.domain.subaccount.request.PatchSubAccountCompensateTaskRequestDO;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubAccountBathQueryEntity;
import com.payermax.channel.inst.center.infrastructure.util.LockUtils;
import com.payermax.channel.inst.center.infrastructure.util.MigrationAlgorithmUtils;
import com.ushareit.fintech.common.util.JsonUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@JobHandler(value = "batchSubAccountCompensateJobHandler")
public class BatchSubAccountCompensateJobHandler extends AbstractJobHandler<InstSubAccountBathQueryEntity> {

    @Autowired
    SubAccountCompensateManage subAccountCompensateManager;

    @Autowired
    InstSubFundsAccountService instSubFundsAccountService;

    @Autowired
    ReqDoAssembler reqDoAssembler;
    
    @Resource
    LockUtils lockUtils;

    @NacosValue(value = "${batchSubAccountCompensateJobHandler.lockTime:5}", autoRefreshed = true)
    private long lockTime;

    @NacosValue(value = "${batchSubAccountCompensateJobHandler.lockTimeUnit:MINUTES}", autoRefreshed = true)
    private TimeUnit lockTimeUnit;

    @Override
    public InstSubAccountBathQueryEntity validateParams(String param) {
        // 业务逻辑
        PatchSubAccountCompensateTaskRequestDO request = JsonUtils.toBean(PatchSubAccountCompensateTaskRequestDO.class, param);
        InstSubAccountBathQueryEntity queryEntity = reqDoAssembler.toInstSubAccountBathQueryEntity(request);
        // 必填校验
        ValidationUtils.validate(queryEntity);
        return queryEntity;
    }

    @Override
    public void executeBusiness(InstSubAccountBathQueryEntity queryEntity) {
        // 补偿申请开关，数据源切换后补偿
        while (!MigrationAlgorithmUtils.getMigrateStart()) {

            // 分页查询子级资金账号数据
            IPage<InstSubFundsAccountEntity> subFundsAccountEntityIPage = instSubFundsAccountService.querySubAccountForTask(queryEntity);

            List<InstSubFundsAccountEntity> subFundsAccountEntityList = subFundsAccountEntityIPage.getRecords();

            // 如果子级资金账号为空，则跳过
            if (CollectionUtil.isEmpty(subFundsAccountEntityList)) {
                break;
            }

            for (InstSubFundsAccountEntity subFundsAccount : subFundsAccountEntityList) {
                try {
                    subAccountCompensateManager.subAccountCompensate(subFundsAccount, queryEntity.getCompensateRetry());
                    XxlJobLogger.log("子级账号补偿成功:{}", JSON.toJSONString(subFundsAccount));
                } catch (Exception e) {
                    log.error("BatchSubAccountCompensateJobHandler-依次补偿子级账号异常：{}，参数:{}", e, JSON.toJSONString(subFundsAccount));
                    XxlJobLogger.log("输出到xxl-依次补偿子级账号异常：{}", e);
                }
            }
            queryEntity.setPageNum(queryEntity.getPageNum() + 1);
        }
    }

    @Override
    public String buildTaskKey(String taskName, InstSubAccountBathQueryEntity queryEntity) {
        return Joiner.on(":").join(taskName, queryEntity.getStatus());
    }

    @Override
    public boolean getTaskLock(String lockKey) {
        return lockUtils.lock(lockKey, lockRegionEnum, lockTime, lockTimeUnit);
    }

    @Override
    public void releaseTaskLock(String lockKey) {
        lockUtils.release(lockKey, lockRegionEnum);
    }
}
