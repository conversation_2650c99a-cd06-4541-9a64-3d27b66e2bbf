package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.manage.account.InstFundsAccountNewManager;
import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.omc.channel.exchange.facade.request.InstFundsAccountSaveRequest;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/10/9
 * @DESC
 */
@Api(tags = "渠道账户API")
@Slf4j
@RestController
@RequestMapping("instCenter/fundsAccount")
@RequiredArgsConstructor
public class InstFundsAccountController {

    private final InstFundsAccountNewManager fundsAccountNewManager;


    /**
     * 新增/修改渠道账户-发起流程
     * @param shareId 用户 ID
     * @param request 请求参数
     * @return 流程发起结果
     */
    @PostMapping("startSaveProcess")
    public Result<Boolean> startSaveProcess(@RequestHeader("shareId") String shareId, @RequestBody InstFundsAccountSaveRequest request) {
        return ExceptionUtils.commonTryCatch(() -> fundsAccountNewManager.startSaveProcess(request, shareId));
    }
}
