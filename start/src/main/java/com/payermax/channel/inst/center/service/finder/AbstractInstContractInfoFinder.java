package com.payermax.channel.inst.center.service.finder;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.payermax.channel.inst.center.app.assembler.domain.InstContractRequestAssembler;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractQueryModeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.service.InstContractQueryServiceImpl;
import com.payermax.channel.inst.center.service.convertor.SettleInfoConvertor;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/11/5
 * @DESC 费用查询器
 */
@Slf4j
@Component
public abstract class AbstractInstContractInfoFinder {

    @Resource
    protected InstContractQueryServiceImpl contractQueryService;

    @Resource
    protected InstContractRequestAssembler instContractRequestAssembler;

    @Resource
    protected SettleInfoConvertor settleInfoConvertor;

    @Resource
    protected InstFeeInfoConvertor feeInfoConvertor;

    /**
     * 费用查询策略
     */
    private static final Map<ContractBizTypeEnum, AbstractInstContractInfoFinder> FINDER_STRATEGY = new ConcurrentHashMap<>();

    /**
     * 策略注册
     */
    public static void registryStrategy(ContractBizTypeEnum contractBizType, AbstractInstContractInfoFinder finder) {
        log.info("registry contract finder type: {}, strategy: {}", contractBizType, finder);
        AssertUtil.isTrue(Objects.nonNull(contractBizType), ErrorCodeEnum.INST_CENTER_FEE_FINDER_STRATEGY_ERROR.getCode(), "合约查询器类型应为枚举值");
        AssertUtil.isTrue(Objects.nonNull(finder), ErrorCodeEnum.INST_CENTER_FEE_FINDER_STRATEGY_ERROR.getCode(), "合约查询器不能为空");
        FINDER_STRATEGY.putIfAbsent(contractBizType, finder);
    }

    /**
     * 策略获取
     */
    public static AbstractInstContractInfoFinder getStrategy(ContractBizTypeEnum contractBizType) {
        log.info("get contract finder type: {}", contractBizType);
        AssertUtil.isTrue(FINDER_STRATEGY.containsKey(contractBizType), ErrorCodeEnum.INNER_ERROR.getCode(), "未注册的策略");
        return FINDER_STRATEGY.get(contractBizType);
    }

    /**
     * 组装响应
     * @param context 查询上下文
     * @return 查询结果
     */
    protected abstract InstInfoQueryResponse composeResponse(InstContractQueryContext context);

    /**
     * 合约查询
     * @param request 请求参数
     * @return 查询结果
     */
    public final InstInfoQueryResponse find(InstInfoQueryRequest request) {


        InstContractQueryContext context = contextInit(request);
        log.info("{}", context);

        // 前置处理
        preProcessor(context);

        // 基础参数校验
        paramCheck(context);

        // 查询合约及产品
        findContractAndProduct(context);

        // 查询费用信息
        // 费用信息中包含手续费(交易|退款|争议|风控手续费)、税费、外汇信息
        findFee(context);

        // 查询结算信息
        findSettle(context);

        // 组装响应
        return composeResponse(context);
    }

    /**
     * 初始化查询上下文
     */
    protected InstContractQueryContext contextInit(InstInfoQueryRequest request) {
        InstContractQueryContext context = new InstContractQueryContext();
        context.setRequest(request);
        return context;
    }

    /**
     * 前置处理
     */
    protected void preProcessor(InstContractQueryContext context){
        // 默认合约查询方式为 MID
        context.setContractQueryMode(ContractQueryModeEnum.MID);
        InstInfoQueryRequest request = context.getRequest();

        feeInfoConvertor.contractAndProductParamFill(request);

        feeInfoConvertor.feeItemParamFill(request);
    }

    /**
     * 合约查询-查询合约、产品信息
     */
    protected void findContractAndProduct(InstContractQueryContext context){
        log.info("开始查询合约、产品信息");
        InstInfoQueryRequest request = context.getRequest();

        // 查询合约
        InstContractVersionInfo versionInfo = contractQueryByScene(context);

        // 查询标准产品
        InstContractStandardProduct standardProduct = queryStandardProduct(context, versionInfo.getStandardProducts(), request.getPaymentMethodType(), request.getTargetOrg(), request.getCardOrg());

        // 查询原始产品
        InstContractOriginProduct originProduct = contractQueryService.queryOriginProductByNo(standardProduct.getInstOriginProductNo(), versionInfo);
        context.setContractVersionInfo(versionInfo);
        context.setOriginProduct(originProduct);
        context.setStandardProduct(standardProduct);
    }

    /**
     * 合约查询-分场景，场景复杂时再使用策略模式实现
     */
    protected InstContractVersionInfo contractQueryByScene(InstContractQueryContext context) {

        InstInfoQueryRequest request = context.getRequest();
        log.info("开始查询合约，查询模式:{}", context.getContractQueryMode());
        AssertUtil.isTrue(EnumUtils.isValidEnum(ContractQueryModeEnum.class, context.getContractQueryMode().name()), ErrorCodeEnum.INST_CENTER_CONTRACT_QUERY_ERROR.getCode(), "合约查询模式应为枚举值");

        // 此处区分场景，按照 MID 查询 / 合约直接查询
        InstContractVersionInfo versionInfo;
        switch (context.getContractQueryMode()){
            case DIRECT:
                versionInfo = contractQueryService.queryActiveContract(request.getBizType(), request.getInstCode(), request.getEntity(), request.getTransactionTime());
                break;
            case MID:
                versionInfo = contractQueryService.queryActiveContract(request.getChannelMerchantCode(), request.getBizType(), request.getTransactionTime());
                break;
            default:
                throw new BusinessException(ErrorCodeEnum.INST_CENTER_CONTRACT_QUERY_ERROR.getCode(), "不支持除 MID | DIRECT 以外的合约查询模式");
        }
        log.info("查询到的合约信息: {}", versionInfo);
        return versionInfo;
    }


    /**
     * 查询标准产品
     */
    protected InstContractStandardProduct queryStandardProduct(InstContractQueryContext context, List<InstContractStandardProduct> standardProducts, String paymentMethodType, String targetOrg, String cardOrg) {
        log.info("合约查询方式:{}, 标准产品匹配逻辑, paymentMethodType: {}, targetOrg: {}, cardOrg: {}", context.getContractQueryMode(), paymentMethodType, targetOrg, cardOrg);
        return contractQueryService.queryStandardProduct(standardProducts, paymentMethodType, targetOrg, cardOrg);
    }


    /**
     * 费用信息查询
     * @param context 合约查询上下文
     */
    protected void findFee(InstContractQueryContext context){
        InstInfoQueryRequest request = context.getRequest();
        // 查询Fee信息
        InstContractFeeItem feeItem = contractQueryService.queryFeeByOriProduct(context.getOriginProduct(), request);
        context.setFeeItem(feeItem);
    }

    /**
     * 查询结算信息
     */
    protected void findSettle(InstContractQueryContext context) {
        try {
            InstInfoQueryRequest request = context.getRequest();
            InstContractSettlementItem settleItem = contractQueryService.querySettleByOriProduct(context.getOriginProduct(), request.getPayCurrency(), request.getChannelMerchantCode());
            context.setSettleItem(settleItem);
        } catch (Exception e) {
            log.warn("查询结算信息失败", e);
        }
    }

    /**
     * 设置合约查询方式为 DIRECT，并校验参数
     */
    protected void setDirectQueryAndCheck(InstContractQueryContext context){
        // 合约查询方式为 DIRECT，即根据 渠道 + 主体 + 业务类型查询
        context.setContractQueryMode(ContractQueryModeEnum.DIRECT);
        InstInfoQueryRequest request = context.getRequest();
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getBizType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "业务类型不能为空");
        AssertUtil.isTrue(EnumUtils.isValidEnum(ContractBizTypeEnum.class, request.getBizType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "业务类型错误");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getInstCode()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "机构编码不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体编码不能为空");
    }


    /**
     * 参数校验
     */
    protected void paramCheck(InstContractQueryContext context){
        InstInfoQueryRequest request = context.getRequest();
        AssertUtil.isTrue(EnumUtils.isValidEnum(ContractBizTypeEnum.class, request.getBizType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "业务类型错误");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getChannelMerchantCode()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "渠道商户号不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPaymentMethodType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付方式类型不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPayCurrency()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付币种不能为空");
        AssertUtil.isTrue(ObjectUtils.isNotNull(request.getTransactionTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "交易时间不能为空");
        AssertUtil.isTrue( StringUtil.isNotBlank(request.getTargetOrg()) || StringUtil.isNotBlank(request.getCardOrg() ),
                ErrorCodeEnum.PARAMETER_INVALID.getCode(), "目标机构和卡组不能全部为空");
    }


}
