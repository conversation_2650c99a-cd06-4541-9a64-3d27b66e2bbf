package com.payermax.channel.inst.center.service.finder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.domain.enums.contract.management.AccountingTypeEnum;
import com.payermax.channel.inst.center.facade.enums.contract.FeeTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstAccountingTypeQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.response.contract.InstAccountingTypeResponse;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC 机构费用核对类型查询器，特殊实现
 *       套用费用查询，仅需到合约版本维度，不查询具体的费用项
 *       不注册到 AbstractInstContractInfoFinder 策略列表中
 */
@Slf4j
@Component
public class InstAccountingTypeFinder extends AbstractInstContractInfoFinder {


    @Override
    protected void preProcessor(InstContractQueryContext context) {
        InstInfoQueryRequest request = context.getRequest();
        if(request.getTransactionTime() <= 0L){
            request.setTransactionTime(System.currentTimeMillis());
        }
    }

    @Override
    protected void paramCheck(InstContractQueryContext context) {
        InstAccountingTypeQueryRequest accountingTypeQueryRequest = (InstAccountingTypeQueryRequest) context.getRequest();
        AssertUtil.isTrue(StringUtils.isNotBlank(accountingTypeQueryRequest.getBizType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "业务类型不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(accountingTypeQueryRequest.getInstCode()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "机构编码不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(accountingTypeQueryRequest.getEntity()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "我司主体编码不能为空");
        AssertUtil.isTrue(EnumUtils.isValidEnum(FeeTypeEnum.class, accountingTypeQueryRequest.getFeeType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "费用类型必须为枚举值");
    }

    @Override
    protected void findFee(InstContractQueryContext context) {
        // 无需查询费用，只需要获取费用对应核对方式
        InstAccountingTypeQueryRequest request = (InstAccountingTypeQueryRequest)context.getRequest();
        InstAccountingTypeResponse response = instContractRequestAssembler.convertReq2Resp(request);

        // 获取合约版本对应的核对方式
        InstContractVersionInfo contractVersionInfo = context.getContractVersionInfo();
        Map<FeeTypeEnum, String> accountingTypeMap = JSON.parseObject(contractVersionInfo.getAccountingType(), new TypeReference<Map<FeeTypeEnum, String>>(){});
        FeeTypeEnum feeType = EnumUtils.getEnum(FeeTypeEnum.class, request.getFeeType());

        // 默认被动核对
        AccountingTypeEnum accountingType = AccountingTypeEnum.PASSIVE;
        if(MapUtils.isNotEmpty(accountingTypeMap) && accountingTypeMap.containsKey(feeType)){
            AssertUtil.isTrue(EnumUtils.isValidEnum(AccountingTypeEnum.class, accountingTypeMap.get(feeType)), ErrorCodeEnum.INST_CONTRACT_ACCOUNTING_TYPE_QUERY_ERROR.getCode(), "渠道核对方式配置错误");
            accountingType = EnumUtils.getEnumIgnoreCase(AccountingTypeEnum.class, accountingTypeMap.get(feeType));
        }
        response.setAccountingType(accountingType.name());
        context.setResponse(response);
    }

    @Override
    protected void findSettle(InstContractQueryContext context) {
        // 无需查询结算信息
        context.setSettleItem(null);
    }

    @Override
    protected InstAccountingTypeResponse composeResponse(InstContractQueryContext context) {

        InstAccountingTypeResponse response = (InstAccountingTypeResponse)context.getResponse();

        // 无需返回扣款信息、费用信息
        response.setFeeConfigMap(null);
        response.setDeductConfig(null);

        // 返回值通用填充
        feeInfoConvertor.responseDefaultFill(response, context);
        return response;
    }



    @Override
    protected void findContractAndProduct(InstContractQueryContext context) {
        log.info("开始查询合约信息");
        InstAccountingTypeQueryRequest request = (InstAccountingTypeQueryRequest) context.getRequest();
        // 查询合约版本，无需查询到产品维度
        InstContractVersionInfo versionInfo = contractQueryService.queryActiveContract(request.getBizType(), request.getInstCode(), request.getEntity(), System.currentTimeMillis());

        versionInfo.setInstCode(request.getInstCode());
        context.setContractVersionInfo(versionInfo);
    }
}
