package com.payermax.channel.inst.center.task;

import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractBaseInfoRepository;
import com.payermax.channel.inst.center.infrastructure.repository.repo.InstContractMidMappingRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
@JobHandler(value = "instContractMidMappingReplenishJob")
public class InstContractMidMappingReplenishJob extends IJobHandler {

    @Resource
    private InstContractMidMappingRepository instContractMidMappingRepository;

    @Resource
    private InstContractBaseInfoRepository instContractBaseInfoRepository;

    @Override
    public ReturnT<String> execute(String s) {
        List<InstContractMidMappingPO> midMappingPOS = instContractMidMappingRepository.queryAllEmptyContractItems();
        midMappingPOS.forEach(item -> {
            try {
                InstContractBaseInfoPO baseInfoPO = new InstContractBaseInfoPO();
                baseInfoPO.setInstCode(item.getInstCode());
                baseInfoPO.setContractEntity(item.getEntity());
                baseInfoPO.setInstProductType(item.getInstType());
                baseInfoPO.setStatus(ContractStatusEnum.ACTIVATED.name());
                InstContractBaseInfoPO baseInfoResult = instContractBaseInfoRepository.queryActiveBaseInfoByBusinessKey(baseInfoPO);
                if (baseInfoResult != null) {
                    item.setContractNo(baseInfoResult.getContractNo());
                    instContractMidMappingRepository.updateContractMidMapping(item);
                    log.info("instContractMidMappingReplenishJob update success. MID:{}", item.getChannelMerchantCode());
                }
            } catch (Exception e) {
                log.error("InstContractMidMappingPO error!", e);
            }
        });

        return ReturnT.SUCCESS;
    }
}
