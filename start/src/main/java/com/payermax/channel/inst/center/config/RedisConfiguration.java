package com.payermax.channel.inst.center.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2024/7/8 11:46
 **/
@Configuration
public class RedisConfiguration {

    @NacosValue(value = "${spring.redis.host:}", autoRefreshed = true)
    private String redisHost;
    @NacosValue(value = "${spring.redis.port:6379}", autoRefreshed = true)
    private Integer redisPort;
    @NacosValue(value = "${spring.redis.password:}", autoRefreshed = true)
    private String redisPassword;
    @NacosValue(value = "${spring.redis.database:}", autoRefreshed = true)
    public int database;
    @NacosValue(value = "${spring.redis.timeout:}", autoRefreshed = true)
    private int readTimeout;
    @NacosValue(value = "${spring.redis.ssl:true}", autoRefreshed = true)
    private boolean useSsl;

    @NacosValue(value = "${spring.redis.jedis.pool.max-active:}", autoRefreshed = true)
    private int maxTotal;
    @NacosValue(value = "${spring.redis.jedis.pool.max-wait:}", autoRefreshed = true)
    private long maxWait;
    @NacosValue(value = "${spring.redis.jedis.pool.max-idle:}", autoRefreshed = true)
    private int maxIdle;
    @NacosValue(value = "${spring.redis.jedis.pool.min-idle:}", autoRefreshed = true)
    private int minIdle;
    
    @Bean
    public JedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
        redisConfig.setHostName(redisHost);
        redisConfig.setPort(redisPort);
        redisConfig.setDatabase(database);
        redisConfig.setPassword(redisPassword);

        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxWaitMillis(maxWait);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        JedisClientConfiguration.JedisClientConfigurationBuilder jedisClientConfigurationBuilder = JedisClientConfiguration.builder()
                .readTimeout(Duration.ofMillis(readTimeout))
                .usePooling()
                .poolConfig(poolConfig).and();
        if (useSsl) {
            return new JedisConnectionFactory(redisConfig, jedisClientConfigurationBuilder.useSsl().build());
        } else {
            return new JedisConnectionFactory(redisConfig, jedisClientConfigurationBuilder.build());
        }
       
    }
    
}
