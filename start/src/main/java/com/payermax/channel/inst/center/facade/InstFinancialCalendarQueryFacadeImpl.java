package com.payermax.channel.inst.center.facade;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.domain.InstFinancialCalendarAssembler;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContextBuilder;
import com.payermax.channel.inst.center.app.manage.calendar.checker.AbstractHolidayCheckerStrategy;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.common.exception.BizException;
import com.payermax.channel.inst.center.common.utils.CalendarUtil;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarTypeEnum;
import com.payermax.channel.inst.center.facade.api.InstFinancialCalendarQueryFacade;
import com.payermax.channel.inst.center.facade.request.calendar.*;
import com.payermax.channel.inst.center.facade.response.calendar.*;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Setter
@DubboService(version = "${dubbo.provider.version}", interfaceClass = InstFinancialCalendarQueryFacade.class)
public class InstFinancialCalendarQueryFacadeImpl implements InstFinancialCalendarQueryFacade {


    @NacosValue(value="${inst.financialCalendar.needAccessCheck:true}", autoRefreshed = true)
    private boolean needAccessCheck;

    @NacosValue(value = "${inst.financialCalendar.allowApp:}", autoRefreshed = true)
    private Set<String> allowApp;

    @NacosValue(value = "${inst.rpc.context.attachment.appNameKey:SOURCE_APP_NAME}", autoRefreshed = true)
    private String applicationAttachmentKey;

    @Resource
    private InstFinancialCalendarContextBuilder contextBuilder;


    @Override
    public Result<HolidayCheckResponse> holidayCheck(HolidayCheckRequest request) {
        try {
            // 通过 RPC 上下文获取应用名称进行校验
            allowInvokeCheck();

            // 参数校验
            paramCheck(request);

            // 根据日历类型进行匹配
            AbstractHolidayCheckerStrategy abstractHolidayCheckerStrategy = AbstractHolidayCheckerStrategy.get(CalendarTypeEnum.valueOf(request.getCalendarType()));
            return ResultUtils.success(abstractHolidayCheckerStrategy.holidayCheck(request));
        } catch (BizException e) {
            log.error("biz Exception", e);
            return ResultUtils.bizExceptionFail(e);
        } catch (BusinessException e) {
            log.error("BusinessException Exception", e);
            return ResultUtils.businessException(e);
        } catch (IllegalArgumentException e) {
            log.error("IllegalArgumentException Exception", e);
            return ResultUtils.bizException(e);
        } catch (Exception e1) {
            log.error("unknown Exception", e1);
            return ResultUtils.unknownFail();
        }
    }

    @Override
    public Result<List<NextWorkdayCalculationResponse>> calculateNextWorkdays(List<NextWorkdayCalculationRequest> request) {
        try {
            // 通过 RPC 上下文获取应用名称进行校验
            allowInvokeCheck();

            AssertUtil.notEmpty(request, "ERROR", "request can not be empty!");

            List<NextWorkdayCalculationResponse> responses = new ArrayList<>(request.size());

            request.forEach(item -> {

                AbstractHolidayCheckerStrategy abstractHolidayCheckerStrategy = AbstractHolidayCheckerStrategy.get(CalendarTypeEnum.valueOf(item.getCalendarType()));

                responses.add(abstractHolidayCheckerStrategy.calculateNextWorkdays(item));

            });

            return ResultUtils.success(responses);
        } catch (Exception e) {
            log.error("unknown Exception", e);
            return ResultUtils.bizException(e);
        }
    }

    @Override
    public Result<HolidayMultipleCheckResponse> calculateNextWorkdaysOfMultipleLimited(HolidayMultipleCheckRequest request) {
        try {
            // 通过 RPC 上下文获取应用名称进行校验
            allowInvokeCheck();

            AbstractHolidayCheckerStrategy abstractHolidayCheckerStrategy = AbstractHolidayCheckerStrategy.get(CalendarTypeEnum.valueOf(request.getCalendarType()));

            HolidayMultipleCheckResponse responses = abstractHolidayCheckerStrategy.calculateNextWorkdaysOfMultiple(request);

            return ResultUtils.success(responses);

        } catch (Exception e) {
            log.error("unknown Exception", e);
            return ResultUtils.bizException(e);
        }
    }

    @Override
    public Result<List<NumOfWorkdaysCalculationResponse>> calculateNumOfWorkdays(List<NumOfWorkdaysCalculationRequest> request) {
        try {
            // 通过 RPC 上下文获取应用名称进行校验
            allowInvokeCheck();

            AssertUtil.notEmpty(request, "ERROR", "request can not be empty!");

            List<NumOfWorkdaysCalculationResponse> responses = new ArrayList<>(request.size());

            request.forEach(item -> {

                AbstractHolidayCheckerStrategy abstractHolidayCheckerStrategy = AbstractHolidayCheckerStrategy.get(CalendarTypeEnum.valueOf(item.getCalendarType()));

                responses.add(abstractHolidayCheckerStrategy.calculateNumOfWorkdays(item));

            });

            return ResultUtils.success(responses);
        } catch (Exception e) {
            log.error("unknown Exception", e);
            return ResultUtils.bizException(e);
        }
    }

    @Override
    public Result<HolidayQueryResponse> queryAllHolidaysByDate(HolidayQueryRequest request) {
        try {
            // 通过 RPC 上下文获取应用名称进行校验
            allowInvokeCheck();

            AbstractHolidayCheckerStrategy abstractHolidayCheckerStrategy = AbstractHolidayCheckerStrategy.get(CalendarTypeEnum.valueOf(request.getCalendarType()));

            HolidayQueryResponse response = abstractHolidayCheckerStrategy.queryAllHolidaysByDate(request);

            return ResultUtils.success(response);
        } catch (Exception e) {
            log.error("unknown Exception", e);
            return ResultUtils.bizException(e);
        }
    }

    private void paramCheck(HolidayCheckRequest request) {
        // 参数校验
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getDate()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "日期不能为空");
        AssertUtil.isTrue(CalendarUtil.isValidDateFormat(request.getDate(), "yyyy-MM-dd"), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "日期格式异常");
        AssertUtil.isTrue(EnumUtils.isValidEnum(CalendarTypeEnum.class, request.getCalendarType()), ErrorCodeEnum.ILLEGAL_ARGUMENT_ERROR.getCode(), "日历类型异常");

        // 日历类型校验
        contextBuilder.calendarTypeValid(InstFinancialCalendarAssembler.INSTANCE.holidayCheckReq2Calendar(request), false);
    }

    private void allowInvokeCheck() {
        // 先判断是否需要校验接入系统，再进行系统名称校验
        log.info("holidayCheck needAccessCheck:{}", needAccessCheck);
        if(needAccessCheck){
            @SuppressWarnings("deprecation")
            String application = Optional.ofNullable(RpcContext.getContext().getAttachment(applicationAttachmentKey)).orElse("");
            log.info("holidayCheck request application:{}", application);
            AssertUtil.isTrue(StringUtils.isNotBlank(application) && allowApp.contains(application),
                    ErrorCodeEnum.INST_FINANCIAL_CALENDAR_FIND_ERROR.getCode(), String.format("系统未允许接入: %s", application));
        }
    }

}
