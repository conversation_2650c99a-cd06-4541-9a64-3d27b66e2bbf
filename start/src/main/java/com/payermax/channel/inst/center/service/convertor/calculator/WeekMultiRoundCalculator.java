package com.payermax.channel.inst.center.service.convertor.calculator;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.enums.CycleUnit;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> tracy
 * @version 2024/1/25 14:16
 */
@Slf4j
@Service
public class WeekMultiRoundCalculator extends AbstractRoundCalculator {
    @Override
    public SettleInfoVo calculate(DSLEntity dslEntity, Integer cycleStart, Integer cycleEnd) {
        SettleInfoVo settleInfoVo = new SettleInfoVo();
        settleInfoVo.setSettleCycleUnit(CycleUnit.WEEK_MULTI.getUnit());
        settleInfoVo.setCycleStart(cycleStart);
        settleInfoVo.setCycleEnd(cycleEnd);
        settleInfoVo.setSettleCycleOtherLimit(dslEntity.getDefiniteOffset());
        return settleInfoVo;
    }

    @Override
    public RoundTypeEnum getRoundType() {
        return RoundTypeEnum.WEEK_MULTI;
    }
}
