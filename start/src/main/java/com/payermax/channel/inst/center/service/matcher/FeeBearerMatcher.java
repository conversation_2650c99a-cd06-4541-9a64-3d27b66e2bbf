package com.payermax.channel.inst.center.service.matcher;

import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.common.lang.util.StringUtil;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/16
 * @DESC
 */
@Service
@DependsOn("clearNetworkMatcher")
public class FeeBearerMatcher extends AbstractContractMatcher {

    /**
     * 判定漏斗是否直接放行
     */
    @Override
    protected boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        boolean allEmpty = true;
        for (InstContractFeeItem item : feeItems) {
            if (StringUtil.isNotEmpty(item.getFeeBearer())) {
                allEmpty = false;
                break;
            }
        }
        return allEmpty;
    }

    /**
     * 精准匹配
     */
    @Override
    protected List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String feeBearer = funnelMatchItem.getFeeBearer();
        return feeItems.stream().filter(item -> StringUtil.isNotEmpty(feeBearer) &&
                feeBearer.equals(item.getFeeBearer())).collect(Collectors.toList());
    }

    /**
     * 模糊匹配
     */
    @Override
    protected List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        return feeItems.stream().filter(item -> StringUtil.isEmpty(item.getFeeBearer())).collect(Collectors.toList());
    }
}
