package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.model.contract.desensitizer.AuthFeeTypeEnum;
import com.payermax.channel.inst.center.app.request.InstContractFeeItemRequestDTO;
import com.payermax.channel.inst.center.app.request.InstContractProductQueryRequest;
import com.payermax.channel.inst.center.app.request.InstSettleBatchModifiedReqDTO;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemFullyVO;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemVO;
import com.payermax.channel.inst.center.app.response.InstContractSettlementVO;
import com.payermax.channel.inst.center.app.response.InstFeeQueryVO;
import com.payermax.channel.inst.center.app.service.InstContractFeeWorkflowService;
import com.payermax.channel.inst.center.aspect.FeeValueAuthFilter;
import com.payermax.channel.inst.center.infrastructure.cache.CacheEnum;
import com.payermax.channel.inst.center.infrastructure.cache.InstContractCacheClear;
import com.payermax.channel.inst.center.service.InstContractFeeService;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/13
 * @DESC
 */
@Api(tags = "机构合同费用API")
@RestController
@Slf4j
@RequestMapping("instCenter/contract/fee")
@AllArgsConstructor
public class InstContractFeeController {

    private final InstContractFeeService instContractFeeService;
    private final InstContractFeeWorkflowService contractFeeWorkflowService;


    /**
     * 费用信息列表查询，无敏感数据
     */
    @PostMapping("queryFeeList")
    @DigestLog(isRecord = true)
    public Result<List<InstFeeQueryVO>> queryFeeList(@Validated @RequestBody InstContractProductQueryRequest request){
        return ResultUtil.success(instContractFeeService.queryFeeList(request));
    }



    /**
     * 费用完整信息列表查询
     */
    @PostMapping("queryFeeFullyList")
    @DigestLog(isRecord = true)
    public Result<List<InstContractFeeItemFullyVO>> queryFeeFullyList(@Validated @RequestBody InstContractProductQueryRequest request){
        return ResultUtil.success(instContractFeeService.queryFeeFullyList(request));
    }

    /**
     * 最新版本费用完整信息列表查询
     */
    @PostMapping("queryLatestVersionFeeFullyList")
    public Result<List<InstContractFeeItemFullyVO>> queryLatestVersionFeeFullyList(@RequestBody InstContractProductQueryRequest request){
        return ResultUtil.success(instContractFeeService.queryLatestVersionFeeFullyList(request));
    }

    /**
     * 结算信息列表查询
     */
    @PostMapping("querySettleList")
    @DigestLog(isRecord = true)
    public Result<List<InstFeeQueryVO>> querySettleList(@Validated @RequestBody InstContractProductQueryRequest request){
        return ResultUtil.success(instContractFeeService.querySettleList(request));
    }


    /**
     * 根据原始产品编号查询费用信息列表
     */
    @PostMapping("queryFeeConfigByOriginProduct")
    @DigestLog(isRecord = true)
    @FeeValueAuthFilter(feeType = AuthFeeTypeEnum.FEE_ITEM_LIST)
    public Object queryFeeConfigByOriginProduct(@RequestBody Map<String, String> body) {
        return instContractFeeService.queryFeeConfigByOriginProduct(body.get("originProductNo"));
    }

    /**
     * 根据FeeImeNo查询单条费用信息
     */
    @PostMapping("queryFeeConfigByNo")
    @DigestLog(isRecord = true)
    @FeeValueAuthFilter(feeType = AuthFeeTypeEnum.FEE_ITEM)
    public Object queryFeeConfigByNo(@RequestBody Map<String, String> body) {
        return instContractFeeService.queryFeeConfigByNo(body.get("feeItemNo"));
    }

    /**
     * 根据原始产品编号查询结算信息列表
     */
    @PostMapping("querySettlementConfigByOriginProduct")
    @DigestLog(isRecord = true)
    @FeeValueAuthFilter(feeType = AuthFeeTypeEnum.SETTLEMENT_ITEM_LIST)
    public Object querySettlementConfigByOriginProduct(@RequestBody Map<String, String> body) {
        return instContractFeeService.querySettlementByOriginProduct(body.get("originProductNo"));
    }


    /**
     * 根据 SettleItemNo 查询结算信息
     */
    @PostMapping("querySettleConfigByNo")
    @DigestLog(isRecord = true)
    @FeeValueAuthFilter(feeType = AuthFeeTypeEnum.SETTLEMENT_ITEM)
    public Object querySettleConfigByNo(@RequestBody Map<String, String> body) {
        return instContractFeeService.querySettlementFeeByNo(body.get("settleItemNo"));
    }

    @PostMapping("querySettleConfigByNoList")
    @DigestLog(isRecord = true)
    @FeeValueAuthFilter(feeType = AuthFeeTypeEnum.SETTLEMENT_ITEM_LIST)
    public Object querySettleConfigByNoList(@RequestBody Map<String, List<String>> body) {
        return instContractFeeService.querySettlementFeeByNoList(body.get("settleItemNoList"));
    }


    /**
     * 根据feeItemNo修改费用信息
     */
    @PostMapping("saveFeeItemByFeeItemNo")
    @DigestLog(isRecord = true)
    public Result<Boolean> saveFeeItemByFeeItemNo(@RequestHeader("shareId") String shareId, @RequestParam(name = "feeItemNo") String feeItemNo, @RequestBody InstContractFeeItemVO feeItem) {
        return ResultUtil.success(instContractFeeService.saveFeeConfigByFeeItemNo(shareId,feeItemNo, feeItem));
    }



    /**
     * 根据settleItemNo修改结算信息
     */
    @PostMapping("saveSettleItemBySettleItemNo")
    @DigestLog(isRecord = true)
    public Result<Boolean> saveSettleItemBySettleItemNo(@RequestHeader("shareId") String shareId, @RequestParam(name = "settleItemNo") String settleItemNo, @RequestBody InstContractSettlementVO settlementItem) {
        return ResultUtil.success(instContractFeeService.saveSettlementConfigBySettleItemNo(shareId, settleItemNo, settlementItem));
    }


    /**
     * 批量修改结算信息
     */
    @PostMapping("modifiedSettleByNoList")
    @DigestLog(isRecord = true)
    public Result<Boolean> modifiedSettleByNoList(@RequestHeader("shareId") String shareId, @RequestBody InstSettleBatchModifiedReqDTO request){
        return ResultUtil.success(instContractFeeService.modifiedSettleByNoList(shareId, request));
    }

    /**
     * 查询费用修改 DIFF 信息
     */
    @PostMapping("queryFeeDiffMsgByBusinessId")
    @DigestLog(isRecord = true)
    public Result<Map<String,Object>> queryFeeDiffMsgByBusinessId(@RequestParam(name = "businessId") String businessId){
        return ResultUtil.success(instContractFeeService.queryFeeDiffMsgByBusinessId(businessId));
    }


    /**
     * 查询结算修改 DIFF 信息
     */
    @PostMapping("querySettleDiffMsgByBusinessId")
    @DigestLog(isRecord = true)
    public Result<Map<String,Object>> querySettleDiffMsgByBusinessId(@RequestParam(name = "businessId") String businessId){
        return ResultUtil.success(instContractFeeService.querySettleDiffMsgByBusinessId(businessId));
    }

    /**
     * 费用修改流程回调
     */
    @PostMapping("feeModifiedCallback")
    @DigestLog(isRecord = true)
    @InstContractCacheClear(cacheName = CacheEnum.INST_NEW_CONTRACT_FEE)
    public Result<Boolean> feeModifiedCallback(@RequestBody WfProcessEventInfo eventInfo){
        return contractFeeWorkflowService.instContractFeeModifiedCallback(eventInfo);
    }

    /**
     * 结算修改流程回调
     */
    @PostMapping("settleModifiedCallback")
    @DigestLog(isRecord = true)
    public Result<Boolean> settleModifiedCallback(@RequestBody WfProcessEventInfo eventInfo){
        return contractFeeWorkflowService.instContractSettleModifiedCallback(eventInfo);
    }


    /**
     * 结算修改流程回调
     */
    @PostMapping("settleBatchModifiedCallback")
    @DigestLog(isRecord = true)
    public Result<Boolean> settleBatchModifiedCallback(@RequestBody WfProcessEventInfo eventInfo){
        return contractFeeWorkflowService.instContractSettleBatchModifiedCallback(eventInfo);
    }


    /**
     * 费用修改-全量信息，包括费用维度
     */
    @PostMapping("feeDimensionModified")
    @DigestLog(isRecord = true)
    @InstContractCacheClear(cacheName = CacheEnum.INST_NEW_CONTRACT_FEE)
    public Result<Boolean> feeDimensionModified(@RequestHeader("shareId") String shareId, @RequestBody InstContractFeeItemRequestDTO request){
        return ResultUtil.success(instContractFeeService.feeDimensionModified(shareId, request));
    }

    /**
     * 根据支付币种添加新的费用信息
     */
    @PostMapping("feeItemCopyByPayCurrency")
    @DigestLog(isRecord = true)
    public Result<String> feeItemCopyByPayCurrency(@RequestHeader("shareId") String shareId, @RequestBody InstFeeQueryVO request){
        return ResultUtil.success(instContractFeeService.feeItemCopyByPayCurrency(shareId, request));
    }

    /**
     * 根据支付币种添加新的结算信息
     */
    @PostMapping("settleItemCopyByPayCurrency")
    @DigestLog(isRecord = true)
    public Result<String> settleItemCopyByPayCurrency(@RequestHeader("shareId") String shareId, @RequestBody InstFeeQueryVO request){
        return ResultUtil.success(instContractFeeService.settleItemCopyByPayCurrency(shareId, request));
    }

    /**
     * 根据 feeItemNo 删除费用信息
     */
    @PostMapping("feeItemDel")
    @DigestLog(isRecord = true)
    public Result<String> feeItemDel(@RequestHeader("shareId") String shareId, @RequestBody InstContractFeeItemVO request){
        return ResultUtil.success(instContractFeeService.feeItemDel(shareId, request));
    }

    /**
     * 根据 settleItemNo 删除结算信息
     */
    @PostMapping("settleItemDel")
    @DigestLog(isRecord = true)
    public Result<String> settleItemDel(@RequestHeader("shareId") String shareId, @RequestBody InstContractSettlementVO request){
        return ResultUtil.success(instContractFeeService.settleItemDel(shareId, request));
    }

}
