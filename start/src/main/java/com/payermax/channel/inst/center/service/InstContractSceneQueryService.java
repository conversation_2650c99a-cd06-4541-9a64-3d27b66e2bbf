package com.payermax.channel.inst.center.service;

import com.payermax.channel.inst.center.common.utils.ExceptionUtils;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.*;
import com.payermax.channel.inst.center.facade.response.contract.*;
import com.payermax.channel.inst.center.service.finder.AbstractInstContractInfoFinder;
import com.payermax.channel.inst.center.service.finder.InstAccountingTypeFinder;
import com.payermax.channel.inst.center.service.finder.InstContractAllFxAndSettleFinder;
import com.payermax.common.lang.model.dto.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/1/6
 * @DESC 机构合约各场景查询服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InstContractSceneQueryService {


    private final InstAccountingTypeFinder instAccountingTypeFinder;
    private final InstContractAllFxAndSettleFinder instContractAllFxAndSettleFinder;


    /**
     * 查询风控费用信息
     */
    public Result<InstRiskFeeInfoResponse> queryRiskFeeInfo(InstRiskFeeQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() ->
                (InstRiskFeeInfoResponse) AbstractInstContractInfoFinder.getStrategy(ContractBizTypeEnum.R).find(request));
    }

    /**
     * 查询争议费用信息
     */
    public Result<InstDisputeFeeResponse> queryDisputeFeeInfo(InstInfoQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() ->
                (InstDisputeFeeResponse) AbstractInstContractInfoFinder.getStrategy(ContractBizTypeEnum.DISP).find(request));
    }

    /**
     * 查询技术服务费用信息
     */
    public Result<InstTechnicalServiceFeeResponse> queryTechnicalServiceFeeInfo(InstTechnicalServiceFeeQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() ->
                (InstTechnicalServiceFeeResponse) AbstractInstContractInfoFinder.getStrategy(ContractBizTypeEnum.TS).find(request));
    }

    /**
     * 查询渠道核对方式
     */
    public Result<InstAccountingTypeResponse> queryAccountingType(InstAccountingTypeQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() ->
                (InstAccountingTypeResponse) instAccountingTypeFinder.find(request));
    }

    /**
     * 查询 VA 费用信息
     */
    public Result<InstVirtualAccountFeeResponse> queryVirtualAccountFeeInfo(InstInfoQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() ->
                (InstVirtualAccountFeeResponse) AbstractInstContractInfoFinder.getStrategy(ContractBizTypeEnum.VA).find(request));
    }

    /**
     * 查询合约所有外汇和结算信息
     */
    public Result<InstContractAllFxAndSettleResponse> queryContractAllFxAndSettleInfo(InstInfoQueryRequest request) {
        return ExceptionUtils.commonTryCatch(() -> (InstContractAllFxAndSettleResponse) instContractAllFxAndSettleFinder.find(request));
    }
}
