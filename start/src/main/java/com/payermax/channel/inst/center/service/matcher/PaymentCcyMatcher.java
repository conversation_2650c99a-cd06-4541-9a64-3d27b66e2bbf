package com.payermax.channel.inst.center.service.matcher;

import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.config.FunnelMatchItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.util.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PaymentCcyMatcher extends AbstractContractMatcher {

    @Override
    protected boolean ignoreFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String paymentCcy = funnelMatchItem.getPaymentCcy();
        AssertUtil.isTrue(!StringUtil.isEmpty(paymentCcy), "", "paymentCcy can not be empty");
        return false;
    }

    @Override
    protected List<InstContractFeeItem> preciseFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        String paymentCcy = funnelMatchItem.getPaymentCcy();
        return feeItems.stream().filter(item -> paymentCcy.equals(item.getPayCurrency())).collect(Collectors.toList());
    }

    @Override
    protected List<InstContractFeeItem> fuzzyFilter(FunnelMatchItem funnelMatchItem, List<InstContractFeeItem> feeItems) {
        return feeItems.stream().filter(item -> CommonConstants.STAR.equals(item.getPayCurrency())).collect(Collectors.toList());
    }
}
