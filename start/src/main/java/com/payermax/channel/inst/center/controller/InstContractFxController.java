package com.payermax.channel.inst.center.controller;

import com.payermax.channel.inst.center.app.request.InstContractFxBatchModifiedReqDTO;
import com.payermax.channel.inst.center.app.request.InstContractProductQueryRequest;
import com.payermax.channel.inst.center.app.response.InstContractFxQueryVO;
import com.payermax.channel.inst.center.app.service.InstContractFeeWorkflowService;
import com.payermax.channel.inst.center.common.utils.ResultUtils;
import com.payermax.channel.inst.center.facade.api.InstBaseInfoQueryFacade;
import com.payermax.channel.inst.center.facade.request.contract.InstContractFxBatchSyncRequest;
import com.payermax.channel.inst.center.service.InstContractFxService;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.infra.ionia.log.digest.core.annotation.DigestLog;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 * @DESC
 */
@Api(tags = "机构合同 FX API")
@RestController
@Slf4j
@RequestMapping("instCenter/contract/fx")
@AllArgsConstructor
public class InstContractFxController {

    private final InstContractFxService fxService;
    private final InstContractFeeWorkflowService feeWorkflowService;
    private final InstBaseInfoQueryFacade baseInfoQueryFacade;


    /**
     * FX 信息列表查询
     */
    @PostMapping("queryFxList")
    @DigestLog(isRecord = true)
    public Result<List<InstContractFxQueryVO>> queryFxList(@Validated @RequestBody InstContractProductQueryRequest request){
        return ResultUtils.success(fxService.queryFxList(request));
    }

    /**
     * FX 信息批量修改
     */
    @PostMapping("fxBatchModified")
    @DigestLog(isRecord = true)
    public Result<Boolean> fxBatchModified(@RequestHeader("shareId") String shareId, @RequestBody InstContractFxBatchModifiedReqDTO request){
        return ResultUtils.success(fxService.fxBatchModified(shareId, request));
    }

    /**
     * 机构 FX 批量同步
     */
    @PostMapping("channelFxBatchSync")
    public  Result<Boolean> channelFxBatchSync(@RequestBody InstContractFxBatchSyncRequest request){
        return ResultUtils.success(fxService.acceptChannelFxBatchSync(request));
    }


    /**
     * FX 批量修改流程回调
     */
    @PostMapping("fxBatchModifiedCallback")
    @DigestLog(isRecord = true)
    public Result<Boolean> fxBatchModifiedCallback(@RequestBody WfProcessEventInfo eventInfo){
        return feeWorkflowService.instContractFxBatchModifiedCallback(eventInfo);
    }
}
