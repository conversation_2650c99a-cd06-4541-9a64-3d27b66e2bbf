package com.payermax.channel.inst.center.service.convertor;

import com.payermax.channel.inst.center.facade.dsl.DSLEntity;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.channel.inst.center.facade.enums.SettleMode;
import com.payermax.channel.inst.center.facade.util.AssertUtil;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import com.payermax.channel.inst.center.service.convertor.calculator.RoundCalculator;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class DslEntity2SettleInfoVoConvertor {

    private static final Map<RoundTypeEnum, RoundCalculator> CALCULATOR_MAP = new HashMap<>();

    public static void registerRoundCalculator(RoundTypeEnum roundTypeEnum, RoundCalculator calculator) {
        CALCULATOR_MAP.put(roundTypeEnum, calculator);
    }

    public SettleInfoVo convertDslEntity2SettleInfoVo(DSLEntity dslEntity, Integer cycleStart, Integer cycleEnd, String timezone) {
        if (dslEntity != null) {
            RoundCalculator calculator = CALCULATOR_MAP.get(dslEntity.getRoundRelativeType());
            AssertUtil.notNull(calculator, "ERROR", dslEntity.getRoundRelativeType().getDesc() + " has no calculator!");
            SettleInfoVo settleInfoVo = calculator.calculate(dslEntity, cycleStart, cycleEnd);
            settleInfoVo.setSkipHoliday(dslEntity.getIsConsiderHoliday());
            settleInfoVo.setSettleMode(SettleMode.CYCLICAL_SETTLE.getCode());
            settleInfoVo.setTimezone(timezone);
            return settleInfoVo;
        }
        return null;
    }
}
