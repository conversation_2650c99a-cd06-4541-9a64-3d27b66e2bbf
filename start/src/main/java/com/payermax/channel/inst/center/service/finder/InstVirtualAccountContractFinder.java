package com.payermax.channel.inst.center.service.finder;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.payermax.channel.inst.center.common.enums.ErrorCodeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBizTypeEnum;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryResponse;
import com.payermax.channel.inst.center.facade.response.contract.InstVirtualAccountFeeResponse;
import com.payermax.channel.inst.center.service.finder.context.InstContractQueryContext;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/2/19
 * @DESC 机构合约 VA 费用查询器
 */
@Slf4j
@Component
public class InstVirtualAccountContractFinder extends AbstractInstContractInfoFinder {

    /**
     * VA 默认支付类型
     */
    @NacosValue(value = "${inst.contract.fee.query.va.paymentMethodType:BankTransfer}", autoRefreshed = true)
    private String PAYMENT_METHOD_TYPE;
    @NacosValue(value = "${inst.contract.fee.query.va.targetOrg:*}", autoRefreshed = true)
    private String TARGET_ORG;
    @NacosValue(value = "${inst.contract.fee.query.va.cardOrg:SETTLEORG}", autoRefreshed = true)
    private String CARD_ORG;

    @PostConstruct
    public void init() {
        registryStrategy(ContractBizTypeEnum.VA, this);
    }

    @Override
    protected void preProcessor(InstContractQueryContext context){
        // 合约查询方式
        setDirectQueryAndCheck(context);
        // 填充 VA 默认标准产品信息
        log.info("VA 默认标准产品信息: {} - {} - {}", PAYMENT_METHOD_TYPE, TARGET_ORG, CARD_ORG);
        context.getRequest().setPaymentMethodType(PAYMENT_METHOD_TYPE);
        context.getRequest().setTargetOrg(TARGET_ORG);
        context.getRequest().setCardOrg(CARD_ORG);
    }

    @Override
    protected void paramCheck(InstContractQueryContext context) {
        InstInfoQueryRequest request = context.getRequest();
        // DIRECT 合约查询模式，无需校验 MID
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPaymentMethodType()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付方式类型不能为空");
        AssertUtil.isTrue(StringUtils.isNotBlank(request.getPayCurrency()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "支付币种不能为空");
        AssertUtil.isTrue(ObjectUtils.isNotNull(request.getTransactionTime()), ErrorCodeEnum.PARAMETER_INVALID.getCode(), "交易时间不能为空");
        AssertUtil.isTrue( StringUtil.isNotBlank(request.getTargetOrg()) || StringUtil.isNotBlank(request.getCardOrg() ),
                ErrorCodeEnum.PARAMETER_INVALID.getCode(), "目标机构和卡组不能全部为空");

    }

    @Override
    protected void findSettle(InstContractQueryContext context) {
        // VA 无结算信息
        context.setSettleItem(null);
    }

    @Override
    protected InstInfoQueryResponse composeResponse(InstContractQueryContext context) {

        InstInfoQueryRequest request = context.getRequest();
        InstVirtualAccountFeeResponse response = instContractRequestAssembler.convertReq2VAResp(request);
        InstContractFeeItem feeItem = context.getFeeItem();
        AssertUtil.isTrue(Objects.nonNull(feeItem), "Error", "feeItem is null!");

        // 通用信息填充
        feeInfoConvertor.responseDefaultFill(response, context);

        // 费用信息
        response.setFeeConfigMap(feeInfoConvertor.feeConfigConvert(feeItem));
        // 换汇信息
        response.setFxConfig(instContractRequestAssembler.convertInstFx2Response(feeItem));
        // 税费
        response.setTaxConfig(feeInfoConvertor.taxConfigConvert(feeItem));

        // VA 无扣款信息、结算信息
        response.setDeductConfig(null);
        response.setSettlementConfig(null);
        return response;
    }

}
