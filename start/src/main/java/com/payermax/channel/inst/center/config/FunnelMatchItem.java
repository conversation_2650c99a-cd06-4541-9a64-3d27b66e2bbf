package com.payermax.channel.inst.center.config;

import com.payermax.channel.inst.center.common.enums.instcontract.CustomerTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.FeeBearerEnum;
import com.payermax.channel.inst.center.facade.enums.contract.CardTypeEnum;
import lombok.Data;

@Data
public class FunnelMatchItem {
    /**
     * 支付币种
     */
    private String paymentCcy;

    /**
     * MCC
     */
    private String mcc;

    /**
     * 二级商户号
     */
    private String subMerchantNo;

    /**
     * 渠道商户号
     */
    private String channelMerchantCode;

    /**
     * 资金源
     */
    private String fundingSource;

    /**
     * 交易国家
     */
    private String transactionCountry;

    /**
     * 清算网络
     */
    private String clearingNetwork;

    /**
     * 费用承担方
     * {@link FeeBearerEnum}
     */
    private String feeBearer;

    /**
     * 客户类型
     * {@link CustomerTypeEnum}
     */
    private String customerType;

    /**
     * 卡类型
     * {@link CardTypeEnum}
     */
    private String cardType;
}
