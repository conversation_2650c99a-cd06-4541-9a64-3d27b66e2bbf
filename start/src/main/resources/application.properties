spring.application.name=channel-inst-center
spring.main.banner-mode=off
spring.main.allow-bean-definition-overriding=true
spring.jackson.default-property-inclusion=non_null
server.port=9021
server.servlet.context-path=/channel-inst-center
server.compression.enabled=true
server.compression.mime-types=application/json,text/html

# nacos 全局配置
nacos.config.server-addr=${nacos.config.server-addr}
nacos.config.dataIds=application.properties
nacos.config.type=properties
nacos.config.group=${spring.application.name}
nacos.config.autoRefresh=true
nacos.config.remoteFirst=true
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.logEnable=false
nacos.config.username=${nacos.config.username}
nacos.config.password=${nacos.config.password}
nacos.config.namespace=${nacos.config.namespace}
nacos.config.enable-remote-sync-config=true

nacos.config.ext-config[0].data-ids=infra-fs.properties
nacos.config.ext-config[0].group=infra_common_config
nacos.config.ext-config[0].type=properties



# mybatis-plus 全局配置，定制化处理
mybatis-plus.typeEnumsPackage=com.payermax.channel.inst.center.enums
mybatis-plus.mapper-locations=classpath*:mapper/**/*.xml
mybatis-plus.configuration.default-enum-type-handler=org.apache.ibatis.type.EnumTypeHandler
mybatis-plus.type-aliases-package=com.payermax.channel.inst.center.infrastructure.entity
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# dubbo指标统一监控
management.endpoints.web.exposure.include=health,info,prometheus
management.endpoints.web.base-path=/
management.endpoints.web.path-mapping.prometheus=/metrics
management.metrics.export.prometheus.enabled=true
management.metrics.tags.application=${spring.application.name}
management.server.port=10108

# sentry错误日志监控 参考文档：https://shimo.im/docs/dPkpKyZlZyHgEmqO
sentry.traces-sample-rate=0.2

# 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
# 不要设置过大，如果过大，启动项目会报错：打开文件数过多
server.undertow.threads.io=16
# 阻塞任务线程池, 当执行类似servlet请求阻塞IO操作, undertow会从这个线程池中取得线程
# 它的值设置取决于系统线程执行任务的阻塞系数，默认值是IO线程数*8
server.undertow.threads.worker=256
# 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
# 每块buffer的空间大小,越小的空间被利用越充分，不要设置太大，以免影响其他应用，合适即可
server.undertow.buffer-size=1024
server.undertow.url-charset=UTF-8
# 是否分配的直接内存(NIO直接分配的堆外内存)
server.undertow.direct-buffers=true

# 钉钉告警机器人 线下token（线上会另行覆盖）
dingTalk.robot.accessToken=2f41767b6e9ed64663aea8a470e39471151b092c34135b512e0fb202e48713f6

#业务配置
effect.delay.seconds=5

#数据源迁移改造配置
database.migrate.pay.start=false

#告警模版
inst.sub.account.alert.message.for.more.than.one=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountQuery.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountQuery.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountChange.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountChange.subAccountName}\n- \u66f4\u6539\u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountChange.status}\n- \u5f02\u5e38\u539f\u56e0\uff1a\u5b9a\u4f4d\u5b50\u7ea7\u8d26\u53f7\u4e3a\u591a\u4e2a\uff0c\u8bf7\u4eba\u5de5\u5904\u7406!
inst.sub.account.alert.message.for.is.null=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountQuery.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountQuery.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountChange.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountChange.subAccountName}\n- \u66f4\u6539\u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountChange.status}\n- \u5f02\u5e38\u539f\u56e0\uff1a\u5b9a\u4f4d\u5b50\u7ea7\u8d26\u53f7\u4e3a\u7a7a!
inst.sub.account.alert.message.for.handle.exception=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountQuery.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountQuery.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountChange.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountChange.subAccountName}\n- \u66f4\u6539\u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountChange.status}\n- \u5f02\u5e38\u539f\u56e0\uff1a\u5b9a\u4f4d\u5b50\u7ea7\u8d26\u53f7\u5f02\u5e38,\u5df2\u5230\u6700\u5927\u91cd\u8bd5\u6b21\u6570!
inst.sub.account.offline.apply.alert.message=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountQuery.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountQuery.subUseType}\n- \u53f7\u6bb5\u751f\u6210\u53f7\u7801\uff1a#!{subAccountQuery.numberSegmentNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountQuery.subAccountName}\n- \u7533\u8bf7\u5546\u6237\u53f7\uff1a#!{subAccountQuery.merchantNo}\n- \u7533\u8bf7\u5b50\u5546\u6237\u53f7\uff1a#!{subAccountQuery.subMerchantNo}\n- \u5e94\u7528\u573a\u666f\uff1a#!{subAccountQuery.scenes}
inst.sub.account.alert.message.for.api.apply.exception=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountInfo.accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{accountInfo.instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{accountInfo.currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountInfo.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountInfo.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountInfo.numberSegmentNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountInfo.subAccountName}\n- \u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountInfo.status}\n - \u54cd\u5e94\u7f16\u7801\uff1a#!{result.code}\n - \u54cd\u5e94\u4fe1\u606f\uff1a#!{result.msg}\n- \u5f02\u5e38\u539f\u56e0\uff1aAPI\u6a21\u5f0f\u8c03\u7528\u5916\u90e8\u6e20\u9053\u5f02\u5e38!
inst.sub.account.alert.message.for.api.apply.inquiry.exception=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountInfo.accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{accountInfo.instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{accountInfo.currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountInfo.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountInfo.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountInfo.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountInfo.subAccountName}\n- \u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountInfo.status}\n - \u54cd\u5e94\u7f16\u7801\uff1a#!{result.code}\n - \u54cd\u5e94\u4fe1\u606f\uff1a#!{result.msg}\n- \u5f02\u5e38\u539f\u56e0\uff1aAPI\u6a21\u5f0f\u8865\u507f\u67e5\u8be2\u8c03\u7528\u5916\u90e8\u6e20\u9053\u5f02\u5e38!
inst.sub.account.alert.message.for.api.activation.exception=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountInfo.accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{accountInfo.instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{accountInfo.currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountInfo.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountInfo.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountInfo.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountInfo.subAccountName}\n- \u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountInfo.status}\n - \u54cd\u5e94\u7f16\u7801\uff1a#!{result.code}\n - \u54cd\u5e94\u4fe1\u606f\uff1a#!{result.msg}\n- \u5f02\u5e38\u539f\u56e0\uff1aAPI\u6a21\u5f0f\u6fc0\u6d3b\u8c03\u7528\u5916\u90e8\u6e20\u9053\u5f02\u5e38!
inst.sub.account.alert.message.for.api.activation.inquiry.exception=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountInfo.accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{accountInfo.instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{accountInfo.currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountInfo.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountInfo.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountInfo.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountInfo.subAccountName}\n- \u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountInfo.status}\n - \u54cd\u5e94\u7f16\u7801\uff1a#!{result.code}\n - \u54cd\u5e94\u4fe1\u606f\uff1a#!{result.msg}\n- \u5f02\u5e38\u539f\u56e0\uff1aAPI\u6a21\u5f0f\u6fc0\u6d3b\u8865\u507f\u67e5\u8be2\u8c03\u7528\u5916\u90e8\u6e20\u9053\u5f02\u5e38!
inst.sub.account.alert.message.for.api.close.exception=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountInfo.accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{accountInfo.instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{accountInfo.currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountInfo.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountInfo.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountInfo.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountInfo.subAccountName}\n- \u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountInfo.status}\n - \u54cd\u5e94\u7f16\u7801\uff1a#!{result.code}\n - \u54cd\u5e94\u4fe1\u606f\uff1a#!{result.msg}\n- \u5f02\u5e38\u539f\u56e0\uff1aAPI\u6a21\u5f0f\u5173\u95ed\u8c03\u7528\u5916\u90e8\u6e20\u9053\u5f02\u5e38!
inst.sub.account.alert.message.for.offline.close.message=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountInfoEntity.accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{accountInfoEntity.instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{accountInfoEntity.currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountInfoEntity.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountInfoEntity.subUseType}\n- \u751f\u6210\u5b50\u7ea7\u8d26\u53f7\uff1a#!{subAccountInfoEntity.subAccountNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountInfoEntity.subAccountName}\n- \u5b50\u7ea7\u8d26\u53f7\u72b6\u6001\uff1a#!{subAccountInfoEntity.status}!

infra.ionia.notice.group.mapGroup= {"push-inst-info-exceptionNotify":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","push-sub-account-exceptionNotify":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","push-sub-account-handleNotify":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2","other":"https://open.feishu.cn/open-apis/bot/v2/hook/fa1afc0c-8c6d-4dc3-a567-da379614e9a2"}
