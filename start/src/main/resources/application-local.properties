dubbo.application.name=${spring.application.name}
dubbo.registry.register=false
dubbo.consumer.check=false
dubbo.provider.version=1.0
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.application.metadata-type=remote
dubbo.registry.address=nacos://${nacos.config.server-addr}?namespace=${nacos.config.namespace}
dubbo.registry.username=${nacos.config.username}
dubbo.registry.password=${nacos.config.password}

spring.cloud.nacos.discovery.server-addr=${nacos.config.server-addr}
spring.cloud.nacos.discovery.namespace=${nacos.config.namespace}
spring.cloud.nacos.discovery.group=FEIGN_GROUP
spring.cloud.nacos.discovery.username=${nacos.config.username}
spring.cloud.nacos.discovery.password=${nacos.config.password}
spring.cloud.loadbalancer.nacos.enabled=true
spring.cloud.nacos.discovery.register-enabled=true

spring.main.allow-bean-definition-overriding=true
spring.cloud.nacos.config.enable=true
spring.cloud.nacos.config.server-addr=${nacos.config.server-addr}
spring.cloud.nacos.config.namespace=${nacos.config.namespace}
spring.cloud.nacos.config.username=${nacos.config.username}
spring.cloud.nacos.config.password=${nacos.config.password}
spring.cloud.nacos.config.group=${spring.application.name}
spring.cloud.nacos.config.file-extension=yaml
spring.cloud.nacos.config.shared-configs[0].data-id=infra-fs.properties
spring.cloud.nacos.config.shared-configs[0].group=infra_common_config
spring.cloud.nacos.config.shared-configs[0].type=properties


spring.redis.database=10
spring.redis.host=$ENV(REDIS_MASTER_SLAVE_TOKEN_ADDRESS)
spring.redis.port=6379
spring.redis.password=$ENV(REDIS_MASTER_SLAVE_TOKEN)
spring.redis.timeout=3000
spring.redis.ssl=true
spring.redis.jedis.pool.max-active=8
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0

# ????
ushareit.redisson.lock.server.address=${spring.redis.host}:${spring.redis.port}
ushareit.redisson.lock.server.type=standalone
ushareit.redisson.lock.server.password=${spring.redis.password}
ushareit.redisson.lock.server.database=${spring.redis.database}
ushareit.redisson.lock.server.sslEnable=true

# Distributed-ID redis address
distributed.redis.masterSlave.addr=${spring.redis.host}:${spring.redis.port}
distributed.redis.password=${spring.redis.password}
distributed.redis.ssl=true

# ????????
multilevel-cache.redis.prefix=instCenter:

management.endpoints.web.exposure.include=health,info,prometheus
management.endpoints.web.base-path=/
management.endpoints.web.path-mapping.prometheus=/metrics
management.server.port=10108
management.metrics.distribution.percentiles.fusion=0.95,0.99


#?????????
database.migrate.pay.start=false
database.migrate.pay.finish=true

#?????
#****************************************************Master**************************************************
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.datasource.master.driverClassName=software.aws.rds.jdbc.mysql.Driver
spring.datasource.dynamic.datasource.master.url=**********************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=dev_v8
spring.datasource.dynamic.datasource.master.password=$ENV(PAAS:RDS:MYSQL-V8:PSWD)
spring.datasource.dynamic.datasource.master.type=com.alibaba.druid.pool.DruidDataSource
#???????
spring.datasource.dynamic.datasource.master.druid.initial-size=2
spring.datasource.dynamic.datasource.master.druid.min-idle=1
spring.datasource.dynamic.datasource.master.druid.max-active=4
spring.datasource.dynamic.datasource.master.druid.max-wait=6000
spring.datasource.dynamic.datasource.master.druid.validation-query=select 1
#???????????????????????jdbc Statement???void setQueryTimeout(int seconds)??
#spring.datasource.dynamic.datasource.master.druid.validation-query-timeout = 5
spring.datasource.dynamic.datasource.master.druid.test-on-borrow=false
spring.datasource.dynamic.datasource.master.druid.test-while-idle=true
spring.datasource.dynamic.datasource.master.druid.test-on-return=false
#???????????????????????????????
spring.datasource.dynamic.datasource.master.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.datasource.master.druid.break-after-acquire-failure=false
spring.datasource.dynamic.datasource.master.druid.remove-abandoned-timeout-millis=1800
#****************************************************new Master**************************************************
spring.datasource.dynamic.datasource.new-master.driverClassName=software.aws.rds.jdbc.mysql.Driver
spring.datasource.dynamic.datasource.new-master.url=**********************************************************************************************************************************************
spring.datasource.dynamic.datasource.new-master.username=dev_v8
spring.datasource.dynamic.datasource.new-master.password=$ENV(PAAS:RDS:MYSQL-V8:PSWD)
spring.datasource.dynamic.datasource.new-master.type=com.alibaba.druid.pool.DruidDataSource
#???????
spring.datasource.dynamic.datasource.new-master.druid.initial-size=2
spring.datasource.dynamic.datasource.new-master.druid.min-idle=1
spring.datasource.dynamic.datasource.new-master.druid.max-active=4
spring.datasource.dynamic.datasource.new-master.druid.max-wait=6000
spring.datasource.dynamic.datasource.new-master.druid.validation-query=select 1
#???????????????????????jdbc Statement???void setQueryTimeout(int seconds)??
#spring.datasource.dynamic.datasource.new-master.druid.validation-query-timeout = 5
spring.datasource.dynamic.datasource.new-master.druid.test-on-borrow=false
spring.datasource.dynamic.datasource.new-master.druid.test-while-idle=true
spring.datasource.dynamic.datasource.new-master.druid.test-on-return=false
#???????????????????????????????
spring.datasource.dynamic.datasource.new-master.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.datasource.new-master.druid.break-after-acquire-failure=false
spring.datasource.dynamic.datasource.new-master.druid.remove-abandoned-timeout-millis=1800

spring.datasource.dynamic.datasource.ods.lazy=true
spring.datasource.dynamic.datasource.ods.driverClassName=software.aws.rds.jdbc.mysql.Driver
spring.datasource.dynamic.datasource.ods.url=******************************************************************************************************************************
spring.datasource.dynamic.datasource.ods.username=lurenjie
spring.datasource.dynamic.datasource.ods.password=ENC(d64f57a17e4b0ff2dc68a21ee)
spring.datasource.dynamic.datasource.ods.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.ods.druid.initial-size=2
spring.datasource.dynamic.datasource.ods.druid.min-idle=1
spring.datasource.dynamic.datasource.ods.druid.max-active=4
spring.datasource.dynamic.datasource.ods.druid.max-wait=6000
spring.datasource.dynamic.datasource.ods.druid.validation-query=select 1
spring.datasource.dynamic.datasource.ods.druid.test-on-borrow=false
spring.datasource.dynamic.datasource.ods.druid.test-while-idle=true
spring.datasource.dynamic.datasource.ods.druid.test-on-return=false
spring.datasource.dynamic.datasource.ods.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.datasource.ods.druid.break-after-acquire-failure=false
spring.datasource.dynamic.datasource.ods.druid.remove-abandoned-timeout-millis=1800

#spring datasource druid config
spring.datasource.dynamic.druid.query-timeout=6000
spring.datasource.dynamic.druid.transaction-query-timeout=6000
spring.datasource.dynamic.druid.remove-abandoned-timeout=1800
spring.datasource.dynamic.druid.remove-abandoned=true
spring.datasource.dynamic.druid.wall.multi-statement-allow=true
spring.datasource.dynamic.druid.filters=stat,config,slf4j,wall
spring.datasource.druid.filter.slf4j.enabled=true
spring.datasource.druid.filter.slf4j.statement-create-after-log-enabled=false
spring.datasource.druid.filter.slf4j.statement-close-after-log-enabled=false
spring.datasource.druid.filter.slf4j.result-set-open-after-log-enabled=false
spring.datasource.druid.filter.slf4j.result-set-close-after-log-enabled=false

# ??WallFilter
spring.datasource.dynamic.druid.wall.insert-allow=true
spring.datasource.dynamic.druid.wall.update-allow=true


#spring ??????
#???????????????
spring.servlet.multipart.max-file-size=10MB
#???????????????
spring.servlet.multipart.max-request-size=10MB

feign.hystrix.enabled=false
feign.compression.request.enabled=true
feign.compression.response.enabled=true
feign.okhttp.enabled=true
feign.httpclient.enabled=true

# sentry env
sentry.dsn=https://<EMAIL>/107
sentry.environment=dev
sentry.release=release20221103
sentry.traces-sample-rate=
sentry.sample-rate=0.01

#????
effect.delay.seconds=15

fintech.dingtalk.group.mapGroup={"push-inst-info-exceptionNotify":"https://dingding-test.payermax.com/robot/send?access_token=86b5e13bf15096c91a5f1d0b7b5c6a74f01cb70239ed4e7148f5324651b0f0c7","push-sub-account-exceptionNotify":"https://dingding-test.payermax.com/robot/send?access_token=a45fbddb0fab7f5aa6c5263c571e7b46efe9d84a6504f27904823e9e7342a867","push-sub-account-handleNotify":"https://dingding-test.payermax.com/robot/send?access_token=a45fbddb0fab7f5aa6c5263c571e7b46efe9d84a6504f27904823e9e7342a867","push-fx-settle-query-result-handleNotify":"https://dingding-test.payermax.com/robot/send?access_token=6b3f2e00a7a260832abf989216072e1269d50a522dda8c1691d5f4d98985df24"}
fintech.dingtalk.group.connect-timeout=2000

fin-operating-log.name=fin-operating-log
fin-operating-log.url=http://$ENV(BACK_END_INTERNEL_LAN_ADDRESS)/fin-operating-log

group.contractSystem.url=http://**************:11080/grcv5/foreignController/v1/createQD
group.contractSystem.timeout=5000

channel.sign.key=TVCAoozjDRdDnsM4
internal.front.url=http://$ENV(BACK_END_INTERNEL_LAN_ADDRESS)/in-pay-channel-front
internal.channel.url=http://$ENV(BACK_END_INTERNEL_LAN_ADDRESS)/in-pay-channel
fintech.channel.activation.url=
fintech.channel.apply.url=


# rocketmq name sever ?? ; ??
rocketmq.name-server=$ENV(ROCKETMQ_ADDRESS)
# rocket mq producer group
rocketmq.producer.group=pg_${spring.application.name}
# ???????????3000ms
rocketmq.producer.send-message-timeout=3001
# ?????????????????? 2
rocketmq.producer.retry-times-when-send-failed=2
# ?????????????????? 2
rocketmq.producer.retry-times-when-send-async-failed=2
# ???????????? 4M
rocketmq.producer.max-message-size=4194304
# ????body???????????? 4096???Consumer???????????
rocketmq.producer.compress-message-body-threshold=4096
# ??????????????, ??? 10
rocketmq.consumer.pull-batch-size=10

rocket.topic.channel.account.status.change.notify=topic_channel_account_status_change
rocket.topic.inst-contract.fee-item.modified.notify=topic_inst_center_contract_fee_item_modified

logging.level.com.baomidou.example.mapper=debug

# xxl-job
xxl.job.admin.addresses=http://$ENV(XXL_JOB_ADDRESSS)/xxl-job-admin
xxl.job.executor.port=9041
xxl.job.executor.name=${spring.application.name}

# ????????????
inst.funds.account.pre-apply.ding.map={"RIYADBANKSA01_1000413099940_SA_SAR":20,"HECTOKR01_31001839_KR_KRW":50}
inst.funds.account.number-segment.ding.map={"RIYADBANKSA01_1000413099940_SA_SAR":100}
inst.funds.account.params-bucket.ding.map={"MAYBANKMY01_82374972387_MY_MYR":999}
inst.funds.account.online.file.apply.map={"SCBHK01_36807780088_HK_HKD":"SCB_VA_OPEN_ACCOUNT_APPLY","SCBHK01_36807780177_HK_CNY":"SCB_VA_OPEN_ACCOUNT_APPLY","SCBHK01_36807900563_HK_USD":"SCB_VA_OPEN_ACCOUNT_APPLY","":""}
inst.funds.account.special.name.value=NETBANKPH01_41001000015_PH_PHP,DBSHK01_001220363_HK_USD,DBSHK01_1220363_HK_CNY,DBSHK01_1220363_HK_HKD,HECTOKR01_43716011531226_KR_KRW
mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

inst.in.channel.payment.method.map={"":""}
inst.out.channel.payment.method.map={"":""}

channel.fxInfo.query.oldVersion=true
channel.fxInfo.query.newVersion=true
channel.fxInfo.query.errIsNotify=true

# excel????
# TAX
excel.import.inst-contract.parse.fee.startSeparator=TAX>>>
excel.import.inst-contract.parse.fee.endSeparator=<<<TAX
# ??
excel.import.inst-contract.parse.fee.fieldMapping.zh.taxType=??
# ??
excel.import.inst-contract.parse.fee.fieldMapping.zh.taxRate=??
# ????
excel.import.inst-contract.parse.fee.fieldMapping.zh.taxCalculateType=????
# ??????
excel.import.inst-contract.parse.fee.fieldMapping.zh.deductible=????

# SettlementDate
excel.import.inst-contract.parse.settle-date.startSeparator=SettlementDate>>>
excel.import.inst-contract.parse.settle-date.endSeparator=<<<SettlementDate
# ???
excel.import.inst-contract.parse.settle-date.fieldMapping.zh.billDate=???
# ??????
excel.import.inst-contract.parse.settle-date.fieldMapping.zh.transactionStartDate=??????
# ??????
excel.import.inst-contract.parse.settle-date.fieldMapping.zh.transactionEndDate=??????
# ???
excel.import.inst-contract.parse.settle-date.fieldMapping.zh.paymentDate=???
# ???
excel.import.inst-contract.parse.settle-date.fieldMapping.zh.arriveDate=???

# excel????

# ??????
# ?????com.payermax.channel.inst.center.domain.enums.contract.content.CurrencyExchangeTimingEnum
# Transaction Day??????
enum.desc.currency-exchange-timing.exchange-in-trade-day.zh=Transaction Day
enum.desc.currency-exchange-timing.exchange-in-trade-day.en=Transaction Day
# Settlement Day????????
#enum.desc.currency-exchange-timing.exchange-in-settlement-day.zh=Settlement Day
#enum.desc.currency-exchange-timing.exchange-in-settlement-day.en=Settlement Day
# Non Exchange????
#enum.desc.currency-exchange-timing.on-exchange.zh=No Exchange
#enum.desc.currency-exchange-timing.on-exchange.en=No Exchange

# Settlement Day????????
enum.desc.currency-exchange-timing.exchange-in-settlement-day.zh=???????
enum.desc.currency-exchange-timing.exchange-in-settlement-day.en=???????
# Non Exchange?????
enum.desc.currency-exchange-timing.on-exchange.zh=????
enum.desc.currency-exchange-timing.on-exchange.en=????

# ?????com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateBaseModeEnum
# Txn/(1+Tax Ratio) : ????/(1+??)
enum.desc.fee-calculate-base-mode.vat-include-in-tpv.zh=Txn/(1+TaxRatio)
enum.desc.fee-calculate-base-mode.vat-include-in-tpv.en=Txn/(1+TaxRatio)
# Txn: ????
#enum.desc.fee-calculate-base-mode.fee-on-tpv.zh=Txn
#enum.desc.fee-calculate-base-mode.fee-on-tpv.en=Txn
# Txn+Tax Fee:????+??
enum.desc.fee-calculate-base-mode.fee-on-tpv-and-tax.zh=Txn+TaxFee
enum.desc.fee-calculate-base-mode.fee-on-tpv-and-tax.en=Txn+TaxFee
# Txn-Tax
enum.desc.fee-calculate-base-mode.fee-on-tpv-except-tax.zh=Txn-TaxFee
enum.desc.fee-calculate-base-mode.fee-on-tpv-except-tax.en=Txn-TaxFee

# Txn: ????
enum.desc.fee-calculate-base-mode.fee-on-tpv.zh=????
enum.desc.fee-calculate-base-mode.fee-on-tpv.en=????


# ?????com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateTimingEnum
# Transaction Day????
#enum.desc.fee-calculate-timing.calculate-in-trade-day.zh=Transaction Day
#enum.desc.fee-calculate-timing.calculate-in-trade-day.en=Transaction Day
# Settlement Day????
#enum.desc.fee-calculate-timing.calculate-in-settlement-day.zh=Settlement Day
#enum.desc.fee-calculate-timing.calculate-in-settlement-day.en=Settlement Day

# Transaction Day????
enum.desc.fee-calculate-timing.calculate-in-trade-day.zh=???
enum.desc.fee-calculate-timing.calculate-in-trade-day.en=???
# Settlement Day????
enum.desc.fee-calculate-timing.calculate-in-settlement-day.zh=???
enum.desc.fee-calculate-timing.calculate-in-settlement-day.en=???

# ?????com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateTypeEnum
# Percentage?????
#enum.desc.fee-calculate-type.single-rate.zh=Percentage
#enum.desc.fee-calculate-type.single-rate.en=Percentage
# Fix?????
#enum.desc.fee-calculate-type.single-money.zh=Fix
#enum.desc.fee-calculate-type.single-money.en=Fix
# Percentage/Fix Combined?????
#enum.desc.fee-calculate-type.single-combine.zh=Percentage/Fix Combined
#enum.desc.fee-calculate-type.single-combine.en=Percentage/Fix Combined
# ????
enum.desc.fee-calculate-type.step-combine.zh=\u9636\u68AF\u8BA1\u8D39
enum.desc.fee-calculate-type.step-combine.en=\u9636\u68AF\u8BA1\u8D39

# Percentage?????
enum.desc.fee-calculate-type.single-rate.zh=????
enum.desc.fee-calculate-type.single-rate.en=????
# Fix?????
enum.desc.fee-calculate-type.single-money.zh=????
enum.desc.fee-calculate-type.single-money.en=????
# Percentage/Fix Combined?????
enum.desc.fee-calculate-type.single-combine.zh=????
enum.desc.fee-calculate-type.single-combine.en=????

# ???????com.payermax.channel.inst.center.domain.enums.contract.content.RoundingModeEnum
# ROUND_UP??0?1
enum.desc.rounding-mode.up.zh=ROUND_UP
enum.desc.rounding-mode.up.en=ROUND_UP
# ROUND_DOWN???
enum.desc.rounding-mode.down.zh=ROUND_DOWN
enum.desc.rounding-mode.down.en=ROUND_DOWN
# HALF_UP?????
#enum.desc.rounding-mode.half-up.zh=HALF_UP
#enum.desc.rounding-mode.half-up.en=HALF_UP
# HALF_DOWN?????
enum.desc.rounding-mode.half-down.zh=HALF_DOWN
enum.desc.rounding-mode.half-down.en=HALF_DOWN
# HALF_EVEN???????
enum.desc.rounding-mode.half-even.zh=HALF_EVEN
enum.desc.rounding-mode.half-even.en=HALF_EVEN

# HALF_UP?????
enum.desc.rounding-mode.half-up.zh=????
enum.desc.rounding-mode.half-up.en=????

# ?????com.payermax.channel.inst.center.domain.enums.contract.content.TaxCalculateFormulaEnum
# TPV Tax:????*???/1+??)
enum.desc.tax-calculate-formula.vat-included-in-tpv.zh=Txn*TaxRatio/(1+TaxRatio)
enum.desc.tax-calculate-formula.vat-included-in-tpv.en=Txn*TaxRatio/(1+TaxRatio)
# TPV Tax by Txn :????*??
enum.desc.tax-calculate-formula.tax-on-tpv.zh=Txn*TaxRatio
enum.desc.tax-calculate-formula.tax-on-tpv.en=Txn*TaxRatio
# MDR Tax:?????*??
#enum.desc.tax-calculate-formula.tax-on-fee.zh=MDR*TaxRatio
#enum.desc.tax-calculate-formula.tax-on-fee.en=MDR*TaxRatio
# TPV Tax exclude MDR :(????-????*??0
enum.desc.tax-calculate-formula.tax-on-tpv-exclude-fee.zh=(Txn-MDR)*TaxRatio
enum.desc.tax-calculate-formula.tax-on-tpv-exclude-fee.en=(Txn-MDR)*TaxRatio

# MDR Tax:?????*??
enum.desc.tax-calculate-formula.tax-on-fee.zh=?????*??
enum.desc.tax-calculate-formula.tax-on-fee.en=?????*??

# ???????com.payermax.channel.inst.center.domain.enums.contract.content.WithdrawMethodEnum
# ????
#enum.desc.withdraw-method.money-arrive-auto.zh=Automatic Settlement
#enum.desc.withdraw-method.money-arrive-auto.en=Automatic Settlement
# ????
#enum.desc.withdraw-method.withdraw-manually.zh=Manual Withdrawal
#enum.desc.withdraw-method.withdraw-manually.en=Manual Withdrawal

# ??????
enum.desc.withdraw-method.money-arrive-auto.zh=??????
enum.desc.withdraw-method.money-arrive-auto.en=??????
# ??????
enum.desc.withdraw-method.withdraw-manually.zh=??????
enum.desc.withdraw-method.withdraw-manually.en=???

# ??????

aws.common.bucket.name=sg-pay-static-apse1/src/channel/instCenter/dev

# S3 ??
#infra.ionia.fs.cdn.mapping={'https://sg-pay-private-ap-southeast-1.s3.ap-southeast-1.amazonaws.com/src/':'https://img-cdn-inner.payermax.com/'}



#???
inst.bucketName=sg-pay-private-ap-southeast-1/src/instCenter/dev
inst.regionsName=ap-southeast-1
cdn.url.config.pair=https://s3.ap-southeast-1.amazonaws.com/sg-pay-private-ap-southeast-1/src/,https://img-cdn-inner.payermax.com/
inst.s3.urlPrefix=https://img-cdn-inner.payermax.com/instCenter/dev/

# ???? header ???
com.payermax.infra.digest.log.record.headers=connection,content-length,content-type,host,sw8,sw8-correlation,user-agent

excel.import.inst-contract.parse.cardPaymentMethods=CardPay,ApplePay,GooglePay,GGG

inst.funds.contract.payout.deduct.map={"20240117113015FE0000785077000007":"HKD"}

inst.sub.account.offline.apply.alert.message=\n- \u673a\u6784\u8d26\u53f7\uff1a#!{accountNo}\n- \u673a\u6784\u7f16\u7801\uff1a#!{instCode}\n- \u7533\u8bf7\u5e01\u79cd\uff1a#!{currency}\n- \u7533\u8bf7\u4e1a\u52a1\u952e\uff1a#!{subAccountQuery.businessKey}\n- \u4f7f\u7528\u7c7b\u578b\uff1a#!{subAccountQuery.subUseType}\n- \u53f7\u6bb5\u751f\u6210\u53f7\u7801\uff1a#!{subAccountQuery.numberSegmentNo}\n- \u5b50\u7ea7\u8d26\u53f7\u540d\u79f0\uff1a#!{subAccountQuery.subAccountName}\n- \u7533\u8bf7\u5546\u6237\u53f7\uff1a#!{subAccountQuery.merchantNo}\n- \u7533\u8bf7\u5b50\u5546\u6237\u53f7\uff1a#!{subAccountQuery.subMerchantNo}\n- \u5e94\u7528\u573a\u666f\uff1a#!{subAccountQuery.scenes}

# OMC ?????
omc.workflow.process.feeModifiedProcess.key=process_inst-center-fee-modified-event-review
omc.workflow.process.feeModifiedProcess.desc=????????????
omc.workflow.process.settleModifiedProcess.key=process_inst-center-settle-modified-event-review
omc.workflow.process.settleModifiedProcess.desc=????????????
omc.workflow.process.settleBatchModifiedProcess.key=process_inst-center-settle-batch-modified-event-review
omc.workflow.process.settleBatchModifiedProcess.desc=??????????????
omc.workflow.process.settleBatchModifiedProcess.formMsgTemplate=? ?? #!{instCode}??????#!{contractEntity}??????#!{payCurrency}?????? #!{instProductName} \n #if(#!{diffMsg.transactionStartDate.left} || #!{diffMsg.transactionStartDate.right}) ??????-????#!{diffMsg.transactionStartDate.left}? -> ?#!{diffMsg.transactionStartDate.right}?\n #end #if(#!{diffMsg.transactionEndDate.left} || #!{diffMsg.transactionEndDate.right}) ??????-????#!{diffMsg.transactionEndDate.left}? -> ?#!{diffMsg.transactionEndDate.right}?\n #end #if(#!{diffMsg.billDate.left} || #!{diffMsg.billDate.right}) ?????#!{diffMsg.billDate.left}? -> ?#!{diffMsg.billDate.right}?\n #end #if(#!{diffMsg.paymentDate.left} || #!{diffMsg.paymentDate.right}) ?????#!{diffMsg.paymentDate.left}? -> ?#!{diffMsg.paymentDate.right}?\n #end #if(#!{diffMsg.arriveDate.left} || #!{diffMsg.arriveDate.right}) ?????#!{diffMsg.arriveDate.left}? -> ?#!{diffMsg.arriveDate.right}?\n #end #if(#!{diffMsg.exchangeDate.left} || #!{diffMsg.exchangeDate.right}) ?????#!{diffMsg.exchangeDate.left}? -> ?#!{diffMsg.exchangeDate.right}?\n #end #if(#!{diffMsg.minSettleAmount.left} || #!{diffMsg.minSettleAmount.right}) ??????#!{diffMsg.minSettleAmount.left}? -> ?#!{diffMsg.minSettleAmount.right}?\n #end #if(#!{diffMsg.accountType.left} || #!{diffMsg.accountType.right}) ??????#!{diffMsg.accountType.left}? -> ?#!{diffMsg.accountType.right}?\n #end #if(#!{diffMsg.accountId.left} || #!{diffMsg.accountId.right}) ?? ID??#!{diffMsg.accountId.left}? -> ?#!{diffMsg.accountId.right}?\n #end #if(#!{diffMsg.withdrawMethod.left} || #!{diffMsg.withdrawMethod.right}) ??????#!{diffMsg.withdrawMethod.left}? -> ?#!{diffMsg.withdrawMethod.right}?\n #end #if(#!{diffMsg.timezone.left} || #!{diffMsg.timezone.right}) ????#!{diffMsg.timezone.left}? -> ?#!{diffMsg.timezone.right}?\n #end
omc.workflow.process.fxBatchModifiedProcess.key=process_inst-center-fx-batch-modified-event-review
omc.workflow.process.fxBatchModifiedProcess.desc=????FX????????
omc.workflow.process.fxBatchModifiedProcess.formMsgTemplate=? ?? #!{instCode}??????#!{contractEntity}??????#!{payCurrency}?????? #!{instProductName} \n #if(#!{diffMsg.fxSpread}) FX ????#!{diffMsg.fxSpread.left}? -> ?#!{diffMsg.fxSpread.right}?\n #end #if(#!{diffMsg.currencyExchangeTime}) ??????#!{diffMsg.currencyExchangeTime.left}? -> ?#!{diffMsg.currencyExchangeTime.right}?\n #end #if(#!{noChange}) ????? \n #end

omc.workflow.process.instFundsAccount.save.excluding-fields=instId,bankOperator,accountOpeningTime,isSupportCustomName,isNeedActivation,bankName,isDeleted,isSupportPreApply,subAccountMode,isSupportSubAccount,utcCreate,utcModified,subAccountRule,bankAddress,isSupportDirectPayment,isSupportDirectLink,isSupportDayEndBalance,isSupportIntradayBalance

# ??????
# ???????? ??-????
inst.financialCalendar.bank.allow.set=HK-USD
inst.financialCalendar.allowApp=channel-inst-center,funds-fx-omc,omc-channel-exchange,solution-commun-center

# ???????????
inst.contract.validate.owner.fee=???,???
inst.contract.validate.owner.fx=???,???
inst.contract.validate.owner.settlement=???