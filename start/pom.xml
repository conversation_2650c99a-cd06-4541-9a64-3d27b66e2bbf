<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-inst-center-parent</artifactId>
        <version>1.0.0-RELEASE</version>
    </parent>
    <artifactId>start</artifactId>
    <packaging>jar</packaging>
    <name>start</name>
    <version>${revision}</version>
    <dependencies>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-app</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ushareit.fintech.parent</groupId>
            <artifactId>fintech-components-rpc-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-log4j-2.x</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>fintech-components-dubbo-extension-qos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>fintech-components-mybatis-interceptor</artifactId>
            <version>1.0.0-20221024-RELEASE</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.payermax.channel.inst.center.ChannelInstCenterApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.payermax.infra</groupId>
                <artifactId>check-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.1.0</version>
                <dependencies>
                    <dependency>
                        <groupId>custom-rule</groupId>
                        <artifactId>custom-rule-sample</artifactId>
                        <version>1.0.0-SNAPSHOT</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>enforce-banned-dependencies</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <myCustomRule implementation="com.payermax.infra.custom.rule.sample.SnapshotInterceptorRule">
                                    <!--只有当打release包时规则生效-->
                                    <onlyWhenRelease>false</onlyWhenRelease>
                                    <!--告警级别-->
                                    <level>ERROR</level>
                                    <!--这些包排除掉，因为历史原因，允许为snapshot-->
                                    <excludes>
                                        <!--历史版本代码，无人维护，代码在fintech-parent sharding分支 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-shardingphysical-core:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-security:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.payermax.common:common-exception-handler:jar:[1.0.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-jasypt-dubbo:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-jasypt-kms:jar:[1.0-SNAPSHOT]</exclude>
                                        <!--未部署release分支，无人维护 -->
                                        <exclude>com.ushareit.fintech.parent:fintech-components-jasypt:jar:[1.0-SNAPSHOT]</exclude>
                                    </excludes>
                                    <message>依赖列表中包含SNAPSHOT版本禁止部署，请升级到RELEASE版本再部署：</message>
                                </myCustomRule>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <!-- 摘要日志缺陷版本 -->
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.5-20220719-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.2-20220621-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.1-20220620-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.4.0-20220620-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.9-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.8-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.6-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.3-20220617-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.3-20220613-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.2-20220613-RELEASE]</exclude>
                                        <exclude>com.payermax.common:fintech-components-log:[1.3.1-20220530-RELEASE]</exclude>
                                    </excludes>
                                    <message>存在有缺陷的摘要日志版本,请升级至最新版本</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>com.alibaba:fastjson</exclude>
                                    </excludes>
                                    <includes>
                                        <!-- fastjson缺陷版本,仅支持1.2.83以上的版本 -->
                                        <include>com.alibaba:fastjson:1.2.83</include>
                                    </includes>
                                    <message>请将fastjson版本升级到1.2.83版本及以上</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>com.payermax.common:fintech-components-mq-kafka-change-server-apollo</exclude>
                                        <exclude>com.payermax.common:fintech-components-mq-kafka-change-server-nacos</exclude>
                                    </excludes>
                                    <message>Kafka集群迁移工作已完成,请移除对应的自动切换组件</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>org.jboss.resteasy:*:[3.0.19.Final]</exclude>
                                    </excludes>
                                    <message>resteasy该版本有内存泄露缺陷,请升级到3.0.20.Final及以上版本</message>
                                </bannedDependencies>
                                <bannedDependencies>
                                    <level>ERROR</level>
                                    <excludes>
                                        <exclude>com.payermax.common:fintech-components-multilevel-cache-aspectj:[1.0.2-20220615-RELEASE]</exclude>
                                    </excludes>
                                    <message>存在有缺陷的多级缓存版本,请升级至最新版本</message>
                                </bannedDependencies>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>start</finalName>
    </build>
</project>
