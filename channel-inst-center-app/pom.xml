<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payermax.channel</groupId>
        <artifactId>channel-inst-center-parent</artifactId>
        <version>1.0.0-RELEASE</version>
    </parent>

    <artifactId>channel-inst-center-app</artifactId>
    <packaging>jar</packaging>
    <name>channel-inst-center-app</name>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>operating-log-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ushareit.fintech.base</groupId>
            <artifactId>fintech-base-manage-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-domain</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-inst-center-infrastructure</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hystrix-core</artifactId>
                    <groupId>com.netflix.hystrix</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-tool-distributed-lock</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.statemachine</groupId>
            <artifactId>spring-statemachine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-rocketMQ</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating.upms</groupId>
            <artifactId>upms-context</artifactId>
            <version>1.0.9-20240112-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.payermax.infra</groupId>
                    <artifactId>ionia-log-digest-http</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.payermax.infra</groupId>
                    <artifactId>ionia-log-digest-annotation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.payermax.infra</groupId>
                    <artifactId>ionia-log-digest-dubbo3</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
    </dependencies>

</project>
