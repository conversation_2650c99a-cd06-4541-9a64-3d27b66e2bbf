package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * DD调研问卷VO
 *
 * <AUTHOR>
 * @date 2022/5/18 18:00
 */
@Data
public class InstDdSurveyVO implements Serializable {

    private static final long serialVersionUID = -6214675199670127605L;

    private Long id;

    private Long ddId;

    private String hasPaymentLicense;

    private String hasTransMonitor;

    private String hasDueDiligence;

    private String hasSanctionsScan;

    private String hasPciCertification;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date pciCertificationDate;

    private Long surveyAttachId;

    private String remark;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

}
