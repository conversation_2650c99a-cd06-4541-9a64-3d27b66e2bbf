package com.payermax.channel.inst.center.app.assembler.domain;

import com.payermax.channel.inst.center.app.dto.fundsAgreement.InstFundsAgreementContextDTO;
import com.payermax.channel.inst.center.app.request.InstFundsAgreementQueryRequestDTO;
import com.payermax.channel.inst.center.app.response.InstFundsAgreementQueryVO;
import com.payermax.channel.inst.center.facade.response.fundsAgreement.FundsAgreementQueryResponse;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBizAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsAgreementPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFundsSettleRulePO;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @date 2024/3/21
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {})
public interface InstFundsAgreementAssembler {

    /**
     * 业务协议上下文转业务协议PO
     * @param context 业务协议上下文
     * @return 业务协议PO
     */
    InstBizAgreementPO bizAgreementContext2Po(InstFundsAgreementContextDTO.BizAgreement context);


    /**
     * 资金协议上下文转为资金协议PO
     * @param context 资金协议上下文
     * @return 资金协议PO
     */
    @Mappings({
            @Mapping(target = "bizAgreement", ignore = true),
            @Mapping(target = "settleRules", ignore = true)
    })
    InstFundsAgreementPO fundsAgreementContext2Po(InstFundsAgreementContextDTO.FundsAgreement context);

    /**
     * 结算规则上下文转结算规则PO
     * @param context 结算规则上下文
     * @return 结算规则PO
     */
    InstFundsSettleRulePO fundsSettleRuleContext2Po(InstFundsAgreementContextDTO.FundsSettleRule context);

    /**
     * 查询参数转资金协议PO
     * @param request 查询参数
     * @return 资金协议PO
     */
    @Mappings({
            @Mapping(target = "type", source = "request.fundsAgreementType"),
            @Mapping(target = "name", source = "request.fundsAgreementName"),
            @Mapping(target = "clearingCcy", source = "request.clearingCurrency"),
            @Mapping(target = "status", source = "request.fundsAgreementStatus")
    })
    InstFundsAgreementPO request2FundsPo(InstFundsAgreementQueryRequestDTO request);

    @Named(value = "po2QueryVO")
    FundsAgreementQueryResponse.FundsSettleRule po2QueryVO(InstFundsSettleRulePO po);

    @Mappings({
            @Mapping(target = "name", source = "fundsAgreement.bizAgreement.name"),
            @Mapping(target = "bizAgreementNo", source = "fundsAgreement.bizAgreement.bizAgreementNo"),
            @Mapping(target = "fundsAgreementNo", source = "fundsAgreement.fundsAgreementNo"),
            @Mapping(target = "initiator", source = "fundsAgreement.bizAgreement.initiator"),
            @Mapping(target = "counter", source = "fundsAgreement.bizAgreement.counter"),
            @Mapping(target = "bizAgreementType", source = "fundsAgreement.bizAgreement.type"),
            @Mapping(target = "fundsAgreementType", source = "fundsAgreement.type"),
            @Mapping(target = "clearingCcy", source = "fundsAgreement.clearingCcy"),
            @Mapping(target = "clearingPattern", source = "fundsAgreement.clearingPattern"),
            @Mapping(target = "fundsSettleRule", source = "fundsSettleRule", qualifiedByName = "po2QueryVO"),
    })
    FundsAgreementQueryResponse composeFundsAgreementQueryResponse(InstFundsAgreementPO fundsAgreement, InstFundsSettleRulePO fundsSettleRule);


    /**
     * 资金协议 PO 转 VO
     * @param po 资金协议 PO
     * @return  资金协议 VO
     */
    InstFundsAgreementQueryVO fundsPo2QueryVo(InstFundsAgreementPO po);

    /**
     * 资金协议复制
     * @param po 资金协议 PO
     * @return 资金协议 PO
     */
    InstFundsAgreementPO fundsAgreementCopy(InstFundsAgreementPO po);
}
