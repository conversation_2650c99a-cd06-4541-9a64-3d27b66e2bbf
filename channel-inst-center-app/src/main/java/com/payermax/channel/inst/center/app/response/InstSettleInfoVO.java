package com.payermax.channel.inst.center.app.response;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * <AUTHOR> t<PERSON>
 * @version 2022-10-20 11:10 AM
 */
@Getter
@Setter
public class InstSettleInfoVO {
    
    /**
     * 结算方式(自动结算/开票结算)
     */
    private String settleMethod;

    /**
     * 结算周期.按周期结算/指定结算日/其他
     */
    private String settleMode;

    /**
     * 结算单位(天、周、月、双月)
     */
    private String settleCycleUnit;

    /**
     * 结算周期
     */
    private String settleCycle;

    /**
     * 周期限制(例如,以周为结算单位,每周三固定结算)
     */
    private String settleCycleOtherLimit;

    /**
     * 结算节假日
     */
    private String settleHolidayCountry;

    /**
     * 节假日处理方式(下一个工作日/上一个工作日)
     */
    private String settleForHoliday;

    /**
     * 结算币种
     */
    private String settleCurrency;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InstSettleInfoVO)) return false;
        InstSettleInfoVO that = (InstSettleInfoVO) o;
        return Objects.equals(settleMethod, that.settleMethod) && Objects.equals(settleMode, that.settleMode) && Objects.equals(settleCycleUnit, that.settleCycleUnit) && Objects.equals(settleCycle, that.settleCycle) && Objects.equals(settleCycleOtherLimit, that.settleCycleOtherLimit) && Objects.equals(settleHolidayCountry, that.settleHolidayCountry) && Objects.equals(settleForHoliday, that.settleForHoliday) && settleCurrency.equals(that.settleCurrency);
    }

    @Override
    public int hashCode() {
        return Objects.hash(settleMethod, settleMode, settleCycleUnit, settleCycle, settleCycleOtherLimit, settleHolidayCountry, settleForHoliday, settleCurrency);
    }
}
