package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.app.request.account.InstBankAccountSaveOrUpdateRequest;
import com.payermax.channel.inst.center.app.response.InstBankAccountVO;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.utils.ListUtils;
import com.payermax.channel.inst.center.domain.entity.account.InstBankAccount;
import com.payermax.channel.inst.center.domain.enums.account.UseTypeEnum;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBankAccountPO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/29
 * @DESC
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, Optional.class, ListUtils.class, CommonConstants.class, UseTypeEnum.class})
public interface InstBankAccountAssembler {

    InstBankAccountAssembler INSTANCE = Mappers.getMapper(InstBankAccountAssembler.class);

    @Mappings({
            @Mapping(target = "accountUse", expression = "java(ListUtils.enumList2String(domain.getAccountUse(), CommonConstants.COMMA))"),
            @Mapping(target = "rechargeCanUseCcy", expression = "java(ListUtils.list2String(domain.getRechargeCanUseCcy(), CommonConstants.COMMA))")
    })
    InstBankAccountPO domain2Po(InstBankAccount domain);


    @Mappings({
            @Mapping(target = "accountUse", expression = "java(ListUtils.string2EnumList(domain.getAccountUse(), CommonConstants.COMMA, UseTypeEnum.class))"),
            @Mapping(target = "rechargeCanUseCcy", expression = "java(ListUtils.string2List(domain.getRechargeCanUseCcy(), CommonConstants.COMMA))")
    })
    InstBankAccount po2Domain(InstBankAccountPO domain);

    @Named(value = "po2Vo")
    InstBankAccountVO po2Vo(InstBankAccountPO po);

    @IterableMapping(qualifiedByName = "po2Vo")
    List<InstBankAccountVO> po2VoList(List<InstBankAccountPO> po);


    @Mappings({
            @Mapping(target = "accountUse", source = "accountUseList"),
            @Mapping(target = "rechargeCanUseCcy", source = "rechargeCanUseCcyList")
    })
    InstBankAccount request2Domain(InstBankAccountSaveOrUpdateRequest request);

    InstBankAccountPO request2Po(InstBankAccountReqDTO request);


    @Mappings({
            @Mapping(target = "accountUseList", source = "accountUse"),
            @Mapping(target = "rechargeCanUseCcyList", source = "rechargeCanUseCcy")
    })
    InstBankAccountVO domain2Vo(InstBankAccount domain);

}
