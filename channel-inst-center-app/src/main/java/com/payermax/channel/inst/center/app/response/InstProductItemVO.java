package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/27
 * @DESC
 */
@Data
public class InstProductItemVO implements Serializable {

    private String draftId;

    private String status;

    private String instCode;

    private String contractEntity;

    private String businessType;

    private String instProductName;

    private String paymentType;

    private String target;

    private String contractNo;

    private Date effectiveTime;

    private Date utcCreate;

    private Date utcModified;

}
