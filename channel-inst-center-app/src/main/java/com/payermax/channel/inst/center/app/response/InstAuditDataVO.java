package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstAuditDataVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 00:17
 * @Version 1.0
 */
@Data
public class InstAuditDataVO implements Serializable {


    private static final long serialVersionUID = -9118377231570392085L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 关联业务类型 KYC：KYC审核，DD：DD审核
     */
    private String businessType;

    /**
     * 关联业务单号
     */
    private String businessNo;

    /**
     * 资料类型
     */
    private String dataType;

    /**
     * 资料附件
     */
    private String dataAttachId;

    /**
     * 证件号
     */
    private String idNumber;

    /**
     * 证件有效期
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date idValidityDate;

    /**
     * 是否已有水印
     */
    private String hasWatermarkWord;

    /**
     * 备注
     */
    private String remark;
}
