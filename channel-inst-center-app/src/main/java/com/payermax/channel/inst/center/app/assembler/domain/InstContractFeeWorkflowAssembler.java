package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractSettlementItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstProcessDockPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/2/21
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, FeeConfig.class, Optional.class
})
public interface InstContractFeeWorkflowAssembler {


    /**
     * 费用信息转流程 dock 信息
     */
    @Mappings({
            @Mapping(target = "createUser", source = "shareId"),
            @Mapping(target = "processStatus", source = "processStatus"),
            @Mapping(target = "businessType", source = "businessType"),
            @Mapping(target = "businessId", source = "originData.instContractFeeItemNo"),
            @Mapping(target = "originalFormContent", expression = "java(JSON.toJSONString(originData))"),
            @Mapping(target = "formContent", expression = "java(JSON.toJSONString(modifiedData))"),
    })
    InstProcessDockPO fee2ProcessDockPo(String shareId, String processStatus, String businessType, InstContractFeeItemPO originData, InstContractFeeItemPO modifiedData);


    /**
     * 费用信息列表转流程 dock 信息
     */
    @Mappings({
            @Mapping(target = "createUser", source = "shareId"),
            @Mapping(target = "processStatus", source = "processStatus"),
            @Mapping(target = "businessType", source = "businessType"),
            @Mapping(target = "businessId", source = "businessId"),
            @Mapping(target = "originalFormContent", expression = "java(JSON.toJSONString(originData))"),
            @Mapping(target = "formContent", expression = "java(JSON.toJSONString(modifiedData))"),
    })
    InstProcessDockPO feeItems2ProcessDock(String shareId, String processStatus, String businessType, String businessId, List<InstContractFeeItemPO> originData, List<InstContractFeeItemPO> modifiedData);

    /**
     * 结算信息转流程 dock 信息
     */
    @Mappings({
            @Mapping(target = "createUser", source = "shareId"),
            @Mapping(target = "processStatus", source = "processStatus"),
            @Mapping(target = "businessType", source = "businessType"),
            @Mapping(target = "businessId", source = "originData.instContractSettlementItemNo"),
            @Mapping(target = "originalFormContent", expression = "java(JSON.toJSONString(originData))"),
            @Mapping(target = "formContent", expression = "java(JSON.toJSONString(modifiedData))"),
    })
    InstProcessDockPO settle2ProcessDockPo(String shareId, String processStatus, String businessType, InstContractSettlementItemPO originData, InstContractSettlementItemPO modifiedData);

    /**
     * 结算信息列表转流程 dock 信息
     */
    @Mappings({
            @Mapping(target = "createUser", source = "shareId"),
            @Mapping(target = "processStatus", source = "processStatus"),
            @Mapping(target = "businessType", source = "businessType"),
            @Mapping(target = "businessId", source = "businessId"),
            @Mapping(target = "originalFormContent", expression = "java(JSON.toJSONString(originData))"),
            @Mapping(target = "formContent", expression = "java(JSON.toJSONString(modifiedData))"),
    })
    InstProcessDockPO  settleItems2ProcessDock(String shareId, String processStatus, String businessType, String businessId, List<InstContractSettlementItemPO> originData, List<InstContractSettlementItemPO> modifiedData);

}
