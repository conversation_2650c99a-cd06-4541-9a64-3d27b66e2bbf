package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstReportMerchantEntity;

/**
 * 机构商户报备Service
 *
 * <AUTHOR>
 * @date 2022/6/4 17:10
 */
public interface InstReportMerchantService {

    /**
     * 保存
     *
     * @param record
     * @return
     */
    int save(InstReportMerchantEntity record);

    /**
     * 根据申请单号查询
     *
     * @param queryEntity
     * @return
     */
    InstReportMerchantEntity query(InstReportMerchantEntity queryEntity);

}
