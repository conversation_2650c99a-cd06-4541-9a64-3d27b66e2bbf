package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.domain.enums.contract.content.InstSettleAccountType;
import com.payermax.channel.inst.center.domain.enums.contract.content.WithdrawMethodEnum;
import com.payermax.channel.inst.center.facade.dsl.enums.dsl.RoundTypeEnum;
import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2023/6/21  10:35 AM
 */
@Data
public class InstFxContractSettleInfoVO {

    /**
     * 换汇类型(交易换汇/结算换汇/提现换汇/其他)
     */
    private String exchangeType;

    /**
     * 结算打款信息
     */
    private ContractSettlePaymentInfo settlePaymentInfo;

    /**
     * 结算周期类型（DAY、WEEK、MONTH）
     */
    private RoundTypeEnum settleCycleType;

    /**
     * 结算周期条款（可能有多条的情况）
     * <p>
     * 例如：月内1-10日11日结算；11-21的22日结算；22-月底的次月1日结算
     */
    private List<ContractSettleCycleInfo> contractSettleCycleInfos;


    @Data
    public static class ContractSettlePaymentInfo implements Serializable {

        /**
         * 结算方式(自动结算/开票结算)
         */
        private WithdrawMethodEnum settleMethod;

        /**
         * 结算币种
         */
        private String settleCurrency;

        /**
         * 最小起结金额
         */
        private Money minSettleAmount;

        /**
         * 银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
         */
        private InstSettleAccountType settleAccountType;

        /**
         * 结算账户的accountId
         */
        private String settleAccountId;

    }

    @Data
    public static class ContractSettleCycleInfo implements Serializable {

        private static final long serialVersionUID = -2819913096464716089L;

        /**
         * 交易周期范围描述
         */
        private TransactionDateCycleInfo transactionDateCycleInfo;

        /**
         * 对应交易周期内，交易的结算周期信息
         */
        private SettleCycleInfo settleCycleInfo;
    }

    @Data
    public static class SettleCycleInfo implements Serializable {

        private static final long serialVersionUID = -5904733953018061790L;

        /**
         * 结算单位(天、周、月)
         */
        private RoundTypeEnum settleCycleType;

        /**
         * 结算周期（天、周、月结算单位下的偏移，+，-; 如天为周期，+2表示两天后，周为周期，-2表示两周前）
         */
        private Integer settleCycle;

        /**
         * 结算周期绝对偏移量（具体偏移几天，+，-） - 在周、月场景下，只有上面的信息无法确定是哪天
         * <p>
         * 如周为结算单位，结算周期是下周，但还需要知道下周哪天，需要指定是周几
         * 这里的sign：+表示从周期第一天向后偏移,-从周期最后一天向前偏移
         */
        private Integer settleCycleDefiniteOffset;

        /**
         * 是否考虑节假日
         */
        private Boolean isConsiderHoliday;

        /**
         * 国家
         */
        private String settleHolidayCountry;
    }

    @Data
    public static class TransactionDateCycleInfo implements Serializable {

        /**
         * 交易日范围描述单位
         */
        private RoundTypeEnum settleUnit;

        /**
         * 开始
         */
        private Integer start;

        /**
         * 结束(-1表示最后一天，比如是周日或者月底)
         */
        private Integer end;
    }
}
