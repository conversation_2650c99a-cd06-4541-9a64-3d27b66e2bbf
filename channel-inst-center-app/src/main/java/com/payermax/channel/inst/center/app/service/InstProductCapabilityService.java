package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstProductCapabilityEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstProductCapabilityQueryEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 20:36
 */
public interface InstProductCapabilityService {
    /**
     * 保存产品能力
     *
     * @param record
     * @return
     */
    int save(InstProductCapabilityEntity record);

    /**
     * 保存产品能力
     *
     * @param records
     * @return
     */
    int saveBatch(List<InstProductCapabilityEntity> records);

    /**
     * 更新产品能力
     *
     * @param record
     * @return
     */
    int update(InstProductCapabilityEntity record);

    /**
     * 获取机构产品能力列表
     *
     * @param queryEntity
     * @return
     */
    List<InstProductCapabilityEntity> getList(InstProductCapabilityQueryEntity queryEntity);

    /**
     * 根据产品编码查询产品能力
     *
     * @param productCodes
     * @return
     */
    List<InstProductCapabilityEntity> getByProductCodes(List<String> productCodes);

    /**
     * 根据产品编码和操作版本查询产品能力
     *
     * @param productCode
     * @param version
     * @return
     */
    List<InstProductCapabilityEntity> getByProductCodeAndVersion(String productCode, String version);

    /**
     * 根据产品编码和能力编码查询产品能力
     *
     * @param productCode
     * @param capabilityCodes
     * @return
     */
    List<InstProductCapabilityEntity> getByProductCodeAndCapabilityCodes(String productCode, List<String> capabilityCodes);

    /**
     * 根据能力编码查询产品能力
     *
     * @param capabilityCodes
     * @return
     */
    List<InstProductCapabilityEntity> getByCapabilityCodes(List<String> capabilityCodes);


    /**
     * 根据产品编码删除产品能力
     *
     * @param productCode
     * @return
     */
    int deleteByProductCode(String productCode);

    /**
     * 根据产品编码和操作版本删除产品能力
     *
     * @param productCode
     * @param version
     * @return
     */
    int deleteByProductCodeAndVersion(String productCode, String version);

    /**
     * 根据产品编码和能力删除产品能力
     *
     * @param productCode
     * @param capabilityCodes
     * @return
     */
    int deleteByProductCodeAndCapabilityCode(String productCode, List<String> capabilityCodes);

}
