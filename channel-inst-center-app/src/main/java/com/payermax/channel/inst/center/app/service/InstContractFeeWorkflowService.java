package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.app.dto.workflow.InstContractFeeFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractSettleFormMsgDTO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractSettlementItemPO;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.omc.portal.workflow.facade.common.dto.WfProcessEventInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/29
 * @DESC 机构中心费用工作流
 */
public interface InstContractFeeWorkflowService {


    /**
     * 费用信息修改发起流程
     * @param shareId 用户 ID
     * @param formMessage 审批表单信息
     * @param originFeeItem 原始信息
     * @param newFeeItem 修改信息
     * @return 是否成功
     */
    Boolean feeConfigModifiedProcessStart(String shareId, InstContractFeeFormMsgDTO formMessage
            , InstContractFeeItemPO originFeeItem, InstContractFeeItemPO newFeeItem);


    /**
     * 费用信息修改流程回调
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    Result<Boolean> instContractFeeModifiedCallback(WfProcessEventInfo eventInfo);


    /**
     * 费用信息修改发起流程
     * @param shareId  用户 ID
     * @param formMessage 审批表单信息
     * @param originSettleItem 原始信息
     * @param newSettleItem 修改信息
     * @return 是否成功
     */
    Boolean settleConfigModifiedProcessStart(String shareId, InstContractSettleFormMsgDTO formMessage
            , InstContractSettlementItemPO originSettleItem, InstContractSettlementItemPO newSettleItem);


    /**
     * 结算信息修改流程回调
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    Result<Boolean> instContractSettleModifiedCallback(WfProcessEventInfo eventInfo);


    /**
     * 结算信息批量修改发起流程
     * @param shareId  用户 ID
     * @param formMsg      审批表单信息
     * @param originSettleItems 原始信息
     * @param modifiedSettleItems 修改信息
     * @param settleItemNos 结算No列表
     * @param businessKey 业务唯一键
     * @return 是否成功
     */
    Boolean settleConfigBatchModifiedProcessStart(String shareId, List<InstContractSettleFormMsgDTO> formMsg
            , List<InstContractSettlementItemPO> originSettleItems, List<InstContractSettlementItemPO> modifiedSettleItems, List<String> settleItemNos, String businessKey);



    /**
     * 结算信息批量修改流程回调
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    Result<Boolean> instContractSettleBatchModifiedCallback(WfProcessEventInfo eventInfo);

    /**
     * FX 批量修改流程发起
     * @param shareId 用户 ID
     * @param formMsg 审批表单信息
     * @param originItems 原始信息
     * @param modifiedItems 修改信息
     * @param itemNos 编号列表
     * @param instCode 机构编码
     * @return 是否成功
     */
    Boolean fxBatchModifiedProcessStart(String shareId, List<InstContractFeeFormMsgDTO> formMsg
            , List<InstContractFeeItemPO> originItems, List<InstContractFeeItemPO> modifiedItems, List<String> itemNos, String instCode);


    /**
     * FX 批量修改流程回调
     * @param eventInfo 流程信息
     * @return 是否成功
     */
    Result<Boolean> instContractFxBatchModifiedCallback(WfProcessEventInfo eventInfo);
}
