package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstProductVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 15:21
 */
@Data
public class InstProductVO implements Serializable {

    private static final long serialVersionUID = 5426710231282827890L;

    @ApiModelProperty(notes = "产品编码")
    private String productCode;

    @ApiModelProperty(notes = "机构标识")
    private String instId;

    @ApiModelProperty(notes = "产品名称")
    private String productName;

    @ApiModelProperty(notes = "渠道类型")
    private String channelType;

    @ApiModelProperty(notes = "支付方式类型")
    private String paymentMethodType;

    @ApiModelProperty(notes = "客户类型")
    private String customerType;

    @ApiModelProperty(notes = "有无行业限制 Y:是,N:否")
    private String isLimitMcc;

    @ApiModelProperty(notes = "行业限制详情 json")
    private String mccDetail;

    private String isDivideMid;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "扩展配置 json")
    private String extraInfo;

    @ApiModelProperty(notes = "机构产品能力集合")
    private List<InstProductCapabilityVO> productCapabilityList;

    /**
     * 标识一次操作
     */
    @ApiModelProperty(hidden = true)
    private String version;

    /**
     * 一次能力操作对应的国家
     */
    @ApiModelProperty(hidden = true)
    private String countrys;

    /**
     * 一次能力操作对应的目标机构
     */
    @ApiModelProperty(notes = "目标机构集合，多个直接用逗号相连")
    private String targetOrgs;

    /**
     * 一次能力操作对应的卡组织
     */
    @ApiModelProperty(notes = "卡组织集合，多个直接用逗号相连")
    private String cardOrgs;

    /**
     * 一次能力操作对应的扩展信息
     */
    @ApiModelProperty(hidden = true)
    private String capabilityExtraInfo;
}
