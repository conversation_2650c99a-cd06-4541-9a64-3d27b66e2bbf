package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构商户报备VO
 *
 * <AUTHOR>
 * @date 2022/6/4 17:30
 */
@Data
public class InstReportMerchantVO implements Serializable {

    private static final long serialVersionUID = 8173786920090216031L;

    private Long id;

    private Long requirementOrderId;

    private Long instId;

    private String channelType;

    private String isNeedReportMerchant;

    private String reportRequire;

    private String reportType;

    private String reportProcessTime;

    private String reportTemplate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

}
