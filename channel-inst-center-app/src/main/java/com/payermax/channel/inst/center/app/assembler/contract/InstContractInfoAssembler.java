package com.payermax.channel.inst.center.app.assembler.contract;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.payermax.channel.inst.center.app.dto.contract.InstContractBatchVerifyContext;
import com.payermax.channel.inst.center.app.dto.contract.InstContractVersionInfoDto;
import com.payermax.channel.inst.center.app.request.contract.InstContractAccountingTypeModifyRequest;
import com.payermax.channel.inst.center.app.request.contract.InstContractInfoQueryRequest;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.facade.request.contract.InstContractBatchVerifyRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.response.InstContractBatchVerifyResponse;
import com.payermax.channel.inst.center.facade.response.contract.InstContractMidQueryResponse;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractBaseInfoPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractMidMappingPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractVersionInfoPO;
import com.payermax.common.lang.model.dto.response.RowResponse;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/9
 * @DESC
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {})
public interface InstContractInfoAssembler {

    InstContractInfoAssembler INSTANCE = Mappers.getMapper(InstContractInfoAssembler.class);

    @Mappings({
            @Mapping(target = "contractNo", source = "base.contractNo"),
            @Mapping(target = "instCode", source = "base.instCode"),
            @Mapping(target = "status", source = "base.status"),
            @Mapping(target = "versionStatus", source = "version.status"),
            @Mapping(target = "newlyCreated", ignore = true),
            @Mapping(target = "accountingTypeJson", source = "version.accountingType", qualifiedByName = "convertAccountingTypeJson"),
            @Mapping(target = "accountingTypeList", source = "version.accountingType", qualifiedByName = "convertAccountingTypeList")
    })
    InstContractVersionInfoDto contractAndVersion2Dto(InstContractBaseInfo base, InstContractVersionInfo version);

    InstContractVersionInfoPO accountingTypeModifyReq2Po(InstContractAccountingTypeModifyRequest request);



    InstContractVersionInfo deepCopy(InstContractVersionInfo version);

    @Named("convertAccountingTypeList")
    default List<Pair<String,String>> convertAccountingTypeList(String accountingType) {
        if(StringUtils.isBlank(accountingType)){
            return null;
        }
        JSONObject accountTypeJson = JSONObject.parseObject(accountingType);
        return accountTypeJson.entrySet().stream()
                .map(entry -> new Pair<>(entry.getKey(), String.valueOf(entry.getValue())))
                .collect(Collectors.toList());
    }

    @Named("convertAccountingTypeJson")
    default JSONObject convertAccountingTypeJson(String accountingType) {
        if (StringUtils.isBlank(accountingType)) {
            return null;
        }
        return JSONObject.parseObject(accountingType);
    }


    @Named("convertBaseInfoDomainList")
    List<InstContractBaseInfo> convertBaseInfoDomainList(List<InstContractBaseInfoPO> poList);


    @Mapping(target = "status", expression = "java(ContractStatusEnum.ACTIVATED.name())")
    InstContractBaseInfoPO request2Po(InstContractInfoQueryRequest request);

    @Mapping(target = "rows", qualifiedByName = "convertBaseInfoDomainList")
    RowResponse<InstContractBaseInfo> poRow2Domain(RowResponse<InstContractBaseInfoPO> poRowResponse);

    RowResponse<InstContractVersionInfoDto> buildContractVersionInfoDtoRowResponse(Long total, List<InstContractVersionInfoDto> rows);


    InstContractMidQueryResponse po2Response(InstContractMidMappingPO po);


    CopyOnWriteArrayList<InstContractBatchVerifyContext.VerifyItemDto> verifyItem2Dto(List<InstContractBatchVerifyRequest.VerifyItem> verifyItem);

    InstInfoQueryRequest verifyItem2QueryRequest(InstContractBatchVerifyContext.VerifyItemDto verifyItem, String channelMerchantCode, String payCurrency, long transactionTime);


    @IterableMapping(qualifiedByName = "verifyItem2FailMsg")
    List<InstContractBatchVerifyResponse.VerifyFailMsg> verifyItem2FailMsgs(List<InstContractBatchVerifyContext.VerifyItemDto> list);

    @Named("verifyItem2FailMsg")
    @Mapping(target = "msg", source = "verifyFailMsgList")
    InstContractBatchVerifyResponse.VerifyFailMsg verifyItem2FailMsg(InstContractBatchVerifyContext.VerifyItemDto item);
}
