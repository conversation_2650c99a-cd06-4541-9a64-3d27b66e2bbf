package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstAccountNumberSegmentMappingEntity;


/**
 * 机构账号可用号段观关联中间表Service
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstAccuntNumberSegmentMappingService {

    /**
     * 更新中间表状态信息
     *
     * @param instAccountNumberSegmentMappingEntity
     * @return
     */
    int updateByEntity(InstAccountNumberSegmentMappingEntity instAccountNumberSegmentMappingEntity);

}
