package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构对账VO
 *
 * <AUTHOR>
 * @date 2022/6/4 17:35
 */
@Data
public class InstReconcileVO implements Serializable {

    private static final long serialVersionUID = -7010711818071166287L;

    private Long id;

    private Long requirementOrderId;

    private Long instId;

    private String channelType;

    private String invoiceProvider;

    private String reconcileMethod;

    private String reconcileTemplate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

}
