package com.payermax.channel.inst.center.app.response;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 2022-10-20 11:09 AM
 */
@Getter
@Setter
public class InstFxSettleInfoVO {

    /**
     * 换汇类型(交易换汇/结算换汇/提现换汇/其他)
     * SettlementDay/TransactionDay/WithdrawDay
     */
    private String exchangeType;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 机构结算信息
     */
    private InstSettleInfoVO instSettleInfoVO;

    private Boolean isUseNewVersionQuery = Boolean.FALSE;
}
