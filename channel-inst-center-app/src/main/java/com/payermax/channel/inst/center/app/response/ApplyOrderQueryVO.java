package com.payermax.channel.inst.center.app.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @ClassName ApplyOrderQueryVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 15:26
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class ApplyOrderQueryVO extends InstApplyOrderVO implements Serializable {

    private static final long serialVersionUID = -2074637408589101545L;

    private InstBrandVO instBrandVO;

    private InstBaseInfoVO instBaseInfoVO;

    private InstDdVO instDdVO;
    //合同单号
    private String contractNo;
    //是否已有产品配置
    private Boolean isHasProductConfigFlag;
    //是否已有集成单
    private Boolean hasRequirementOrderId;
}
