package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarDTO;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarHolidayDTO;
import com.payermax.channel.inst.center.app.dto.calendar.InstFinancialCalendarInitTemplateDTO;
import com.payermax.channel.inst.center.app.manage.calendar.InstFinancialCalendarContext;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarHolidayRequest;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarInitTemplateRequest;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarRequest;
import com.payermax.channel.inst.center.app.request.calendar.InstFinancialCalendarSaveRequest;
import com.payermax.channel.inst.center.common.constants.CommonConstants;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.common.utils.CalendarUtil;
import com.payermax.channel.inst.center.common.utils.ListUtils;
import com.payermax.channel.inst.center.domain.entity.businessDraft.InstBusinessDraft;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendar;
import com.payermax.channel.inst.center.domain.entity.calendar.InstFinancialCalendarHoliday;
import com.payermax.channel.inst.center.domain.enums.calendar.CalendarStatusEnum;
import com.payermax.channel.inst.center.domain.enums.calendar.HolidayOperateEnum;
import com.payermax.channel.inst.center.domain.enums.calendar.HolidayTypeEnum;
import com.payermax.channel.inst.center.facade.request.calendar.*;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.facade.response.calendar.*;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarPO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/26
 * @DESC
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, Optional.class, FeeConfig.class, TaxConfig.class, StringUtils.class, BigDecimal.class, LocalDate.class, Objects.class,
                CalendarUtil.class, HolidayOperateEnum.class, CommonConstants.class, BusinessTypeEnum.class, OperateModuleEnum.class, OperateTypeEnum.class,
                InstProcessStatusEnum.class, CalendarStatusEnum.class, SerializerFeature.class, HolidayTypeEnum.class, ListUtils.class
        })
public interface InstFinancialCalendarAssembler {


    InstFinancialCalendarAssembler INSTANCE = Mappers.getMapper(InstFinancialCalendarAssembler.class);

    /**
     * po转dto
     *
     * @param po po
     * @return dto
     */
    @Mappings({
            @Mapping(target = "displayStatus", source = "status"),
            @Mapping(target = "weekendList", source = "weekendList", qualifiedByName = "string2List")
    })
    InstFinancialCalendarDTO po2dto(InstFinancialCalendarPO po);


    /**
     * 填充关联流程信息
     *
     * @param dto   dto
     * @param draft 流程信息
     */
    @Mappings({
            @Mapping(target = "relationProcessStatus", source = "draft.status"),
            @Mapping(target = "relationProcessOperator", source = "draft.owner"),
            @Mapping(target = "displayStatus", source = "draft.status"), // draft.status 为空时使用日历状态
            @Mapping(target = "status", ignore = true),
    })
    void fillCalendarProcessMsg(@MappingTarget InstFinancialCalendarDTO dto, InstBusinessDraft draft);

    @Mapping(target = "weekendList", source = "weekendList", qualifiedByName = "list2String")
    InstFinancialCalendarPO domain2Po(InstFinancialCalendar domain);

    @Mapping(target = "weekendList", source = "weekendList", qualifiedByName = "string2List")
    InstFinancialCalendar po2Domain(InstFinancialCalendarPO po);

    InstFinancialCalendarHolidayPO domain2Po(InstFinancialCalendarHolidayPO po);

    InstFinancialCalendarHoliday po2Domain(InstFinancialCalendarHolidayPO po);

    InstFinancialCalendarHolidayPO domain2Po(InstFinancialCalendarHoliday po);


    InstFinancialCalendarPO request2Po(InstFinancialCalendarRequest request);

    InstFinancialCalendarHolidayPO request2Po(InstFinancialCalendarHolidayRequest request);


    /**
     * po分页对象转dto分页对象
     */
    @Mapping(target = "records", source = "dtoList")
    Page<InstFinancialCalendarDTO> poPage2DtoPage(Page<InstFinancialCalendarPO> poPage, List<InstFinancialCalendarDTO> dtoList);

    /**
     * 初始化请求转上下文
     */
    @Mappings({
            @Mapping(target = "calendarYear", source = "calendarYear"),
            @Mapping(target = "country", expression = "java(StringUtils.isNotBlank(request.getCountry()) ? request.getCountry() : CommonConstants.STAR)"),
            @Mapping(target = "currency", expression = "java(StringUtils.isNotBlank(request.getCurrency()) ? request.getCurrency() : CommonConstants.STAR)"),
            @Mapping(target = "description", source = "description"),
            @Mapping(target = "calendarType", source = "calendarType"),
            @Mapping(target = "weekendList", source = "weekendList"),
            @Mapping(target = "status", expression = "java(CalendarStatusEnum.VALID)"),
            @Mapping(target = "owner", source = "owner"),
    })
    InstFinancialCalendar request2Calendar(InstFinancialCalendarSaveRequest request);


    /**
     * 领域对象转节假日 PO
     */
    @Mappings({
            @Mapping(target = "calendarId", source = "calendar.calendarId"),
            @Mapping(target = "description", source = "holiday.description"),
            @Mapping(target = "holidayMonth", expression = "java(holiday.getHolidayDate().getMonth())"),
    })
    InstFinancialCalendarHoliday dto2Holiday(InstFinancialCalendarHolidayDTO holiday, InstFinancialCalendar calendar);


    /**
     * 节假日对象转 DTO
     */
    InstFinancialCalendarHolidayDTO domain2Dto(InstFinancialCalendarHoliday holiday);

    /**
     * 领域对象转日历 DTO
     */
    InstFinancialCalendarDTO domain2Dto(InstFinancialCalendar calendar);

    /**
     * 日历模板初始化 DTO 转换
     */
    InstFinancialCalendarInitTemplateDTO initCalendarTemplate(InstFinancialCalendarInitTemplateRequest request, String calendarId, List<InstFinancialCalendarHolidayDTO> holidayList);

    /**
     * 周末对象构建
     */
    @Mappings({
            @Mapping(target = "holidayDate", source = "date"),
            @Mapping(target = "isWorkday", expression = "java(false)"),
            @Mapping(target = "holidayMonth", expression = "java(date.getMonth())"),
            @Mapping(target = "description", expression = "java(HolidayTypeEnum.WEEKEND.name())"),
            @Mapping(target = "holidayOperate", expression = "java(HolidayOperateEnum.DEFAULT)"),
    })
    InstFinancialCalendarHolidayDTO buildWeekendHoliday(LocalDate date);

    /**
     * 日历保存上下文转草稿
     */
    @Mappings({
            @Mapping(target = "draftId", source = "draftId"),
            @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.INST_CENTER)"),
            @Mapping(target = "moduleName", expression = "java(OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER)"),
            @Mapping(target = "operateType", expression = "java(OperateTypeEnum.ADD)"),
            @Mapping(target = "status", expression = "java(InstProcessStatusEnum.PROCESSING)"),
            @Mapping(target = "draftData", expression = "java(JSON.toJSONString(context,SerializerFeature.DisableCircularReferenceDetect))"),
            @Mapping(target = "businessKey", source = "context.calendar.calendarId"),
            @Mapping(target = "owner", source = "context.calendar.owner")
    })
    InstBusinessDraft calendarSaveContext2Draft(InstFinancialCalendarContext context, String draftId, String shareId);


    /**
     * 日历更新上下文转草稿
     */
    @Mappings({
            @Mapping(target = "draftId", source = "draftId"),
            @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.INST_CENTER)"),
            @Mapping(target = "moduleName", expression = "java(OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER)"),
            @Mapping(target = "operateType", expression = "java(OperateTypeEnum.UPDATE)"),
            @Mapping(target = "status", expression = "java(InstProcessStatusEnum.PROCESSING)"),
            @Mapping(target = "draftData", expression = "java(JSON.toJSONString(context,SerializerFeature.DisableCircularReferenceDetect))"),
            @Mapping(target = "businessKey", source = "context.calendar.calendarId"),
            @Mapping(target = "owner", source = "shareId")
    })
    InstBusinessDraft calendarUpdateContext2Draft(InstFinancialCalendarContext context, String draftId, String shareId);

    /**
     * 日历上下线 上下文转草稿
     */
    @Mappings({
            @Mapping(target = "draftId", source = "draftId"),
            @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.INST_CENTER)"),
            @Mapping(target = "moduleName", expression = "java(OperateModuleEnum.FINANCIAL_CALENDAR_MANAGER)"),
            @Mapping(target = "operateType", expression = "java(OperateTypeEnum.INVALID)"),
            @Mapping(target = "status", expression = "java(InstProcessStatusEnum.PROCESSING)"),
            @Mapping(target = "businessKey", source = "calendar.calendarId"),
            @Mapping(target = "owner", source = "shareId"),
            @Mapping(target = "utcCreate", ignore = true),
            @Mapping(target = "utcModified", ignore = true),
    })
    InstBusinessDraft calendarActivateDraftInit(InstFinancialCalendar calendar, String draftId, String shareId);

    /**
     * 节假日检查响应对象构建
     */
    HolidayCheckResponse holidayCheckResponseBuild(HolidayCheckRequest request, boolean isHoliday, LocalDate date, LocalDate nextWorkDay);


    NextWorkdayCalculationResponse nextWorkdayCalculationMapper(NextWorkdayCalculationRequest request);

    HolidayMultipleCheckResponse nextWorkdayCalculationMultipleMapper(HolidayMultipleCheckRequest request);


    HolidayQueryResponse holidayQueryMapper(HolidayQueryRequest request);

    NumOfWorkdaysCalculationResponse numOfWorkdaysCalculationMapper(NumOfWorkdaysCalculationRequest request);

    /**
     * 节假日检查请求对象转日历对象
     */
    @Mappings({
            @Mapping(target = "country", expression = "java(StringUtils.isNotBlank(request.getCountry()) ? request.getCountry() : CommonConstants.STAR)"),
            @Mapping(target = "currency", expression = "java(StringUtils.isNotBlank(request.getCurrency()) ? request.getCurrency() : CommonConstants.STAR)"),
    })
    InstFinancialCalendar holidayCheckReq2Calendar(HolidayCheckRequest request);


    InstFinancialCalendar deepcopy(InstFinancialCalendar calendar);

    @Named("string2List")
    default List<String> string2List(String str) {
        return ListUtils.string2List(str, CommonConstants.COMMA);
    }

    @Named("list2String")
    default String list2String(List<String> list) {
        return ListUtils.list2String(list, CommonConstants.COMMA);
    }
}
