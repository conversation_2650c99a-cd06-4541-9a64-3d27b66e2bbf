package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstProductFeeVO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/1 20:38
 */
@Data
public class InstProductFeeVO implements Serializable {
    private static final long serialVersionUID = -6698146224896317908L;
    private Long id;

    private String contractNo;

    private String instProductCode;

    private String baseRate;

    private String rateTimeType;

    private String rateTime;

    private String baseRateSource;

    private String isUseNdf;

    private String ndfCurrency;

    private String hasAccessFee;

    private String accessFeeDetail;

    private String isChargeAfterward;

    private String chargeAfterwardDetail;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    private String productName;

    private String channelType;

    private String paymentMethodType;

//    /**
//     * 使用该费用配置的所有接入国家
//     */
//    private List<String> countrys;
//
//    /**
//     * 使用该费用配置的所有目标机构
//     */
//    private List<String> targetOrgs;
//
//    /**
//     * 使用该费用配置的所有卡组织
//     */
//    private List<String> cardOrgs;
//
//    /**
//     * 该费用配置下所有的费用明细（不做任何处理）
//     */
//    private List<InstTransFeeVO> feeDetailList;
//
//    /**
//     * 该费用配置下所有的费用明细（按页面展示需要处理）
//     * List<Map>：表示费用种类维度的多个费用明细
//     * 一级Map说明:
//     *  key：表示费用种类
//     *  value：表示该费用种类对应的多个行业分类Map
//     *      二级Map说明：
//     *          key：行业分类
//     *          value：计费信息（计费方式+对应值）
//     */
//    private List<Map<String,List<Map<String,String>>>> feeDetailVOs;

}
