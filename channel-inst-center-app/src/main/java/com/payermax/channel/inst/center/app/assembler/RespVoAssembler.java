package com.payermax.channel.inst.center.app.assembler;

import com.payermax.channel.inst.center.app.response.*;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.entity.query.*;
import com.payermax.common.lang.util.money.Money;
import com.ushareit.fintech.base.dto.DictItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;


/**
 * 响应转换
 *
 * <AUTHOR>
 * @date 2022/5/15 21:11
 */
@Mapper(componentModel = "spring", imports = {Money.class})

public interface RespVoAssembler {

    /**
     * 文件附件：entity转vo
     *
     * @param entity
     * @return
     */
    AttachVO toAttachVo(AttachEntity entity);

    /**
     * 转换字典项
     *
     * @param itemDTO
     * @return
     */
    DictItemVO toDictItemVo(DictItemDTO itemDTO);

    /**
     * 转换成机构DD VO
     *
     * @param entity
     * @return
     */
    InstDdVO toInstDdVo(InstDdEntity entity);

    /**
     * 转换成机构DD调研问卷 VO
     *
     * @param entity
     * @return
     */
    InstDdSurveyVO toInstDdSurveyVo(InstDdSurveyEntity entity);

    /**
     * NDA：Entity转换成VO
     *
     * @param instNdaEntity
     * @return
     */
    InstNdaVO toInstNdaVo(InstNdaEntity instNdaEntity);

    /**
     * 机构联系人：Entity转换成VO
     *
     * @param instContactEntityList
     * @return
     */
    List<InstContactVO> toInstContactVo(List<InstContactEntity> instContactEntityList);

    /**
     * 机构银行卡信息：Entity list转换成VO
     *
     * @param instBankAccountEntityList
     * @return
     */
    List<InstBankAccountVO> toInstBankAccountVo(List<InstBankAccountEntity> instBankAccountEntityList);


    /**
     * 机构银行卡信息：Entity 单个转换成VO
     *
     * @param instBankAccountEntity
     * @return
     */
    InstBankAccountVO toSingleInstBankAccountVo(InstBankAccountEntity instBankAccountEntity);

    /**
     * 机构品牌信息：Entity转换成VO
     *
     * @param instBrandEntityList
     * @return
     */
    List<InstBrandVO> toInstBrandVo(List<InstBrandEntity> instBrandEntityList);

    /**
     * 机构信息：Entity转换成VO
     *
     * @param instBaseInfoEntity
     * @return
     */
    InstBaseInfoVO toInstBaseInfoVo(InstBaseInfoEntity instBaseInfoEntity);

    /**
     * 机构信息：Entity转换成VO
     *
     * @param instBrandEntityList
     * @return
     */
    List<InstBaseInfoVO> toInstBaseInfoVos(List<InstBaseInfoEntity> instBrandEntityList);

    /**
     * 机构信息：Entity转换成VO
     *
     * @param instBrandQueryEntityList
     * @return
     */
    List<InstBaseInfoVO> toInstBaseInfoQueryVos(List<InstBaseInfoQueryEntity> instBrandQueryEntityList);

    /**
     * KYC：Entity转换成VO
     *
     * @param instKycEntity
     * @return
     */
    InstKycVO toInstKycVo(InstKycEntity instKycEntity);

    /**
     * 审核资料：Entity转换成VO
     *
     * @param instNdaEntity
     * @return
     */
    InstAuditDataVO toInstAuditDataVo(InstAuditDataEntity instNdaEntity);

    /**
     * 审核资料：Entity转换成VO
     *
     * @param instNdaEntitys
     * @return
     */
    List<InstAuditDataVO> toInstAuditDataVo(List<InstAuditDataEntity> instNdaEntitys);

    /**
     * 审核结果：Entity转换成VO
     *
     * @param instAuditResultEntity
     * @return
     */
    InstAuditResultVO toInstAuditResultVo(InstAuditResultEntity instAuditResultEntity);

    /**
     * 转换成机构品牌 VO
     *
     * @param entity
     * @return
     */
    InstBrandVO toInstBrandVo(InstBrandEntity entity);

    /**
     * 转换成机构产品 VO
     *
     * @param entity
     * @return
     */
    InstProductVO toInstProductVo(InstProductEntity entity);

    /**
     * 转换成机构产品 VO
     *
     * @param entity
     * @return
     */
    List<InstProductVO> toInstProductVos(List<InstProductEntity> entity);

    /**
     * 转换成申请单 VO
     *
     * @param entity
     * @return
     */
    InstApplyOrderVO toInstApplyOrderVo(InstApplyOrderEntity entity);

    /**
     * 转换成申请单 VO
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(source = "instBaseInfoEntity", target = "instBaseInfoVO"),
            @Mapping(source = "instBaseInfoEntity.instName", target = "instName"),
            @Mapping(source = "instBrandEntity", target = "instBrandVO"),
            @Mapping(source = "instDdEntity", target = "instDdVO"),
    })
    ApplyOrderQueryVO toApplyOrderQueryVo(ApplyOrderQueryEntity entity);

    /**
     * 转换成申请单 VO
     *
     * @param list
     * @return
     */
    List<InstApplyOrderVO> toInstApplyOrdersVo(List<InstApplyOrderEntity> list);

    /**
     * 转换成申请单 VO
     *
     * @param list
     * @return
     */
    List<ApplyOrderQueryVO> toApplyOrdersQueryVo(List<ApplyOrderQueryEntity> list);

    /**
     * 转换成查询机构DD VO
     *
     * @param entity
     * @return
     */
    @Mappings({
            @Mapping(source = "instBaseInfoEntity", target = "instBaseInfoVO")
    })
    InstDdQueryVO toInstDdQueryVo(InstDdQueryEntity entity);

    /**
     * 集成需求单：转成VO
     *
     * @param entity
     * @return
     */
    InstRequirementOrderVO toInstRequirementOrderVO(InstRequirementOrderEntity entity);

    /**
     * 集成排期：转VO
     *
     * @param entity
     * @return
     */
    InstRequirementScheduleQueryVO toInstRequirementScheduleQueryVO(InstRequirementScheduleQueryEntity entity);

    /**
     * 集成排期：转VO
     *
     * @param entityList
     * @return
     */
    List<InstRequirementScheduleQueryVO> toInstRequirementScheduleQueryVOs(List<InstRequirementScheduleQueryEntity> entityList);

    /**
     * 机构账号：转成VO
     *
     * @param entityList
     * @return
     */
    List<InstAccountVO> toInstAccountVOList(List<InstAccountEntity> entityList);

    /**
     * 机构商户报备：转成VO
     *
     * @param entity
     * @return
     */
    InstReportMerchantVO toInstReportMerchantVO(InstReportMerchantEntity entity);

    /**
     * 机构对账：转成VO
     *
     * @param entity
     * @return
     */
    InstReconcileVO toInstReconcileVO(InstReconcileEntity entity);

    /**
     * 合同：entity转成VO
     *
     * @param entity
     * @return
     */
    InstContractVO toInstContractVo(InstContractEntity entity);

    /**
     * 合同：entity转成VO
     *
     * @param entityList
     * @return
     */
    List<InstContractVO> toInstContractVos(List<InstContractEntity> entityList);

    /**
     * 产品能力：entity转成VO
     *
     * @param entity
     * @return
     */
    InstProductCapabilityVO toInstProductCapabilityVo(InstProductCapabilityEntity entity);

    /**
     * 产品能力：entity转成VO
     *
     * @param list
     * @return
     */
    List<InstProductCapabilityVO> toInstProductCapabilityVos(List<InstProductCapabilityEntity> list);

    /**
     * 产品费用配置：entity转成VO
     *
     * @param entity
     * @return
     */
    InstProductFeeVO toInstProductFeeVo(InstProductFeeEntity entity);

    /**
     * 产品费用配置：entity转成VO
     *
     * @param entity
     * @return
     */
    InstTransFeeVO toInstTransFeeVo(InstTransFeeEntity entity);

    /**
     * 产品费用配置：entity转成VO
     *
     * @param list
     * @return
     */
    List<InstTransFeeVO> toInstTransFeeVos(List<InstTransFeeEntity> list);

    /**
     * 合同签约产品能力信息：Entity转换成VO
     *
     * @param instContractProductEntity
     * @return
     */
    InstContractProductVO toInstContractProductVo(InstContractProductEntity instContractProductEntity);

    /**
     * 合同签约产品能力信息：Entity转换成VO
     *
     * @param instContractProductEntityList
     * @return
     */
    List<InstContractProductVO> toInstContractProductVos(List<InstContractProductEntity> instContractProductEntityList);

    /**
     * 目标机构：entity转成VO
     *
     * @param entity
     * @return
     */
    InstTargetOrgVO toInstTargetOrgVo(InstTargetOrgEntity entity);

    /**
     * 产品费用配置：entity转成VO
     *
     * @param list
     * @return
     */
    List<InstTargetOrgVO> toInstTargetOrgVos(List<InstTargetOrgEntity> list);

    /**
     * 业务字典：entity转成VO
     *
     * @param entity
     * @return
     */
    InstBusinessDictVO toInstBusinessDictVo(InstBusinessDictEntity entity);

    /**
     * 业务字典：entity转成VO
     *
     * @param list
     * @return
     */
    List<InstBusinessDictVO> toInstBusinessDictVos(List<InstBusinessDictEntity> list);

    /**
     * 集成排期：entity转成VO
     *
     * @param entity
     * @return
     */
    InstRequirementScheduleVO toInstRequirementScheduleVo(InstRequirementScheduleEntity entity);

    /**
     * 机构账户密钥：转成VO
     *
     * @param entity
     * @return
     */
    InstAccountKeyQueryVO toInstAccountKeyQueryVO(InstAccountKeyQueryEntity entity);

    /**
     * 机构账户密钥：转成VO
     *
     * @param entityList
     * @return
     */
    List<InstAccountKeyQueryVO> toInstAccountKeyQueryVOList(List<InstAccountKeyQueryEntity> entityList);

    SettleInfoVo toSettleInfoVO(InstSettleInfoVO instFxSettleInfoVO);

//    @Mappings({
//            @Mapping(target = "minSettleAmount", expression = "java(settleInfoVONew.getMinSettleAmount() != null ? settleInfoVONew.getMinSettleAmount().getAmount() : null)"),
//            @Mapping(target = "settleMethod", expression = "java(settleInfoVONew.getSettleMethod() != null ? settleInfoVONew.getSettleMethod().getDesc() : null)"),
//            @Mapping(target = "settleAccountType", expression = "java(settleInfoVONew.getSettleAccountType() != null ? settleInfoVONew.getSettleAccountType().getDesc() : null)"),
//
//    })
//    SettleInfoVONew toSettleInfoVONew(InstFxContractSettleInfoVO settleInfoVONew);
//
//
//    SettleInfoVONew.SettleCycleInfoVO toSettleCycleInfoVO(InstFxContractSettleInfoVO.SettleCycleInfo settleCycleInfo);
//
//
//    SettleInfoVONew.TransactionDateCycleInfoVO toSettleTransactionDateCycleInfoVO(InstFxContractSettleInfoVO.TransactionDateCycleInfo settleCycleInfo);
}
