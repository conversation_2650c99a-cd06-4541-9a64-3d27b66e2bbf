package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountBucketEntity;

import java.util.List;


/**
 * 机构资金账号子级资金账户开通拓展请求参数
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstFundsAccountBucketService {

    /**
     * 子级资金账户开通拓展计数参数
     *
     * @param bucketEntity
     * @return
     */
    List<InstFundsAccountBucketEntity> queryBucketsIdIsNullByAccountId(InstFundsAccountBucketEntity bucketEntity);

    /**
     * 子级资金账户开通拓展请求参数
     *
     * @param bucketEntity
     * @return
     */
    List<InstFundsAccountBucketEntity> queryBucketsIdNotNullByBucketsId(InstFundsAccountBucketEntity bucketEntity);


}
