package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstContractProductVO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/14 15:27
 */
@Data
public class InstContractProductVO implements Serializable {

    private static final long serialVersionUID = 6787711192102867654L;

    private String contractNo;

    private String instProductCode;

    private String instProductCapabilityCode;

    private String version;

    private String feeGroupId;

    private Date utcCreate;

    private Date utcModified;
}
