package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstProductTransFeeVO
 * @Description 产品维度->交易费用编辑页使用
 * <AUTHOR>
 * @Date 2022/6/6 1:11
 */
@Data
public class InstProductTransFeeVO implements Serializable {

    private static final long serialVersionUID = 3122004956426426333L;

    /**
     * 产品基本信息
     */
    private String contractNo;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 支付方式类型
     */
    private String paymentMethodType;

    /**
     * 产品下所有能力中未配交易费用的国家列表
     */
    private List<String> countrys;

    /**
     *产品下所有能力中未配交易费用的目标机构列表
     */
    private List<String> targetOrgs;

    /**
     *产品下所有能力中未配交易费用的卡组织列表
     */
    private List<String> cardOrgs;

    /**
     * 产品下未配置交易费用的能力列表
     */


    /**
     * 产品下已配置的交易费用列表
     */
    private List<ProductCapabilityTransFeeVO> productCapabilityTransFeeVOList;

    /**
     * 一组费用配置对应一条
     */
    @Data
    public static class ProductCapabilityTransFeeVO{
        /**
         * 国家
         */
        private String countrys;
        /**
         * 目标机构
         */
        private String targetOrgs;
        /**
         * 卡组织
         */
        private String cardOrgs;
        /**
         * 费用分组id
         */
        private String feeGroupId;
        /**
         * 创建时间
         */
        @JsonFormat(shape = JsonFormat.Shape.NUMBER)
        private Date utcCreate;

        /**
         * 更新时间
         */
        @JsonFormat(shape = JsonFormat.Shape.NUMBER)
        private Date utcModified;
        /**
         * feeGroupId对应的交易费用列表
         */
        private List<InstTransFeeVO> transFeeVOList;
    }

    /**
     * 产品下所有能力中已配置交易费用的交易费用列表
     */
    private List<InstTransFeeVO> transFeeVOList;
}
