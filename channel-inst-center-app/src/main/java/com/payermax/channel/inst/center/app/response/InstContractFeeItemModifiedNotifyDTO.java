package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.common.enums.instcontract.ClearNetworkEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.CustomerTypeEnum;
import com.payermax.channel.inst.center.common.enums.instcontract.FeeBearerEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/26
 * @DESC
 */
@Data
public class InstContractFeeItemModifiedNotifyDTO extends BaseMqMessage {


    /**
     * 机构编号
     */
    private String instCode;

    /**
     * 我司主体
     */
    private String contractEntity;

    /**
     * 机构产品编码
     */
    private String instProductType;


    /**
     * 合同费用条款项id
     */
    private String instContractFeeItemNo;

    /**
     * 机构原始产品编码
     */
    private String instOriginProductNo;

    /**
     * 支付币种
     */
    private String payCurrency;


    /**
     * 内部渠道商户号
     */
    private String channelMerchantNo;


    /**
     * 机构合同中行业标识，及标准化后的内部统一mcc
     */
    private String mccLogic;
    private String standardMcc;
    /**
     * 清算网络
     * {@link ClearNetworkEnum}
     */
    private String clearNetwork;

    /**
     * 费用承担方
     * {@link FeeBearerEnum}
     */
    private String feeBearer;

    /**
     * 客户类型
     * {@link CustomerTypeEnum}
     */
    private String customerType;

    /**
     * 资金源
     */
    private String fundingSource;
    private String subMerchantNo;

    /**
     * 合约外汇加点
     */
    private BigDecimal contractFxSpread;

    /**
     * 外汇加点
     */
    private BigDecimal fxSpread;

    private String currencyExchangeTime;


    private Integer roundingScale;

    private String roundingMode;


    //-- 出款项目新增 --//
    /**
     * 发卡国家新增
     */
    private String cardIssueCountry;

    /**
     * 交易国家新增
     */
    private String transactionCountry;


    //----- 大阶梯相关
    private String accumulationCycle;

    private String accumulationType;

    private String accumulationMethod;

    private String accumulationRange;

    private String accumulationDeductTime;

    private String accumulationJoin;

    private String accumulationKey;

    private String accumulationMode;
    //----- 大阶梯相关

    private List<InstContractBaseResponse.StandardProductMsg> paymentMethodTypeGroup;

    private FeeConfig oldFeeConfig;
    private FeeConfig newFeeConfig;

    private List<TaxConfig> oldTaxConfig;
    private List<TaxConfig> newTaxConfig;
}
