package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketQueryEntity;

import java.util.List;

/**
 * 机构子级资金账号预申请账号Service
 *
 * <AUTHOR>
 * @date 2022/10/20 13:59
 */
public interface InstSubFundsAccountBucketService {

    /**
     * 查询机构预申请的子级资金账号(无商户号或子商户限制条件)
     *
     * @param
     * @return
     */
    List<InstSubFundsAccountBucketEntity> queryByQueryEntityWithoutMerchantNo(InstSubFundsAccountBucketQueryEntity queryEntity);

    /**
     * 查询机构预申请的子级资金账号(绑定商户号)
     *
     * @param
     * @return
     */
    List<InstSubFundsAccountBucketEntity> queryBindMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity);

    /**
     * 查询机构预申请的子级资金账号(绑定子商户号)
     *
     * @param
     * @return
     */
    List<InstSubFundsAccountBucketEntity> queryBindSubMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity);

    /**
     * 查询机构预申请的子级资金账号(不绑定商户号和子商户号)
     *
     * @param
     * @return
     */
    List<InstSubFundsAccountBucketEntity> queryUnboundMerchantAccountByQueryEntity(InstSubFundsAccountBucketQueryEntity queryEntity);

    /**
     * 通过子级资金账号ID更新预申请的子级资金账号
     *
     * @param record
     * @return
     */
    int updateBySubAccountId(InstSubFundsAccountBucketEntity record);

    /**
     * 创建机构子级账号预申请记录
     *
     * @param record
     * @return
     */
    int insert(InstSubFundsAccountBucketEntity record);

    /**
     * 通过账号ID查询预申请账号
     *
     * @param subAccountId
     * @return
     */
    InstSubFundsAccountBucketEntity queryBySubAccountId(String subAccountId);

    /**
     * 查询机构账号下，预申请中可分配的
     * 但不存在于子级账号表且状态为申请中的
     * 预申请账号总数
     *
     * @param accountId
     * @return
     */
    int queryAssignableCountByAccountId(String accountId);
}
