package com.payermax.channel.inst.center.app.assembler.domain;

import com.payermax.channel.inst.center.facade.request.contract.InstInfoQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstRiskFeeQueryRequest;
import com.payermax.channel.inst.center.facade.request.contract.InstTechnicalServiceFeeQueryRequest;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2024/11/5
 * @DESC
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {})
public interface InstContractFeeQueryAssembler {


    InstContractFeeQueryAssembler INSTANCE = Mappers.getMapper(InstContractFeeQueryAssembler.class);

    InstInfoQueryRequest riskFeeQuery2Request(InstRiskFeeQueryRequest request);

    InstInfoQueryRequest techServiceFeeQuery2Request(InstTechnicalServiceFeeQueryRequest request);
}
