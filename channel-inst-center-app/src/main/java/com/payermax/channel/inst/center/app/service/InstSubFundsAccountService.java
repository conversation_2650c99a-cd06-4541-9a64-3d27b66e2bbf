package com.payermax.channel.inst.center.app.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubAccountBathQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountBucketCountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;

import java.util.List;


/**
 * 机构资金账号Service
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstSubFundsAccountService {

    /**
     * 查询子级资金账户
     *
     * @param instSubFundsAccountQueryEntity
     * @return
     */
    InstSubFundsAccountEntity queryById(InstSubFundsAccountQueryEntity instSubFundsAccountQueryEntity);

    /**
     * 通过机构账号查询子级资金账户
     *
     * @param subFundsAccountQueryEntity
     * @return
     */
    List<InstSubFundsAccountEntity> queryListByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity);

    /**
     * 查询子级资金账户
     *
     * @param subFundsAccountQueryEntity
     * @return
     */
    List<InstSubFundsAccountEntity> queryListByQueryEntity(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity);

    /**
     * 通过业务键查询子级资金账户
     *
     * @param 
     * @return
     */
    InstSubFundsAccountEntity queryByBusinessKey(InstSubFundsAccountQueryEntity instSubFundsAccountQueryEntity);

    /**
     * 新增子级资金账户
     *
     * @param instSubFundsAccountEntity
     * @return
     */
    int insert(InstSubFundsAccountEntity instSubFundsAccountEntity);

    /**
     * 更新子级资金账户
     *
     * @param origStatus
     * @param instSubFundsAccountEntity
     * @return
     */
    int update(Integer origStatus, InstSubFundsAccountEntity instSubFundsAccountEntity);

    /**
     * 查询机构账号下生成的子级账号数
     *
     * @param subFundsAccountQueryEntity
     * @return
     */
    int queryCountByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity);

    /**
     * 查询机构账号下使用的bucketId机器子级账号数
     *
     * @param subFundsAccountQueryEntity
     * @return
     */
    List<InstSubFundsAccountBucketCountEntity> queryBucketIdCountByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity);

    /**
     * 查询机构账号下参数bucketId为空的子级账号数
     *
     * @param subFundsAccountQueryEntity
     * @return
     */
    int queryCountBucketIdIsNullByAccountId(InstSubFundsAccountQueryEntity subFundsAccountQueryEntity);

    /**
     * 定时任务查询机构子级账号
     *
     * @param subAccountBathQueryEntity
     * @return
     */
    IPage<InstSubFundsAccountEntity> querySubAccountForTask(InstSubAccountBathQueryEntity subAccountBathQueryEntity);
}
