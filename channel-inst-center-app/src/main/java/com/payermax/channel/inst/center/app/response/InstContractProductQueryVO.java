package com.payermax.channel.inst.center.app.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/1
 * @DESC
 */
@Data
public class InstContractProductQueryVO {

    @ApiModelProperty(notes = "原始产品编号")
    private String instOriginProductNo;

    @ApiModelProperty(notes = "合同编号")
    private String contractNo;

    @ApiModelProperty(notes = "我司主体")
    private String contractEntity;

    @ApiModelProperty(notes = "机构标识")
    private String instCode;

    @ApiModelProperty(notes = "机构产品类型")
    private String instProductType;

    @ApiModelProperty(notes = "机构产品名称")
    private String instProductName;

    @ApiModelProperty(notes = "支付币种列表")
    private List<String> payCurrencyList;

    @ApiModelProperty(notes = "支付方式、目标机构、卡组")
    private List<InstFeeQueryVO.StandardProductMsg> standardProductMsgList;

    @ApiModelProperty(notes = "费用列表")
    private List<InstContractFeeItemFullyVO> feeList;

}
