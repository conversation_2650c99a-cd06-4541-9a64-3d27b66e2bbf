package com.payermax.channel.inst.center.app.assembler.domain;

import com.payermax.channel.inst.center.common.enums.LogicKeyEnum;
import com.payermax.channel.inst.center.common.utils.ConvertUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.*;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.enums.contract.content.*;
import com.payermax.channel.inst.center.facade.request.contract.*;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.FxConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.SettlementConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleAccount;
import com.payermax.channel.inst.center.facade.request.contract.config.sub.SettleDate;
import com.payermax.channel.inst.center.facade.response.contract.*;
import com.payermax.channel.inst.center.facade.vo.SettleInfoVo;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/13 5:32 PM
 **/
@Mapper(componentModel = "spring", imports = {FeeTypeEnum.class, FeeCalculateTypeEnum.class, FeeCalculateBaseModeEnum.class,
        TaxCalculateFormulaEnum.class, YesOrNoEnum.class, CurrencyExchangeTimingEnum.class, RoundingModeEnum.class,
        FeeCalculateTimingEnum.class, LogicKeyEnum.class,
        BigDecimal.class, ConvertUtils.class, StringUtils.class, InstFeeConfig.FeeStepCombineConfig.class})
public interface InstContractRequestAssembler {

    InstInfoQueryResponse convertRequest2Response(InstInfoQueryRequest request);


    InstContractAllFxAndSettleResponse convertRequest2AllFxAndSettleResponse(InstInfoQueryRequest request);

    FeeConfig convertInstFee2Response(InstFeeConfig feeConfig);

    TaxConfig convertInstTax2Response(InstTaxConfig taxConfig);

    @Mappings({
            @Mapping(target = "settleDates", expression = "java(convertInstSettleDateList2Response(settlementItem.getSettleDateConfigs()))"),
            @Mapping(target = "settleAccount", expression = "java(convertInstSettleAccount2Response(settlementItem.getSettlePaymentConfig()))"),
    })
    SettlementConfig convertInstSettle2Response(InstContractSettlementItem settlementItem);

    SettleAccount convertInstSettleAccount2Response(InstSettlePaymentConfig instSettlePaymentConfig);

    SettleDate convertInstSettleDate2Response(InstSettleDateConfig settleDateConfig);

    List<SettleDate> convertInstSettleDateList2Response(List<InstSettleDateConfig> settleDateConfigs);

    FxConfig convertInstFx2Response(InstContractFeeItem feeItem);


    SettleInfoVo convertSettleConfig2VO(SettleInfoConfig settleDateConfig);

    List<TaxConfig> convertInstTax2ResponseList(List<InstTaxConfig> taxConfig);

    InstRiskFeeInfoResponse convertReq2Resp(InstRiskFeeQueryRequest response);

    InstTechnicalServiceFeeResponse convertReq2Resp(InstTechnicalServiceFeeQueryRequest response);

    InstAccountingTypeResponse convertReq2Resp(InstAccountingTypeQueryRequest response);

    InstVirtualAccountFeeResponse convertReq2VAResp(InstInfoQueryRequest response);

    InstDisputeFeeResponse convertReq2DisputeResp(InstInfoQueryRequest response);
}
