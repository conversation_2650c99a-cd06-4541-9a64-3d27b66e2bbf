package com.payermax.channel.inst.center.app.assembler.domain;

import com.google.common.collect.ImmutableMap;
import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.common.utils.ConvertUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstTaxConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.domain.enums.contract.content.*;
import com.payermax.channel.inst.center.domain.enums.contract.management.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> at 2023/6/13 5:32 PM
 **/
@Mapper(componentModel = "spring", imports = {FeeTypeEnum.class, FeeCalculateTypeEnum.class, FeeCalculateBaseModeEnum.class,
        TaxCalculateFormulaEnum.class, YesOrNoEnum.class, CurrencyExchangeTimingEnum.class, RoundingModeEnum.class,
        FeeCalculateTimingEnum.class, AccumulationCycle.class, AccumulationDeductTime.class, AccumulationMethod.class, AccumulationRange.class, AccumulationType.class,
        BigDecimal.class, ConvertUtils.class, StringUtils.class, InstFeeConfig.FeeStepCombineConfig.class})
public interface InstContractFeeItemAssembler {


    /**
     * 构造 单笔费用 交易费用配置
     */
    @Mappings({
        @Mapping(target = "feeType", expression = "java(FeeTypeEnum.TRADE)"),
        @Mapping(target = "calculateType", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateTypeEnum.class, dto.getCalculateType()))"),
        @Mapping(target = "feeBasementMode", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateBaseModeEnum.class, dto.getFeeBasementMode()))"),
        @Mapping(target = "feeCalculateTiming", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateTimingEnum.class, dto.getFeeCalculateTime()))"),
        @Mapping(target = "feeDeductCurrency", expression = "java(dto.getChargeCurrency())"),
        @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(dto.getSingleStrokeRate()))"),
        @Mapping(target = "percentMinAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getMinCharge()))"),
        @Mapping(target = "percentMaxAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getMaxCharge()))"),
        @Mapping(target = "feeValue", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getSingleFixedAmount()))"),
        @Mapping(target = "feeCurrency", expression = "java(dto.getSingleFixedCurrency())")
    })
    InstFeeConfig convertSingleInstTradeFeeFromExcel(InstContractFeeRowItemDTO dto);


    /**
     * 构造 阶梯费用 交易费用配置
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(FeeTypeEnum.TRADE)"),
            @Mapping(target = "calculateType", expression = "java(FeeCalculateTypeEnum.STEP_COMBINE)"),
            @Mapping(target = "feeBasementMode", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateBaseModeEnum.class, dto.getFeeBasementMode()))"),
            @Mapping(target = "feeCalculateTiming", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateTimingEnum.class, dto.getFeeCalculateTime()))"),
            @Mapping(target = "feeDeductCurrency", expression = "java(dto.getChargeCurrency())")
    })
    InstFeeConfig convertStepInstTradeFeeFromExcel(InstContractFeeRowItemDTO dto);

    List<InstFeeConfig.FeeStepCombineConfig> convertStepCombineFeeListFromExcel(List<InstContractFeeRowItemDTO> dtoList);

    @Mappings({
            @Mapping(target = "leftAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getStepLower()))"),
            @Mapping(target = "rightAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getStepUpper()))"),
            @Mapping(target = "calculateType", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateTypeEnum.class, dto.getCalculateType()))"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(dto.getSingleStrokeRate()))"),
            @Mapping(target = "percentMinAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getMinCharge()))"),
            @Mapping(target = "percentMaxAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getMaxCharge()))"),
            @Mapping(target = "feeValue", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getSingleFixedAmount()))"),
            @Mapping(target = "feeCurrency", expression = "java(dto.getSingleFixedCurrency())")
    })
    InstFeeConfig.FeeStepCombineConfig convertStepCombineFeeFromExcel(InstContractFeeRowItemDTO dto);


    /**
     * 构造 退款 费用配置
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(FeeTypeEnum.REFUND)"),
            @Mapping(target = "calculateType", expression = "java(FeeCalculateTypeEnum.SINGLE_MONEY)"),
            @Mapping(target = "feeBasementMode", ignore = true),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(dto.getRefundSingleStrokeRate()))"),
            @Mapping(target = "feeValue", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getRefundSingleFixedAmount()))"),
            @Mapping(target = "feeCurrency", expression = "java(dto.getRefundSingleFixedCurrency())"),
            @Mapping(target = "extendFields", source = "refundOrNot", qualifiedByName = "composeRefundFeeExtendFields"),
            @Mapping(target = "feeDeductCurrency", expression = "java(dto.getChargeCurrency())")
    })
    InstFeeConfig convertInstRefundFeeFromExcel(InstContractFeeRowItemDTO dto);

    default Map<String, String> composeRefundFeeExtendFields(String refundOrNot) {
        return ImmutableMap.of(InstFeeConfig.ExtendFieldsKey.SHOULD_TRADE_FEE_REFUNDED, refundOrNot);
    }

    /**
     * 构造 税费 配置
     */
    @Mappings({
        @Mapping(target = "taxType", expression = "java(StringUtils.upperCase(dto.getTaxType()))"),
        @Mapping(target = "taxCalculateType", expression = "java(ConvertUtils.getEnumByDesc(TaxCalculateFormulaEnum.class, dto.getTaxCalculateType()))"),
        @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(dto.getTaxRate()))"),
        @Mapping(target = "deductible", expression = "java(ConvertUtils.getEnumByDesc(YesOrNoEnum.class, dto.getDeductible()))")
    })
    InstTaxConfig convertInstTaxFromExcel(InstContractFeeRowItemDTO.TaxInfo dto);

    List<InstTaxConfig> convertInstTaxFromExcel(List<InstContractFeeRowItemDTO.TaxInfo> dtoList);


    /**
     * 构造机构合约费用配置
     */
    @Mappings({
        @Mapping(target = "mccLogic", expression = "java(ConvertUtils.processOnLogicKeyEnum(dto.getStandardMcc()))"),
        @Mapping(target = "standardMcc", expression = "java(ConvertUtils.processExceptLogicKeyEnum(dto.getStandardMcc()))"),
        @Mapping(target = "currencyExchangeTime", expression = "java(ConvertUtils.getEnumByDesc(CurrencyExchangeTimingEnum.class, dto.getCurrencyExchangeTime()))"),
        @Mapping(target = "fxSpread", expression = "java(ConvertUtils.convertPercentBigDecimal(dto.getFxSpread()))"),
        @Mapping(target = "roundingMode", expression = "java(ConvertUtils.getEnumByDesc(RoundingModeEnum.class, dto.getRoundingMode()))"),
        @Mapping(target = "accumulationCycle", expression = "java(ConvertUtils.getEnumByDesc(AccumulationCycle.class, dto.getAccumulationCycle()))"),
        @Mapping(target = "accumulationType", expression = "java(ConvertUtils.getEnumByDesc(AccumulationType.class, dto.getAccumulationType()))"),
        @Mapping(target = "accumulationMethod", expression = "java(ConvertUtils.getEnumByDesc(AccumulationMethod.class, dto.getAccumulationMethod()))"),
        @Mapping(target = "accumulationRange", expression = "java(ConvertUtils.getEnumByDesc(AccumulationRange.class, dto.getAccumulationRange()))"),
        @Mapping(target = "accumulationDeductTime", expression = "java(ConvertUtils.getEnumByDesc(AccumulationDeductTime.class, dto.getAccumulationDeductTime()))"),
        @Mapping(target = "accumulationJoin", expression = "java(dto.getAccumulationJoin())"),
        @Mapping(target = "accumulationKey", expression = "java(dto.getAccumulationKey())"),
        @Mapping(target = "accumulationMode", expression = "java(dto.getAccumulationMode())"),
        @Mapping(target = "transactionCountry", expression = "java(dto.getTransactionCountry())"),
        @Mapping(target = "cardIssueCountry", expression = "java(dto.getCardIssueCountry())"),
    })
    InstContractFeeItem convertContractFeeItemFromExcel(InstContractFeeRowItemDTO dto);
}
