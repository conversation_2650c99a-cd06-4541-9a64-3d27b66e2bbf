package com.payermax.channel.inst.center.app.rocketmq.producer;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.dto.calendar.HolidayChangeNotify;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import com.payermax.infra.ionia.rocketmq.handler.IoniaRocketMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HolidayUpdateProducer {


    @NacosValue("${spring.application.name}")
    private String appName;

    @Autowired
    private IoniaRocketMqTemplate ioniaRocketMqTemplate;

    /**
     * 节假日更新topic
     */
    public static final String HOLIDAY_UPDATE_TOPIC = "topic_funds_calendar_update";

    /**
     * 发送费用信息变更通知
     */
    public void sendHolidayUpdate(HolidayChangeNotify notify) {
        SendResult sendResult = sendMessage(HOLIDAY_UPDATE_TOPIC, notify, UUID.randomUUID().toString());
        log.info("HolidayUpdateProducer send Info:{} ,result:{}", notify, sendResult);
    }


    /**
     * 同步发送消息
     */
    public <T extends BaseMqMessage> SendResult sendMessage(String topic, T data, String key) {
        data.setKey(key);
        data.setSendTime(new Date());
        data.setSource(appName);
        String traceId = TraceContext.traceId();
        if (StringUtils.isNotBlank(traceId)) {
            data.setTraceId(TraceContext.traceId());
        }
        return ioniaRocketMqTemplate.sendMsgSync(topic, data);
    }
}
