package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleDetailEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleDetailQueryEntity;

import java.util.List;

/**
 * 集成需求单排期详情Service
 *
 * <AUTHOR>
 * @date 2022/6/15 17:34
 */
public interface InstRequirementScheduleDetailService {

    /**
     * 新增
     *
     * @param record
     * @return
     */
    int add(InstRequirementScheduleDetailEntity record);

    /**
     * 更新
     *
     * @param record
     * @return
     */
    int update(InstRequirementScheduleDetailEntity record);

    /**
     * 查询
     *
     * @param queryEntity
     * @return
     */
    List<InstRequirementScheduleDetailEntity> queryList(InstRequirementScheduleDetailQueryEntity queryEntity);

}
