package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/12
 * @DESC
 */
@Data
public class InstContractSettlementVO implements Serializable {


    /**
     * 原始产品编号
     */
    private String instOriginProductNo;

    private String instContractSettlementItemNo;


    /**
     * 结算配置
     */
    private SettleConfig settleConfig;

    /**
     * 结算费用
     * FeeConfig 权限及内容与 FeeItem 一致，直接引用
     */
    private InstContractFeeItemVO.FeeConfig settleFeeConfig;

    /**
     * 结算周期
     */
    private List<InstSettleDateConfig> settleDateConfigs;

    /**
     * 提现打款
     */
    private InstSettlePaymentConfig settlePaymentConfig;


    @Data
    public static class SettleConfig{

        /**
         * 结算币种
         */
        private String settleCurrency;

        /**
         * 渠道商户号
         */
        private String channelMerchantNo;

    }

    @Data
    public static class InstSettleDateConfig {

        /**
         * 时区
         */
        private String timezone;

        private String transactionStartDate;

        private String transactionEndDate;

        /**
         * 账单日
         */
        private String billDate;

        /**
         * 打款日
         */
        private String paymentDate;

        /**
         * 到账日
         */
        private String arriveDate;

        /**
         * 换汇日
         */
        private String exchangeDate;
    }

    @Data
    public static class InstSettlePaymentConfig {

        private String         minSettleAmount;

        private String withdrawMethod;

        private String             accountId;

        /**
         * 机构账号类型：银行账户：BANK_ACCOUNT、渠道支付账户：CHANNEL_PAY_ACCOUNT
         */
        private String             accountType;
    }

}
