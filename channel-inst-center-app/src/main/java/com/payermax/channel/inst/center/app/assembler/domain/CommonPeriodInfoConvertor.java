package com.payermax.channel.inst.center.app.assembler.domain;

import com.payermax.channel.inst.center.facade.common.CommonPeriodInfo;
import com.payermax.channel.inst.center.facade.response.fundsAgreement.FundsAgreementQueryResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/7/5
 */
@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS, imports = {})
public interface CommonPeriodInfoConvertor {

    InstBusinessDraftAssembler INSTANCE = Mappers.getMapper(InstBusinessDraftAssembler.class);


    @Mappings({
            @Mapping(target = "timeRangeStart", source = "settleRule.clearingRangeStart"),
            @Mapping(target = "timeRangeEnd", source = "settleRule.clearingRangeEnd"),
    })
    CommonPeriodInfo buildCommonPeriodInfo(FundsAgreementQueryResponse.FundsSettleRule settleRule, String dateDsl);
}
