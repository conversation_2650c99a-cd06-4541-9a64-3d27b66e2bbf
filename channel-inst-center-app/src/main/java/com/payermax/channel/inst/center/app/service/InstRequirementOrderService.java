package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementOrderEntity;

/**
 * 集成需求单Service
 *
 * <AUTHOR>
 * @date 2022/6/4 11:30
 */
public interface InstRequirementOrderService {

    /**
     * 保存
     *
     * @param record
     * @return
     */
    int save(InstRequirementOrderEntity record);

    /**
     * 根据申请单号查询
     *
     * @param applyNo
     * @return
     */
    InstRequirementOrderEntity get(String applyNo);

}
