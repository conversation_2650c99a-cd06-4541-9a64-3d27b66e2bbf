package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName InstNdaVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/13 21:17
 * @Version 1.0
 */
@Data
public class InstKycVO implements Serializable {


    private static final long serialVersionUID = -1810682087247580473L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 申请单号
     */
    private String applyNo;

    /**
     * 水印文字
     */
    private String watermarkWord;

    /**
     * 调研问卷文件
     */
    private String surveyAttachId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 INIT:初始态 DATA_COMPLETED:已提供材料 AUDITING:机构审核中 AUDIT_RETURN:机构审核驳回-待追加材料 AUDIT_AGREE:机构审批通过 COMPLETED:完成
     */
    private String status;

    /**
     * 审核资料
     */
    private List<InstAuditDataVO> auditDataList;
}
