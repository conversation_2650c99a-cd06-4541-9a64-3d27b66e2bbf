package com.payermax.channel.inst.center.app.assembler.po;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettleDateConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstTaxConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.domain.enums.contract.content.CurrencyExchangeTimingEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.channel.inst.center.facade.request.contract.AccumulationResponse;
import com.payermax.channel.inst.center.facade.request.contract.config.mapping.InstChannelMerchantCodeRequest;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> at 2023/6/19 11:31 AM
 **/

@Mapper(componentModel = "spring", imports = {JSON.class, JSONObject.class, TypeReference.class, FeeTypeEnum.class, HashMap.class, InstFeeConfig.class,
        InstTaxConfig.class, InstSettleDateConfig.class, InstSettlePaymentConfig.class, CurrencyExchangeTimingEnum.class, StringUtils.class,
        ContractStatusEnum.class
})
public interface ContractVersionPOAssembler {

    ContractVersionPOAssembler INSTANCE = Mappers.getMapper(ContractVersionPOAssembler.class);


    InstContractBaseInfoPO convertBaseInfoPO(InstContractBaseInfo base);

    InstContractBaseInfo convertBaseInfoDomain(InstContractBaseInfoPO po);

    List<InstContractBaseInfo> convertBaseInfoDomainList(List<InstContractBaseInfoPO> poList);

    List<InstContractVersionInfo> convertVersionDomainList(List<InstContractVersionInfoPO> poList);

    InstContractVersionInfoPO convertVersionInfoPO(InstContractVersionInfo version);

    InstContractVersionInfo convertVersionInfoDomain(InstContractVersionInfoPO version);


    List<InstContractOriginProductPO> convertOriginProductPOList(List<InstContractOriginProduct> productList);

    InstContractOriginProductPO convertOriginProductPO(InstContractOriginProduct product);

    InstContractOriginProduct convertOriginProductPO2Domain(InstContractOriginProductPO product);


    List<InstContractStandardProductPO> convertStandardProductPOList(List<InstContractStandardProduct> productList);

    @Mappings({
            @Mapping(target = "instOriginProductNo", expression = "java(product.getOriginProduct() == null ? null : product.getOriginProduct().getInstOriginProductNo())")
    })
    InstContractStandardProductPO convertStandardProductPO(InstContractStandardProduct product);

    InstContractStandardProduct convertStandardProductDomain(InstContractStandardProductPO product);


    List<InstContractFeeItemPO> convertFeeItemPOList(List<InstContractFeeItem> feeItemList);

    List<InstContractFeeItem> convertFeeItemDomainList(List<InstContractFeeItemPO> feeItemList);

    @Mappings({
            @Mapping(target = "feeConfig", expression = "java(JSON.toJSONString(feeItem.getFeeConfigs()))"),
            @Mapping(target = "taxConfig", expression = "java(JSON.toJSONString(feeItem.getTaxConfigs()))")
    })
    InstContractFeeItemPO convertFeeItemPO(InstContractFeeItem feeItem);

    @Mappings({
            @Mapping(target = "taxConfigs", expression = "java(JSONObject.parseArray(feeItem.getTaxConfig(), InstTaxConfig.class))"),
            @Mapping(target = "feeConfigs", expression = "java(JSONObject.parseObject(feeItem.getFeeConfig(), new TypeReference<HashMap<FeeTypeEnum,InstFeeConfig>>() {}))"),
            @Mapping(target = "currencyExchangeTime", expression = "java(StringUtils.isNotBlank(feeItem.getCurrencyExchangeTime()) ? CurrencyExchangeTimingEnum.valueOf(feeItem.getCurrencyExchangeTime()) : null)")
    })
    InstContractFeeItem convertFeeItemDomain(InstContractFeeItemPO feeItem);


    List<InstContractSettlementItemPO> convertSettlementItemPOList(List<InstContractSettlementItem> settlementItemList);

    List<InstContractSettlementItem> convertSettlementItemDomainList(List<InstContractSettlementItemPO> settlementItemList);

    @Mappings({
            @Mapping(target = "settleFeeConfig", expression = "java(JSON.toJSONString(settlementItem.getSettleFeeConfig()))"),
            @Mapping(target = "settleDateConfig", expression = "java(JSON.toJSONString(settlementItem.getSettleDateConfigs()))"),
            @Mapping(target = "settlePaymentConfig", expression = "java(JSON.toJSONString(settlementItem.getSettlePaymentConfig()))")
    })
    InstContractSettlementItemPO convertSettlementItemPO(InstContractSettlementItem settlementItem);

    @Mappings({
            @Mapping(target = "settleFeeConfig", expression = "java(JSONObject.parseObject(settlementItem.getSettleFeeConfig(), InstFeeConfig.class))"),
            @Mapping(target = "settleDateConfigs", expression = "java(JSONObject.parseArray(settlementItem.getSettleDateConfig(), InstSettleDateConfig.class))"),
            @Mapping(target = "settlePaymentConfig", expression = "java(JSONObject.parseObject(settlementItem.getSettlePaymentConfig(), InstSettlePaymentConfig.class))")
    })
    InstContractSettlementItem convertSettlementItemDomain(InstContractSettlementItemPO settlementItem);


    @Mappings({
            @Mapping(target = "instProductType", source = "instType"),
            @Mapping(target = "contractEntity", source = "entity")
    })
    InstContractBaseInfoPO convertRequest2BaseInfoPO(InstChannelMerchantCodeRequest request);

    InstContractMidMappingPO convertRequest2MidMappingPO(InstChannelMerchantCodeRequest request);

    AccumulationResponse convertAccumulationResponsePO(AccumulationResponsePO responsePO);

}
