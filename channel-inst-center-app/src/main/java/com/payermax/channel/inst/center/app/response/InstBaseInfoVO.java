package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstBaseInfoVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 21:17
 * @Version 1.0
 */
@Data
public class InstBaseInfoVO implements Serializable {


    private static final long serialVersionUID = -8960778893120568172L;
    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 机构编码
     */
    private String instCode;

    /**
     * 机构品牌标识
     */
    private Long instBrandId;

    /**
     * 机构名称
     */
    private String instName;

    /**
     * 机构类型，可多个
     */
    private String instTypes;

    /**
     * 主体所在地
     */
    private String entityCountry;

    /**
     * 是否FATF成员国 Y:是 N:否
     */
    private String isFatfMember;

    /**
     * 简介
     */
    private String remark;

    /**
     * 状态 Y：启用，N：停用
     */
    private String status;

    /**
     * 机构品牌名称
     */
    private String instBrandName;

    /**
     * 机构品牌编码
     */
    private String instBrandCode;

    /**
     * 负责BD
     */
    private String bdId;

    /**
     * 负责AM
     */
    private String amId;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    /**
     * 更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    /**
     * 机构产品
     */
    private List<InstProductVO> instProductVOS;

    /**
     * 接入国家/地区
     */
    private String countrys;

    /**
     * 公司注册名
     */
    private String registerName;

    /**
     * 机构联系人列表
     */
    private List<InstContactVO> instContactVOS;

    /**
     * 机构合同列表
     */
    private List<InstContractVO> instContractVOS;

    /**
     * 申请单列表
     */
    private List<String> applyNos;
}
