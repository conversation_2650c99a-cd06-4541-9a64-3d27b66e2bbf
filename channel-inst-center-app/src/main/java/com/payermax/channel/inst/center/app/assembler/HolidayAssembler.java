package com.payermax.channel.inst.center.app.assembler;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.facade.response.calendar.HolidayChangeItem;
import com.payermax.channel.inst.center.facade.response.calendar.HolidayInfo;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialCalendarHolidayPO;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstFinancialHolidaysUpdateLogPO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> tracy
 * @version 2024/12/10 14:38
 */
public class HolidayAssembler {
    public static List<HolidayChangeItem> holidayChangeItemsConvert(List<InstFinancialHolidaysUpdateLogPO> logPOS) {
        List<HolidayChangeItem> holidayChangeItems = new ArrayList<>();
        if (logPOS != null) {
            for (InstFinancialHolidaysUpdateLogPO logPO : logPOS) {
                InstFinancialCalendarHolidayPO origin = JSON.parseObject(logPO.getOriginInfo(), InstFinancialCalendarHolidayPO.class);

                HolidayInfo originInfo = new HolidayInfo();
                originInfo.setIsHoliday(!origin.getIsWorkday());
                originInfo.setHolidayName(origin.getDescription());
                originInfo.setCalendarType(logPO.getCalendarType());
                originInfo.setCountry(logPO.getCountry());
                originInfo.setCurrency(logPO.getCurrency());

                InstFinancialCalendarHolidayPO latest = JSON.parseObject(logPO.getLatestInfo(), InstFinancialCalendarHolidayPO.class);
                HolidayInfo latestInfo = new HolidayInfo();
                latestInfo.setIsHoliday(!latest.getIsWorkday());
                latestInfo.setHolidayName(latest.getDescription());
                latestInfo.setCalendarType(logPO.getCalendarType());
                latestInfo.setCountry(logPO.getCountry());
                latestInfo.setCurrency(logPO.getCurrency());

                HolidayChangeItem holidayChangeItem = new HolidayChangeItem();
                holidayChangeItem.setHolidayDate(origin.getHolidayDate().toString());
                holidayChangeItem.setOriginInfo(originInfo);
                holidayChangeItem.setLatestInfo(latestInfo);
                holidayChangeItems.add(holidayChangeItem);
            }
        }
        return holidayChangeItems;
    }
}
