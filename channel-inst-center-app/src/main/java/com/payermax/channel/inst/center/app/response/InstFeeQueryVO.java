package com.payermax.channel.inst.center.app.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/9
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InstFeeQueryVO extends InstContractBaseResponse implements Serializable {




    @ApiModelProperty(notes = "支付币种")
    private String payCurrency;

    /**
     * 机构原始mid，及标准化后的内部渠道商户号
     */
    @ApiModelProperty(notes = "渠道商户号")
    private String channelMerchantNo;


    /**
     * 机构合同中行业标识，及标准化后的内部统一mcc
     */
    @ApiModelProperty(notes = "MCC 逻辑")
    private String mccLogic;
    @ApiModelProperty(notes = "行业标识")
    private String originMcc;
    @ApiModelProperty(notes = "标准化MCC")
    private String standardMcc;

    /**
     * 资金源
     */
    @ApiModelProperty(notes = "资金来源")
    private String fundingSource;
    @ApiModelProperty(notes = "二级商户号")
    private String subMerchantNo;
    /**
     * 清算网络
     */
    @ApiModelProperty(notes = "清算网络")
    private String clearNetwork;

    /**
     * 费用承担方
     */
    @ApiModelProperty(notes = "费用承担方")
    private String feeBearer;

    /**
     * 卡类型
     */
    @ApiModelProperty(notes = "卡类型")
    private String cardType;

    /**
     * 客户类型: toB/toC
     */
    @ApiModelProperty(notes = "客户类型")
    private String customerType;



    @ApiModelProperty(notes = "交易国家")
    private String transactionCountry;

}
