package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName InstProductCapabilityVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/27 1:04
 */
@Data
public class InstProductCapabilityVO implements Serializable {

    private static final long serialVersionUID = -7071982187498298853L;

    @ApiModelProperty(notes = "产品能力编码")
    private String capabilityCode;

    @ApiModelProperty(notes = "产品编码")
    private String instProductCode;

    @ApiModelProperty(notes = "目标机构")
    private String targetOrg;

    @ApiModelProperty(notes = "卡组织")
    private String cardOrg;

    @ApiModelProperty(notes = "国家")
    private String country;

    @ApiModelProperty(notes = "币种")
    private String currency;

    @ApiModelProperty(notes = "金额倍数限制")
    private String amountMultipleLimit;

    @ApiModelProperty(notes = "单笔限额")
    private String amountSingleLimit;

    @ApiModelProperty(notes = "日累计限额")
    private String amountDayLimit;

    @ApiModelProperty(notes = "月累计限额")
    private String amountMonthLimit;

    @ApiModelProperty(notes = "支付工具")
    private String paymentTool;

    @ApiModelProperty(notes = "支付流程")
    private String paymentFlow;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "结算扩展数据 json")
    private String settlementExtraInfo;

    @ApiModelProperty(notes = "扩展数据 json")
    private String extraInfo;

    @ApiModelProperty(notes = "操作version")
    private String version;

    @ApiModelProperty(hidden = true)
    private String feeGroupId;

    /**
     * 下列属性对应费用列表中，一个产品下的操作维度的表格对象
     */

    @ApiModelProperty(notes = "是否已完成配置 Y:是 N:否")
    private String isConfigured;

    /**
     * 一个国家对应的一组能力（即一组目标机构）
     */
    @ApiModelProperty(notes = "目标机构集合，多个直接用逗号相连")
    private String targetOrgs;

    /**
     * 一次能力操作对应的卡组织
     */
    @ApiModelProperty(notes = "卡组织集合，多个直接用逗号相连")
    private String cardOrgs;

    /**
     * 一个国家对应的一套费用配置
     */
    @ApiModelProperty(hidden = true)
    private InstProductFeeVO productFeeVO;

    /**
     * 一个国家对应的一套费用配置下的费用明细列表
     * （该字段对应表格一列不展示，作为后面费用信息表格的数据源）
     */
    @ApiModelProperty(hidden = true)
    private List<InstTransFeeVO> feeDetailVOs;


}
