package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.app.model.contract.desensitizer.SensitiveInfo;
import com.payermax.channel.inst.center.app.model.contract.desensitizer.SensitiveType;
import com.payermax.channel.inst.center.facade.request.contract.config.content.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/11
 * @DESC
 */
@Data
public class InstContractFeeItemVO implements Serializable{

    private String instOriginProductNo;

    private String instContractFeeItemNo;

    private FeeConfig tradeFeeConfig;

    /**
     * VA 账号费
     */
    private FeeConfig vaAccountFeeConfig;


    private List<TaxConfig> taxFeeConfig;

    private FxConfig fxFeeConfig;

    private FeeConfig refundFeeConfig;

    /**
     * CHARGE_BACK, 拒付手续费
     */
    private FeeConfig cbFeeConfig;

    /**
     * 调单费用
     */
    private FeeConfig retrievalRequestFeeConfig;


    /**
     * 费用配置，添加权限注解
     * {@link com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig}
     */
    @Data
    public static class FeeConfig implements Serializable {

        /**
         * 费用类型
         * <p>
         * {@link  FeeType}
         */
        private String feeType;

        /**
         * 交易费用算费基准，针对交易金额，还是 交易金额+税费金额一起收 手续费
         * <p>
         * {@link FeeCalculateBaseMode}
         */
        private String feeBasementMode;

        /**
         * 算费时机，交易日算费 还是 结算日算费
         * 影响「渠道账」清分步骤 银行时间，进而影响月结/日结进程
         * <p>
         * {@link FeeCalculateTiming}
         */
        private String feeCalculateTiming;

        /**
         * 扣费币种
         */
        private String feeDeductCurrency;

        /**
         * 算费方式
         * <p>
         * {@link  FeeCalculateType}
         */
        private String calculateType;

        /**
         * 费率百分比
         */

        @SensitiveInfo(value = SensitiveType.FEE_VALUE)
        private String feeRateValue;

        /**
         * 百分比计算上下限及币种（百分比计算时封顶、最低费用）
         */
        private BigDecimal percentMinAmount;

        private BigDecimal percentMaxAmount;

        private String percentMinAmountCurrency;

        private String percentMaxAmountCurrency;

        /**
         * 固定手续费金额 + 币种
         */
        @SensitiveInfo(value = SensitiveType.FEE_VALUE)
        private String feeValue;

        /**
         * 费用币种
         */
        private String feeCurrency;

        /**
         * 阶梯费用的周期.
         * <p>
         * MONTH - 月
         * YEAR  - 年
         */
        private String accumulationCycle;

        /**
         * 阶梯统计模式
         * <p>
         * NUMBER - 按笔数
         * AMOUNT - 按金额
         */
        private String accumulationType;

        /**
         * 阶梯统计模式
         * <p>
         * NUMBER - 按笔数
         * AMOUNT - 按金额
         */
        private String accumulationMethod;

        /**
         * 阶梯统计的范围
         * <p>
         * PARTS -- 部分
         * TOTAL -- 全部
         */
        private String accumulationRange;

        /**
         * 阶梯扣费时机
         * <p>
         * PERIODIC -- 定期
         * REALTIME -- 实时
         */
        private String accumulationDeductTime;

        /**
         *
         */
        private String accumulationJoin;

        private String accumulationKey;

        private String accumulationMode;

        /**
         * 是否大阶梯
         */
        // @JsonProperty("accumulation")
        // @JSONField(name = "accumulation")
        private Boolean isAccumulation;

        /**
         * 阶梯费率配置- 仅当 calculateType == 'STEP_COMBINE' 时需要用到。
         */
        private List<FeeConfig.FeeStepCombineConfig> stepPercentAmount = new ArrayList<>();

        /**
         * 例如 退款手续费配置，包含FEE_REFUND字段，表示正向交易费用是否退回
         */
        private Map<String, String> extendFields = new HashMap<>();

        /**
         * 阶梯计费，每个阶梯定义
         * <p>
         * 阶梯即在特定的交易量区间内使用不同的费率模式。
         * <p>
         * 例如 0  ～100万 费率-> 3%
         * 例如 100～500万 费率-> 2%
         * 例如 500～ +∞   费率-> 1%
         * <p>
         * 阶梯详细列表内,包含了左闭右开的两个金额，以及在这个金额区间内的计费方式。
         * <p>
         * 区间内的计费方式依然会包含 单笔比例、单笔固定、单笔组合 三种模式
         */
        @Data
        public static class FeeStepCombineConfig implements Serializable {

            /**
             * 左闭右开
             */
            private BigDecimal leftAmount;

            private BigDecimal rightAmount;

            /**
             * 算费方式
             */
            private String calculateType;

            /**
             * 费率百分比
             */
            @SensitiveInfo(value = SensitiveType.FEE_VALUE)
            private String feeRateValue;

            /**
             * 百分比计算上下限及币种
             */
            private BigDecimal percentMinAmount;

            private BigDecimal percentMaxAmount;

            private String percentMinAmountCurrency;

            private String percentMaxAmountCurrency;

            /**
             * 固定手续费金额 + 币种
             */
            @SensitiveInfo(value = SensitiveType.FEE_VALUE)
            private String feeValue;

            private String feeCurrency;

        }
    }


    /**
     * 税费配置，添加权限注解
     * {@link com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig}
     */
    @Data
    public static class TaxConfig implements Serializable {

        /**
         * 税种
         */
        private String taxType;

        /**
         * 计算方式
         * <p>
         * {@link TaxCalculateFormula}
         */
        private String taxCalculateType;

        /**
         * 费率百分比
         */
        @SensitiveInfo(value = SensitiveType.TAX_VALUE)
        private String feeRateValue;

        /**
         * 该税种是否可抵扣
         * <p>
         * Y-可以抵扣
         * N-不可以抵扣
         */
        private String deductible;

    }

    /**
     * 加点值为添加注解，用于数据脱敏
     * {@link com.payermax.channel.inst.center.facade.request.contract.config.FxConfig}
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FxConfig implements Serializable{

        /**
         * 合约外汇加点
         */
        @SensitiveInfo(value = SensitiveType.FX_VALUE)
        private String contractFxSpread;

        /**
         * 外汇加点
         */
        @SensitiveInfo(value = SensitiveType.FX_VALUE)
        private String fxSpread;

        /**
         * 换汇时机
         */
        private String currencyExchangeTime;

    }

}
