package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.app.dto.InstContractFeeRowItemDTO;
import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.app.dto.impl.TaxInfoDTO;
import com.payermax.channel.inst.center.common.enums.LangEnum;
import com.payermax.channel.inst.center.common.utils.ExcelUtil;

import java.util.List;

/**
 * InstContractExcelDataTransfer
 *
 * <AUTHOR>
 * @desc
 */
public interface InstContractExcelDataTransfer {

    /**
     * InstContractFeeRowItemDTO excel自定义数据转换
     * @param instContractFeeRowItemDTO
     * @param excelCellDataList
     */
    void instContractFeeRowItemDtoTransfer(InstContractFeeRowItemDTO instContractFeeRowItemDTO, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList);

    /**
     * Excel 税费自定义转换
     * @param langEnum
     * @param excelCellDataList
     */
    List<TaxInfoDTO> taxInfoListTransfer(LangEnum langEnum, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList);

    /**
     * InstContractSettleRowItemDTO excel自定义数据转换
     * @param instContractSettleRowItemDTO 结算DTO
     * @param excelCellDataList excel数据
     */
    void instContractSettleRowItemDtoTransfer(InstContractSettleRowItemDTO instContractSettleRowItemDTO, List<ExcelUtil.DefineExcelParser.ExcelCellData> excelCellDataList);
}
