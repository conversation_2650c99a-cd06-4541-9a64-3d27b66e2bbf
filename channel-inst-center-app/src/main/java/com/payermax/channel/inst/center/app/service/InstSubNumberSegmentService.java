package com.payermax.channel.inst.center.app.service;


import com.payermax.channel.inst.center.infrastructure.entity.InstSubNumberSegmentEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubNumberSegmentQueryEntity;

import java.util.List;


/**
 * 机构账号可用号段Service
 *
 * <AUTHOR>
 * @date 2022/10/08 17:35
 */
public interface InstSubNumberSegmentService {

    /**
     * 查询账号可用号段
     *
     * @param instSubNumberSegmentQueryEntity
     * @return
     */
    List<InstSubNumberSegmentEntity> queryListByAccountId(InstSubNumberSegmentQueryEntity instSubNumberSegmentQueryEntity);

    /**
     * 更新号段表信息
     *
     * @param instSubNumberSegmentEntity
     * @param originalMaxUsed
     * @return
     */
    int updateById(InstSubNumberSegmentEntity instSubNumberSegmentEntity, String originalMaxUsed);

    /**
     * 查询账号可用号段记录数
     *
     * @param instSubNumberSegmentQueryEntity
     * @return
     */
    Integer queryCountByAccountId(InstSubNumberSegmentQueryEntity instSubNumberSegmentQueryEntity);
}
