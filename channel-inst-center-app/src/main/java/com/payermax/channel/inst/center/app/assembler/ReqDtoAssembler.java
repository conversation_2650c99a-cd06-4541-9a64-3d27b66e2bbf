package com.payermax.channel.inst.center.app.assembler;

import com.payermax.channel.inst.center.app.request.*;
import com.payermax.channel.inst.center.facade.request.FxInfoQueryRequest;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.entity.query.ApplyOrderQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstBaseInfoQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 请求转换
 *
 * <AUTHOR>
 * @date 2022/5/15 21:11
 */
@Mapper(componentModel = "spring")
public interface ReqDtoAssembler {





    /**
     * 转换成InstDdEntity
     *
     * @param ddReqDTO
     * @return
     */
    InstDdEntity toInstDdEntity(InstDdReqDTO ddReqDTO);

    /**
     * 转换成InstProductEntity
     *
     * @param productReqDTO
     * @return
     */
    InstProductEntity toInstProductEntity(InstProductReqDTO productReqDTO);

    /**
     * 转换成InstApplyOrderEntity
     *
     * @param applyOrderReqDTO
     * @return
     */
    InstApplyOrderEntity toInstApplyOrderEntity(InstApplyOrderReqDTO applyOrderReqDTO);

    /**
     * 转换成ApplyOrderQueryEntity
     *
     * @param applyOrderReqDTO
     * @return
     */
    @Mappings({
            @Mapping(source = "instBaseInfoReqDTO", target = "instBaseInfoEntity"),
            @Mapping(source = "instBrandReqDTO", target = "instBrandEntity"),
            @Mapping(source = "instDdReqDTO", target = "instDdEntity")
    })
    ApplyOrderQueryEntity toApplyOrderQueryEntity(InstApplyOrderReqDTO applyOrderReqDTO);

    /**
     * 转换成InstDdSurveyEntity
     *
     * @param ddSurveyReqDTO
     * @return
     */
    InstDdSurveyEntity toInstDdSurveyEntity(InstDdSurveyReqDTO ddSurveyReqDTO);

    /**
     * NDA：DTO转换成Entity
     *
     * @param instNdaReqDTO
     * @return
     */
    InstNdaEntity toInstNdaEntity(InstNdaReqDTO instNdaReqDTO);

    /**
     * 机构联系人：DTO转换成Entity
     *
     * @param instContactReqDTO
     * @return
     */
    InstContactEntity toInstContactEntity(InstContactReqDTO instContactReqDTO);

    /**
     * 机构联系人：DTO转换成Entity
     *
     * @param instContactReqDTOs
     * @return
     */
    List<InstContactEntity> toInstContactEntities(List<InstContactReqDTO> instContactReqDTOs);

    /**
     * 机构银行卡信息：DTO转换成Entity
     *
     * @param instBankAccountReqDTO
     * @return
     */
    InstBankAccountEntity toInstBankAccountEntity(InstBankAccountReqDTO instBankAccountReqDTO);

    /**
     * 机构银行卡信息：DTO转换成Entity
     *
     * @param instBankAccountReqDTOs
     * @return
     */
    List<InstBankAccountEntity> toInstBankAccountEntities(List<InstBankAccountReqDTO> instBankAccountReqDTOs);

    /**
     * 机构品牌信息：DTO转换成Entity
     *
     * @param instBrandReqDTO
     * @return
     */
    InstBrandEntity toInstBrandEntity(InstBrandReqDTO instBrandReqDTO);

    /**
     * 机构信息：DTO转换成Entity
     *
     * @param instBaseInfoReqDTO
     * @return
     */
    InstBaseInfoEntity toInstBaseInfoEntity(InstBaseInfoReqDTO instBaseInfoReqDTO);

    /**
     * 机构信息：DTO转换成Entity
     *
     * @param instBaseInfoReqDTO
     * @return
     */
    InstBaseInfoQueryEntity toInstBaseInfoQueryEntity(InstBaseInfoReqDTO instBaseInfoReqDTO);

    /**
     * KYC：DTO转换成Entity
     *
     * @param instKycReqDTO
     * @return
     */
    InstKycEntity toInstKycEntity(InstKycReqDTO instKycReqDTO);

    /**
     * 审核资料：DTO转换成Entity
     *
     * @param instAuditDataReqDTO
     * @return
     */
    InstAuditDataEntity toInstAuditDataEntity(InstAuditDataReqDTO instAuditDataReqDTO);

    /**
     * 审核结果：DTO转换成Entity
     *
     * @param instAuditResultReqDTO
     * @return
     */
    InstAuditResultEntity toInstAuditResultEntity(InstAuditResultReqDTO instAuditResultReqDTO);

    /**
     * 集成需求单：DTO转换成Entity
     *
     * @param reqDTO
     * @return
     */
    InstRequirementOrderEntity toInstRequirementOrderEntity(InstRequirementOrderReqDTO reqDTO);

    /**
     * 集成排期：DTO转换成Entity
     *
     * @param reqDTO
     * @return
     */
    InstRequirementScheduleEntity toInstRequirementScheduleEntity(InstRequirementScheduleReqDTO reqDTO);

    /**
     *
     * @param reqDTO
     * @return
     */
    InstRequirementScheduleQueryEntity toInstRequirementScheduleQueryEntity(InstRequirementScheduleQueryReqDTO reqDTO);

    /**
     * 机构账号：DTO转换成Entity
     *
     * @param reqDTO
     * @return
     */
    InstAccountEntity toInstAccountEntity(InstAccountReqDTO reqDTO);

    /**
     * 机构商户报备：DTO转换成Entity
     *
     * @param reqDTO
     * @return
     */
    InstReportMerchantEntity toInstReportMerchantEntity(InstReportMerchantReqDTO reqDTO);

    /**
     * 机构对账：DTO转换成Entity
     *
     * @param reqDTO
     * @return
     */
    InstReconcileEntity toInstReconcileEntity(InstReconcileReqDTO reqDTO);


    /**
     * 合同：DTO转换成Entity
     *
     * @param instContractReqDTO
     * @return
     */
    InstContractEntity toInstContractEntity(InstContractReqDTO instContractReqDTO);

    /**
     * 产品能力：DTO 转换成 Entity
     * @param productCapabilityReqDTO
     * @return
     */
    InstProductCapabilityEntity toInstProductCapabilityEntity(InstProductCapabilityReqDTO productCapabilityReqDTO);

    /**
     * 产品能力：DTO 转换成 Entity
     * @param productCapabilityReqDTOs
     * @return
     */
    List<InstProductCapabilityEntity> toInstProductCapabilityEntities(List<InstProductCapabilityReqDTO> productCapabilityReqDTOs);

    /**
     * 产品费用：DTO 转换成 Entity
     * @param instProductFeeReqDTO
     * @returnee
     */
    InstProductFeeEntity toInstProductFeeEntity(InstProductFeeReqDTO instProductFeeReqDTO);

    /**
     * 产品费用：DTO 转换成 Entity
     * @param instProductFeeReqDTOs
     * @returnee
     */
    List<InstProductFeeEntity> toInstProductFeeEntities(List<InstProductFeeReqDTO> instProductFeeReqDTOs);

    /**
     * 交易费用：DTO 转换成 Entity
     * @param instTransFeeDTO
     * @returnee
     */
    InstTransFeeEntity toInstTransFeeEntity(InstTransFeeDTO instTransFeeDTO);

    /**
     * 交易费用：DTO 转换成 Entity
     * @param instTransFeeDTOs
     * @returnee
     */
    List<InstTransFeeEntity> toInstTransFeeEntities(List<InstTransFeeDTO> instTransFeeDTOs);

    /**
     * 目标机构：DTO 转换成 Entity
     * @param instTargetOrgReqDTO
     * @returnee
     */
    InstTargetOrgEntity toInstTargetOrgEntity(InstTargetOrgReqDTO instTargetOrgReqDTO);

    /**
     * 目标机构：DTO 转换成 Entity
     * @param instTargetOrgReqDTOs
     * @returnee
     */
    List<InstTargetOrgEntity> toInstTargetOrgEntities(List<InstTargetOrgReqDTO> instTargetOrgReqDTOs);

    /**
     * 转换成InstBusinessDictEntity
     * @param instBusinessDictReqDTO
     * @return
     */
    InstBusinessDictEntity toInstBusinessDictEntity(InstBusinessDictReqDTO instBusinessDictReqDTO);

    /**
     * 转换成InstBusinessDictEntity
     * @param instBusinessDictReqDTOs
     * @return
     */
    List<InstBusinessDictEntity> toInstBusinessDictEntities(List<InstBusinessDictReqDTO> instBusinessDictReqDTOs);

    /**
     * 转换成InstAccountKeyEntity
     *
     * @param instAccountKeyReqDTO
     * @return
     */
    InstAccountKeyEntity toInstAccountKeyEntity(InstAccountKeyReqDTO instAccountKeyReqDTO);

    /**
     * 转换成InstAccountKeyEntity
     *
     * @param instAccountKeyReqDTOs
     * @return
     */
    List<InstAccountKeyEntity> toInstAccountKeyEntities(List<InstAccountKeyReqDTO> instAccountKeyReqDTOs);

    /**
     * 机构信息：DTO转换成Entity
     *
     * @param instInfoReqDTO
     * @return
     */
    InstBaseInfoEntity toInstInfoEntity(InstInfoReqDTO instInfoReqDTO);

    /**
     * 换汇信息查询request映射
     */
    InstFxSettleReqDTO toInstFxReqDTO(FxInfoQueryRequest fxInfoQueryRequest);
}
