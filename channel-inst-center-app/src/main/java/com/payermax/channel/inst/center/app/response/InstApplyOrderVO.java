package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstApplyOrderVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 23:45
 */
@Data
public class InstApplyOrderVO implements Serializable {

    private static final long serialVersionUID = 5074093327881015984L;

    /**
     * 申请时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    /**
     * 接入类型
     */
    private String applyType;

    /**
     * 申请单号
     */
    private String applyNo;

    private Long instBrandId;

    private Long brandId;

    private Long instId;

    /**
     * 机构简称
     */
    private String instName;

    /**
     * 渠道类型
     */
    private String channelType;

    /**
     * 接入国家/地区
     */
    private String countrys;

    /**
     * 接入产品
     */
    private String products;

    /**
     * 接入价值
     */
    private String reason;

    /**
     * 我司主体
     */
    private String shareitEntity;

    /**
     * 合作模式
     */
    private String cooperationMode;

    /**
     * 状态
     */
    private String status;

    /**
     * 渠道BD
     */
    private String bdId;

    /**
     * 渠道BD
     */
    private String bdName;

    /**
     * 渠道AM
     */
    private String amId;

    /**
     * 渠道AM
     */
    private String amName;

    /**
     * 接入优先级
     */
    private String priority;

    /**
     * 期望投产日期
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date expectReleaseTime;

    /**
     * 接入各阶段状态
     */
    private String stageStatus;

    /**
     * 最近更新时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;
}
