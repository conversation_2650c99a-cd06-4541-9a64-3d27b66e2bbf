package com.payermax.channel.inst.center.app.assembler.domain;


import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.dto.AbstractInstContractFeeDTO;
import com.payermax.channel.inst.center.app.dto.impl.*;
import com.payermax.channel.inst.center.app.model.contract.dataParser.FeeItemConvertUtils;
import com.payermax.channel.inst.center.app.model.contract.dataParser.InstProductParseContext;
import com.payermax.channel.inst.center.app.model.contract.excelParser.InstContractContext;
import com.payermax.channel.inst.center.app.request.InstContractParseRequestDTO;
import com.payermax.channel.inst.center.app.request.InstProductQueryRequestDTO;
import com.payermax.channel.inst.center.app.response.InstContractImportDTO;
import com.payermax.channel.inst.center.app.response.InstProductItemVO;
import com.payermax.channel.inst.center.common.enums.instcontract.*;
import com.payermax.channel.inst.center.common.utils.ConvertUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettleDateConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstTaxConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractBaseInfo;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractOriginProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractStandardProduct;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractVersionInfo;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeCalculateTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.FeeTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.content.WithdrawMethodEnum;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.infrastructure.entity.InstContractDraft;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import org.mapstruct.*;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/24
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {
            JSON.class,Collectors.class, Objects.class, FeeTypeEnum.class, FeeCalculateTypeEnum.class,
            FeeItemConvertUtils.class, FeeCalculateBaseModeEnumV2.class, FeeCalculateTypeEnumV2.class, FeeCalculateTimingEnumV2.class, TaxCalculateTypeEnumV2.class,
            ConvertUtils.class, AccumulationRangeV2.class, AccumulationTypeEnumV2.class, AccumulationCycleEnumV2.class, AccumulationMethodEnumV2.class, AccumulationDeductTimeEnumV2.class,
            FeeTypeEnum.class, WithdrawMethodEnum.class, RoundingModeEnumV2.class, CurrencyExchangeTimingEnumV2.class, ClearNetworkEnum.class, FeeBearerEnum.class, CustomerTypeEnum.class,
            CardTypeEnum.class
        },
        uses = {FeeItemConvertUtils.class}
)
public interface InstContractDraftAssembler {


    @Mappings({
            @Mapping(target = "contractEntity", expression = "java(!request.getEntity().isEmpty() ? request.getEntity() : null)"),
            @Mapping(target = "instCode", expression = "java(!request.getInstCode().isEmpty() ? request.getInstCode() : null)"),
            @Mapping(target = "status", expression = "java(!request.getStatus().isEmpty() ? request.getStatus() : null)"),
    })
    InstContractDraft request2Draft(InstProductQueryRequestDTO request);

    @Mappings({
            @Mapping(target = "paymentType", expression = "java(FeeItemConvertUtils.feeToPaymentType(draft))"),
            @Mapping(target = "target", expression = "java(FeeItemConvertUtils.feeToTarget(draft))"),
    })
    InstProductItemVO draft2ProductItem(InstContractDraft draft);

    @Mappings({
            @Mapping(target = "draft", source = "draft"),
            @Mapping(target = "draftDataDto", expression = "java(FeeItemConvertUtils.data2FeeDto(draft))"),
    })
    InstProductParseContext draft2InstProductContext(InstContractDraft draft);
    @Mappings({
            @Mapping(target = "taxType", source = "taxType"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(taxInfo.getTaxRatio()))"),
            @Mapping(target = "taxCalculateType", expression = "java(FeeItemConvertUtils.getTaxCalculateFormula(taxInfo.getTaxCalculateType()))"),
            @Mapping(target = "deductible", source = "deductible"),
    })
    InstTaxConfig draft2TaxInfoList(TaxInfoDTO taxInfo);


    /**
     * 原始产品转PO
     */
    @Mappings({
            @Mapping(target = "settlementItems",ignore = true)
    })
    InstContractOriginProductPO originProduct2Po(InstContractOriginProduct originProduct);

    /**
     * 标准产品转PO
     */
    InstContractStandardProductPO standardProduct2Po(InstContractStandardProduct standardProduct);


    /**
     * PO转标准产品
     */
    InstContractStandardProduct productPo2Standard(InstContractStandardProductPO standardProduct);

    /**
     * 合同基础信息转PO
     */
    InstContractBaseInfoPO contract2Po(InstContractBaseInfo contractBaseInfo);

    /**
     * 合同版本信息转PO
     */
    InstContractVersionInfoPO contractVersion2Po(InstContractVersionInfo contractVersionInfo);

    InstContractImportDTO context2ImportDTO(InstContractContext context);

    InstContractContext importDTO2Context(InstContractImportDTO instContractImportDTO);

    InstContractContext request2Context(InstContractParseRequestDTO request);

    void vaFeeDtoContextFill(@MappingTarget InstContractFeeVaDTO dto, InstContractContext context);

    @Mappings({
            @Mapping(target = "accumulatedInfoList", ignore = true),
            @Mapping(target = "taxInfoList",  ignore = true),
            @Mapping(target = "accountFeeInfo", ignore = true),
    })
    InstContractFeeVaDTO accountInfo2FeeDto(InstContractFeeVaDTO accountInfo);

    /**
     * 费用信息解析
     */
    @Mappings({
            @Mapping(target = "payCurrency", source = "transactionCurrency"),
            @Mapping(target = "originMid", source = "externalMid"),
            @Mapping(target = "accumulationCycle", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(AccumulationCycleEnumV2.class, draft.getAccumulateCycle()))"),
            @Mapping(target = "accumulationType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(AccumulationTypeEnumV2.class, draft.getAccumulateType()))"),
            @Mapping(target = "accumulationMethod", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(AccumulationMethodEnumV2.class, draft.getAccumulateEffectMethod()))"),
            @Mapping(target = "accumulationRange", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(AccumulationRangeV2.class, draft.getAccumulateEffectScope()))"),
            @Mapping(target = "accumulationDeductTime", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(AccumulationDeductTimeEnumV2.class, draft.getDeductFeeTiming()))"),
            @Mapping(target = "roundingMode", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(RoundingModeEnumV2.class, draft.getRoundingMode()))"),
            @Mapping(target = "accumulationJoin", source = "isParticipateAccumulation"),
            @Mapping(target = "transactionCountry", source = "country"),
            @Mapping(target = "clearNetwork", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(ClearNetworkEnum.class, draft.getClearNetWork()))"),
            @Mapping(target = "feeBearer", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeBearerEnum.class, draft.getFeeBearer()))"),
            @Mapping(target = "customerType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(CustomerTypeEnum.class, draft.getCustomerType()))"),
            @Mapping(target = "cardType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(CardTypeEnum.class, draft.getCardType()))"),
    })
    InstContractFeeItemPO draft2FeeItem(InstContractFeePayinDTO draft);



    /**
     * PayIn 退款费用配置解析
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(String.valueOf(FeeTypeEnum.REFUND))"),
            @Mapping(target = "calculateType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateTypeEnumV2.class,feeInfo.getRefundCalculationMethod()))"),
            @Mapping(target = "feeBasementMode", ignore = true),
            @Mapping(target = "feeCurrency", source = "refundFeeCurrency"),
            @Mapping(target = "feeDeductCurrency", source = "refundFeeCurrency"),
            @Mapping(target = "feeValue", source = "refundFixedFee", qualifiedByName = "convertRawBigDecimal"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(feeInfo.getRefundPercentageRatio()))"),
            @Mapping(target = "percentMinAmount", source = "refundMinFee", qualifiedByName = "convertRawBigDecimal"),
            @Mapping(target = "percentMaxAmount", source = "refundMaxFee", qualifiedByName = "convertRawBigDecimal"),
            @Mapping(target = "extendFields", expression = "java(FeeItemConvertUtils.composeRefundFeeExtendFields(feeInfo))"),
    })
    FeeConfig draft2RefundConfig(InstContractFeePayinDTO feeInfo);


    /**
     * PayIn 争议费用配置解析
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(String.valueOf(FeeTypeEnum.CHARGEBACK))"),
            @Mapping(target = "calculateType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateTypeEnumV2.class,feeInfo.getCbCalculationMethod()))"),
            @Mapping(target = "feeBasementMode", ignore = true),
            @Mapping(target = "feeCurrency", source = "cbFeeCurrency"),
            @Mapping(target = "feeDeductCurrency", source = "cbFeeCurrency"),
            @Mapping(target = "feeValue", source = "cbFixedFee", qualifiedByName = "convertRawBigDecimal"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(feeInfo.getCbPercentageRatio()))"),
            @Mapping(target = "percentMinAmount", source = "cbMinFee", qualifiedByName = "convertRawBigDecimal"),
            @Mapping(target = "percentMaxAmount", source = "cbMaxFee", qualifiedByName = "convertRawBigDecimal"),
    })
    FeeConfig draft2ChargebackConfig(InstContractFeePayinDTO feeInfo);

    /*-------------------------- 手续费信息 ---------------------------*/
    /**
     * 交易手续费基础配置解析
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(String.valueOf(FeeTypeEnum.TRADE))"),
            @Mapping(target = "feeBasementMode", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateBaseModeEnumV2.class,feeInfo.getCalculationFormula()))"),
            @Mapping(target = "feeCalculateTiming", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateTimingEnumV2.class, feeInfo.getCalculateFeesTime()))"),
            @Mapping(target = "feeDeductCurrency", source = "deductionCurrency"),
            @Mapping(target = "accumulationMethod", source = "rangeUnit")
    })
    FeeConfig draft2TradeConfig(InstContractFeePayinDTO feeInfo);


    /**
     * VA 账号费基础配置解析
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(String.valueOf(FeeTypeEnum.VA_ACCOUNT))"),
            @Mapping(target = "feeBasementMode", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateBaseModeEnumV2.class,feeInfo.getCalculationFormula()))"),
            @Mapping(target = "feeDeductCurrency", source = "deductionCurrency"),
            @Mapping(target = "accumulationMethod", source = "rangeUnit")
    })
    FeeConfig draft2VaAccountFeeConfig(InstContractFeeVaDTO feeInfo);


    /**
     * 无阶梯手续费配置解析
     */
    @Mappings({
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(accumulatedInfo.getPercentageRatio()))"),
            @Mapping(target = "feeValue", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getFixedFee()))"),
            @Mapping(target = "feeCurrency", source = "accumulatedInfo.fixedFeeCurrency"),
            @Mapping(target = "calculateType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateTypeEnumV2.class,feeDTO.getCalculateMethod()))"),
            @Mapping(target = "percentMinAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getMinFee()))"),
            @Mapping(target = "percentMinAmountCurrency", source = "accumulatedInfo.minFeeCurrency"),
            @Mapping(target = "percentMaxAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getMaxFee()))"),
            @Mapping(target = "percentMaxAmountCurrency", source = "accumulatedInfo.maxFeeCurrency"),
    })
    FeeConfig singleAccumulationFeeParse(@MappingTarget FeeConfig tradeConfig, AccumulatedInfoDTO accumulatedInfo, AbstractInstContractFeeDTO feeDTO);

    /**
     * 交易手续费阶梯配置解析
     */
    @Mappings({
            @Mapping(target = "leftAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getAccumulateStepLower()))"),
            @Mapping(target = "rightAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getAccumulateStepUpper()))"),
            @Mapping(target = "percentMinAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getMinFee()))"),
            @Mapping(target = "percentMinAmountCurrency", source = "accumulatedInfo.minFeeCurrency"),
            @Mapping(target = "percentMaxAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getMaxFee()))"),
            @Mapping(target = "percentMaxAmountCurrency", source = "accumulatedInfo.maxFeeCurrency"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(accumulatedInfo.getPercentageRatio()))"),
            @Mapping(target = "calculateType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateTypeEnumV2.class,feeDTO.getCalculateMethod()))"),
            @Mapping(target = "feeValue", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(accumulatedInfo.getFixedFee()))"),
            @Mapping(target = "feeCurrency", source = "accumulatedInfo.fixedFeeCurrency"),
    })
    FeeConfig.FeeStepCombineConfig accumulationInfo2StepConfig(AccumulatedInfoDTO accumulatedInfo, AbstractInstContractFeeDTO feeDTO);

    /*-------------------------- 手续费信息 ---------------------------*/


    /*-------------------------- 结算信息 ---------------------------*/
    /**
     * 结算：基础信息 - 合同、产品相关信息
     */
    @Mappings({
            @Mapping(target = "instOriginProductNo", source = "originProduct.instOriginProductNo"),
            @Mapping(target = "payCurrency", source = "originFeeItem.payCurrency"),
            @Mapping(target = "settleCurrency", source = "settleInfo.settlementCurrency"),
    })
    InstContractSettlementItemPO convertBaseSettleItem(InstContractFeeItemPO originFeeItem, SettleInfoDTO settleInfo, InstContractOriginProductPO originProduct);

    /**
     * 结算：周期信息 - PayIn
     */
    @Mappings({
            @Mapping(target = "transactionStartDate",source = "tradingRangeStart"),
            @Mapping(target = "transactionEndDate",source = "tradingRangeEnd"),
            @Mapping(target = "billDate",source = "statementDate"),
            @Mapping(target = "paymentDate",source = "paymentDate"),
            @Mapping(target = "arriveDate",source = "arriveDate"),
    })
    InstSettleDateConfig settleItem2SettleDateConfig(SettleInfoDTO settleInfo);

    /**
     * 结算：fee_config
     */
    @Mappings({
            @Mapping(target = "feeType",expression = "java(FeeTypeEnum.SETTLEMENT.name())"),
            @Mapping(target = "feeValue",expression = "java(FeeItemConvertUtils.convertRawBigDecimal(settleInfo.getFixedFee()))"),
            @Mapping(target = "calculateType", expression = "java(FeeItemConvertUtils.getEnumByDescOnExcel(FeeCalculateTypeEnumV2.class,settleInfo.getCalculationMethod()))"),
            @Mapping(target = "feeCurrency",source = "fixedFeeCurrency"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(settleInfo.getPercentageRatio()))"),
            @Mapping(target = "percentMinAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(settleInfo.getMinFee()))"),
            @Mapping(target = "percentMaxAmount", expression = "java(FeeItemConvertUtils.convertRawBigDecimal(settleInfo.getMaxFee()))"),
    })
    FeeConfig settleItem2SettleFeeConfig(SettleInfoDTO settleInfo);

    /**
     * 结算：打款信息
     */
    @Mappings({
            @Mapping(target = "accountId",source = "settleInfo.settlementAccount"),
            @Mapping(target = "minSettleAmount",expression = "java(FeeItemConvertUtils.convertRawBigDecimal(settleInfo.getMinimumSettlementAmount()))"),
            @Mapping(target = "accountType",source = "accountType"),
            @Mapping(target = "withdrawMethod",expression = "java(FeeItemConvertUtils.getSettleMethod(settleInfo.getSettlementMethod()))"),
    })
    InstSettlePaymentConfig settleItem2SettlePaymentConfig(SettleInfoDTO settleInfo, String accountType);

    /*-------------------------- 结算信息 ---------------------------*/

}
