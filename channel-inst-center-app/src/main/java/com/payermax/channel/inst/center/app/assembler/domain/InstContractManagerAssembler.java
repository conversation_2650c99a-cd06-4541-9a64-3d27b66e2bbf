package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.payermax.channel.inst.center.app.response.InstContractVersionUpgradeNotifyDto;
import com.payermax.channel.inst.center.domain.entity.contract.management.*;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/13
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, Optional.class, FeeConfig.class, TaxConfig.class, StringUtils.class, BigDecimal.class
        })
public interface InstContractManagerAssembler {



    @Mappings({
            @Mapping(target = "originProducts", source = "originProducts", qualifiedByName = "originProductListDeepCopy"),
            @Mapping(target = "standardProducts", source = "standardProducts", qualifiedByName = "standardProductListDeepCopy"),
            @Mapping(target = "newlyCreated", expression = "java(Boolean.TRUE)")
    })
    InstContractVersionInfo versionInfoDeepCopy(InstContractVersionInfo instContractVersionInfo);

    @Named(value = "originProductListDeepCopy")
    @IterableMapping(qualifiedByName = "originProductDeepCopy")
    List<InstContractOriginProduct> originProductListDeepCopy(List<InstContractOriginProduct> data);

    @Named(value = "standardProductListDeepCopy")
    @IterableMapping(qualifiedByName = "standardProductDeepCopy")
    List<InstContractStandardProduct> standardProductListDeepCopy(List<InstContractStandardProduct> data);

    @Named(value = "feeItemListDeepCopy")
    @IterableMapping(qualifiedByName = "feeItemDeepCopy")
    List<InstContractFeeItem> feeItemListDeepCopy(List<InstContractFeeItem> data);

    @Named(value = "settleItemListDeepCopy")
    @IterableMapping(qualifiedByName = "settleItemDeepCopy")
    List<InstContractSettlementItem> settleItemListDeepCopy(List<InstContractSettlementItem> data);

    @Named(value = "originProductDeepCopy")
    @Mappings({
            @Mapping(target = "contractFeeItems", source = "contractFeeItems", qualifiedByName = "feeItemListDeepCopy"),
            @Mapping(target = "settlementItems", source = "settlementItems", qualifiedByName = "settleItemListDeepCopy")
    })
    InstContractOriginProduct originProductDeepCopy(InstContractOriginProduct data);

    @Named(value = "standardProductDeepCopy")
    InstContractStandardProduct standardDeepCopy(InstContractStandardProduct data);

    @Named(value = "feeItemDeepCopy")
    InstContractFeeItem feeItemDeepCopy(InstContractFeeItem data);

    @Named(value = "settleItemDeepCopy")
    InstContractSettlementItem settleItemDeepCopy(InstContractSettlementItem data);



    @Mappings({
            @Mapping(target = "originProducts", source = "originProducts", qualifiedByName = "originProductList2Po"),
            @Mapping(target = "standardProducts", source = "standardProducts", qualifiedByName = "standardProductList2Po")
    })
    InstContractVersionInfoPO convertVersionInfoPO(InstContractVersionInfo version);



    @Named(value = "originProductList2Po")
    @IterableMapping(qualifiedByName = "originProduct2Po")
    List<InstContractOriginProductPO> originProductList2Po(List<InstContractOriginProduct> data);

    @Named(value = "standardProductList2Po")
    @IterableMapping(qualifiedByName = "standardProduct2Po")
    List<InstContractStandardProductPO> standardProductList2Po(List<InstContractStandardProduct> data);

    @Named(value = "originProduct2Po")
    @Mappings({
            @Mapping(target = "contractFeeItems", source = "contractFeeItems", qualifiedByName = "feeItemList2Po"),
            @Mapping(target = "settlementItems", source = "settlementItems", qualifiedByName = "settleItemList2Po")
    })
    InstContractOriginProductPO originProduct2Po(InstContractOriginProduct data);

    @Named(value = "standardProduct2Po")
    InstContractStandardProductPO standardProduct2Po(InstContractStandardProduct data);


    @Named(value = "feeItemList2Po")
    @IterableMapping(qualifiedByName = "feeItem2Po")
    List<InstContractFeeItemPO> feeItemList2Po(List<InstContractFeeItem> data);

    @Named(value = "settleItemList2Po")
    @IterableMapping(qualifiedByName = "settleItem2Po")
    List<InstContractSettlementItemPO> settleItemList2Po(List<InstContractSettlementItem> data);

    @Named(value = "feeItem2Po")
    @Mappings({
            @Mapping(target = "feeConfig", expression = "java(JSON.toJSONString(data.getFeeConfigs()))"),
            @Mapping(target = "taxConfig", expression = "java(JSON.toJSONString(data.getTaxConfigs()))")
    })
    InstContractFeeItemPO feeItem2Po(InstContractFeeItem data);

    @Named(value = "settleItem2Po")
    @Mappings({
            @Mapping(target = "settleFeeConfig", expression = "java(JSON.toJSONString(data.getSettleFeeConfig()))"),
            @Mapping(target = "settleDateConfig", expression = "java(JSON.toJSONString(data.getSettleDateConfigs()))"),
            @Mapping(target = "settlePaymentConfig", expression = "java(JSON.toJSONString(data.getSettlePaymentConfig()))")
    })
    InstContractSettlementItemPO settleItem2Po(InstContractSettlementItem data);


    @Mapping(target = "status", ignore = true)
    InstContractVersionUpgradeNotifyDto version2Notify(InstContractVersionInfo version);
}
