package com.payermax.channel.inst.center.app.rocketmq.producer;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.ResDoAssembler;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.AccountStatusChangeMqInfo;
import com.payermax.channel.inst.center.domain.subaccount.request.InstSubAccountActivationMqInfo;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import com.payermax.infra.ionia.rocketmq.handler.IoniaRocketMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Date;
import java.util.Objects;

/**
 * 交融交换rocketMq发送器
 *
 * <AUTHOR>
 * @date 2022/10/13 21:37
 */
@Slf4j
@Component
public class InstCenterRocketProducer {

    @Value("${spring.application.name}")
    private String appName;

    @Value("${rocket.topic.channel.account.status.change.notify}")
    private String accountStaticChangeTopic;

    @NacosValue(value = "${rocket.topic.sub.account.activation.notify:topic_cost_center_va_account_activation_notice}", autoRefreshed = true)
    private String subAccountActivationTopic;

    @Autowired
    private ResDoAssembler resDoAssembler;

    @Autowired
    private IoniaRocketMqTemplate ioniaRocketMqTemplate;

    /**
     * 发送账号状态变更通知
     *
     * @param responseAccountDO
     */
    public SendResult sendAccountStaticChangeNotify(ResponseAccountDO responseAccountDO) {
        if (responseAccountDO == null || responseAccountDO.getInstFundsAccountEntity() == null || responseAccountDO.getInstSubFundsAccountEntity() == null) {
            return null;
        }

        // 发送业财VA账户激活通知（非号段，仅API和线下）
        sendSubAccountStatusActivationNotify(responseAccountDO.getInstFundsAccountEntity(), responseAccountDO.getInstSubFundsAccountEntity());

        AccountStatusChangeMqInfo accountStatusChangeInfo = resDoAssembler.toAccountStatusChangeInfo(responseAccountDO);
        return sendMessage(accountStaticChangeTopic, accountStatusChangeInfo, accountStatusChangeInfo.getSubAccountId());
    }

    /**
     * 发送业财VA账户激活通知（非号段模式，仅API和线下模式下激活通知业财）
     */
    public SendResult sendSubAccountStatusActivationNotify( InstFundsAccountEntity fundsAccount, InstSubFundsAccountEntity subFundsAccount) {
        if ((Objects.equals(fundsAccount.getSubAccountMode(), SubAccountModeEnum.API.getType()) || Objects.equals(fundsAccount.getSubAccountMode(), SubAccountModeEnum.OFFLINE.getType()))
                && Objects.equals(subFundsAccount.getStatus(), SubAccountStatusEnum.ACTIVATED.getStatus())) {
            InstSubAccountActivationMqInfo notifyDto = new InstSubAccountActivationMqInfo();
            notifyDto.setVaAccountNo(subFundsAccount.getSubAccountNo());
            notifyDto.setFundsAccountId(subFundsAccount.getAccountId());
            notifyDto.setActivationTime(Instant.now().toEpochMilli());
            notifyDto.setMerchantNo(subFundsAccount.getMerchantNo());
            notifyDto.setSubMerchantNo(subFundsAccount.getSubMerchantNo());
            SendResult sendResult = sendMessage(subAccountActivationTopic, notifyDto, subFundsAccount.getSubAccountNo());
            log.info("sendSubAccountStatusActivationNotify-sendResult:{}", JSON.toJSONString(sendResult)); 
            return sendResult;
        }
        return null;
    }

    /**
     * 同步发送消息
     *
     * @param topic
     * @param data
     */
    public <T extends BaseMqMessage> SendResult sendMessage(String topic, T data, String key) {
        data.setKey(key);
        data.setSendTime(new Date());
        data.setSource(appName);
        String traceId = TraceContext.traceId();
        if (StringUtils.isNotBlank(traceId)) {
            data.setTraceId(TraceContext.traceId());
        }
        return ioniaRocketMqTemplate.sendMsgSync(topic, data);
    }

}
