package com.payermax.channel.inst.center.app.rocketmq.consumer;

import com.payermax.channel.inst.center.app.manage.contract.InstContractManageService;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemModifiedNotifyDTO;
import com.payermax.channel.inst.center.common.constants.RocketMqConstant;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/24
 * @DESC
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
        topic = RocketMqConstant.TOPIC_INST_CENTER_CONTRACT_VERSION_UPGRADE,
        consumerGroup = RocketMqConstant.CG_CHANNEL_INST_CENTER_CONTRACT_VERSION_UPGRADE,
        // 设置广播模式，确保所有机器都消费
        messageModel = MessageModel.BROADCASTING,
        consumeThreadNumber = 5,
        //关键是这个属性
        consumeMode = ConsumeMode.CONCURRENTLY)
public class InstContractVersionUpgradeRocketConsumer extends BaseMqMessageListener<InstContractFeeItemModifiedNotifyDTO> implements RocketMQListener<InstContractFeeItemModifiedNotifyDTO> {

    private final InstContractManageService contractManageService;

    @Override
    protected void handleMessage(InstContractFeeItemModifiedNotifyDTO message) {
        try{
            log.info("InstContractVersionUpgradeRocketConsumer-机构合约升级, 开始刷新缓存:{}", message.getKey());
            contractManageService.instContractCacheRefresh();
            log.info("InstContractVersionUpgradeRocketConsumer-机构合约升级, 缓存刷新完毕:{}", message.getKey());
        } catch (Exception e) {
            log.error("InstContractVersionUpgradeRocketConsumer-机构合约升级, 缓存刷新失败:{}", message.getKey());
        }
    }

    @Override
    protected void overMaxRetryTimesMessage(InstContractFeeItemModifiedNotifyDTO message) {
        log.error("InstContractVersionUpgradeRocketConsumer-机构合约升级 MQ 重试失败，参数:{}", message.getKey());

    }

    @Override
    public void onMessage(InstContractFeeItemModifiedNotifyDTO message) {
        super.dispatchMessage(message);
    }
}
