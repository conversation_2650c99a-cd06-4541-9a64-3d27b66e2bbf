package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.facade.request.contract.config.FxConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 * @DESC
 */
@Data
public class InstContractFxQueryVO implements Serializable {

    @ApiModelProperty(notes = "我司主体")
    private String contractEntity;

    @ApiModelProperty(notes = "机构标识")
    private String instCode;

    @ApiModelProperty(notes = "机构产品类型")
    private String instProductType;

    @ApiModelProperty(notes = "支付币种")
    private String payCurrency;

    @ApiModelProperty(notes = "外汇信息")
    private FxConfig fxConfig;

    @ApiModelProperty(notes = "FX信息列表")
    private List<FxItem> children;


    @Data
    public static class FxItem extends InstContractBaseResponse {

        @ApiModelProperty(notes = "支付币种")
        private String payCurrency;

        @ApiModelProperty(notes = "外汇信息")
        private FxConfig fxConfig;

        private List<StandardProductMsg> standardProductMsgList;
    }

}
