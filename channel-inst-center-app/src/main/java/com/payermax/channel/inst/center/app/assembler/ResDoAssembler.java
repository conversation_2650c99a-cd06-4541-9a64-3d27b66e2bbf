package com.payermax.channel.inst.center.app.assembler;

import com.payermax.channel.inst.center.app.response.InstBankAccountVO;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.AccountStatusChangeMqInfo;
import com.payermax.channel.inst.center.domain.subaccount.response.*;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.facade.response.*;
import com.payermax.channel.inst.center.infrastructure.entity.*;
import com.payermax.channel.inst.center.infrastructure.repository.po.InstBrandPO;
import org.mapstruct.*;

import java.util.List;

/**
 * 请求转换
 *
 * <AUTHOR>
 * @date 2022/10/10 21:11
 */
@Mapper(componentModel = "spring", imports = {SubAccountStatusEnum.class})
public interface ResDoAssembler {

    /**
     * 转换成List<QueryAccountsResponse>
     *
     * @param queryAccountsResponseDOList
     * @return
     */
    List<QueryAccountsResponse> toQueryAccountsResponseList(List<QueryAccountsResponseDO> queryAccountsResponseDOList);

    /**
     * 转换成CreateSubAccountResponse
     *
     * @param createSubAccountResponseDO
     * @return
     */
    @Named("toCreateSubAccountResponse")
    CreateSubAccountResponse toCreateSubAccountResponse(CreateSubAccountResponseDO createSubAccountResponseDO);

    /**
     * 转换成CreateSubAccountResponse
     *
     * @param responseAccountDO
     * @return
     */
    @Mappings({
            @Mapping(target = "accountId", source = "instFundsAccountEntity.accountId"),
            @Mapping(target = "instCode", source = "instFundsAccountEntity.instCode"),
            @Mapping(target = "entity", source = "instFundsAccountEntity.entity"),
            @Mapping(target = "country", source = "instFundsAccountEntity.country"),
            @Mapping(target = "currency", source = "instFundsAccountEntity.currency"),
            @Mapping(target = "accountNo", source = "instFundsAccountEntity.accountNo"),
            @Mapping(target = "accountType", source = "instFundsAccountEntity.accountType"),
            @Mapping(target = "scenes", source = "instFundsAccountEntity.scenes"),
            @Mapping(target = "accountName", source = "instFundsAccountEntity.accountName"),
            @Mapping(target = "isSupportSubAccount", source = "instFundsAccountEntity.isSupportSubAccount"),
            @Mapping(target = "supportSubAccountType", source = "instFundsAccountEntity.supportSubAccountType"),
            @Mapping(target = "isSupportCustomName", source = "instFundsAccountEntity.isSupportCustomName"),
            @Mapping(target = "subAccountMode", source = "instFundsAccountEntity.subAccountMode"),
            @Mapping(target = "bankName", source = "instFundsAccountEntity.bankName"),
            @Mapping(target = "bankAddress", source = "instFundsAccountEntity.bankAddress"),
            @Mapping(target = "subAccountNameWebRule", expression = "java(responseAccountDO.getInstFundsAccountEntity().getAccountJsonBo().getSubAccountNameRule())"),
            @Mapping(target = "accountExtList", source = "instFundsAccountEntity.accountExtList"),
            @Mapping(target = "subAccountId", source = "instSubFundsAccountEntity.subAccountId"),
            @Mapping(target = "subAccountNo", expression = "java(SubAccountStatusEnum.isReturnAccount(responseAccountDO.getInstSubFundsAccountEntity().getStatus()) ? responseAccountDO.getInstSubFundsAccountEntity().getSubAccountNo() : null)"),
            @Mapping(target = "BSubAccountNo", expression = "java(SubAccountStatusEnum.isReturnAccount(responseAccountDO.getInstSubFundsAccountEntity().getStatus()) ? responseAccountDO.getInstSubFundsAccountEntity().getBSubAccountNo() : null)"),
            @Mapping(target = "subAccountName", source = "instSubFundsAccountEntity.subAccountName"),
            @Mapping(target = "subAccountStatus", source = "instSubFundsAccountEntity.status"),
            @Mapping(target = "merchantNo", source = "instSubFundsAccountEntity.merchantNo"),
            @Mapping(target = "subMerchantNo", source = "instSubFundsAccountEntity.subMerchantNo"),
            @Mapping(target = "subScenes", source = "instSubFundsAccountEntity.scenes"),
    })
    @Named("toCreateSubAccountResponseDO")
    CreateSubAccountResponseDO toCreateSubAccountResponseDO(ResponseAccountDO responseAccountDO);

    /**
     * 转换成QueryAccountDetailByIdResponse
     *
     * @param queryAccountDetailByIdResponseDO
     * @return
     */
    @Named("toQueryAccountDetailByIdResponse")
    QueryAccountDetailByIdResponse toQueryAccountDetailByIdResponse(QueryAccountDetailByIdResponseDO queryAccountDetailByIdResponseDO);

    /**
     * 转换成QueryAccountDetailResponse
     *
     * @param queryAccountDetailResponseDO
     * @return
     */
    @Named("toQueryAccountDetailResponse")
    QueryAccountDetailResponse toQueryAccountDetailResponse(QueryAccountDetailResponseDO queryAccountDetailResponseDO);

    /**
     * 转换成AccountStatusChangeInfo
     *
     * @param responseAccountDO
     * @return
     */
    @Mappings({
            @Mapping(target = "accountId", source = "instFundsAccountEntity.accountId"),
            @Mapping(target = "instCode", source = "instFundsAccountEntity.instCode"),
            @Mapping(target = "entity", source = "instFundsAccountEntity.entity"),
            @Mapping(target = "country", source = "instFundsAccountEntity.country"),
            @Mapping(target = "currency", source = "instFundsAccountEntity.currency"),
            @Mapping(target = "accountNo", source = "instFundsAccountEntity.accountNo"),
            @Mapping(target = "accountType", source = "instFundsAccountEntity.accountType"),
            @Mapping(target = "scenes", source = "instFundsAccountEntity.scenes"),
            @Mapping(target = "accountName", source = "instFundsAccountEntity.accountName"),
            @Mapping(target = "isSupportSubAccount", source = "instFundsAccountEntity.isSupportSubAccount"),
            @Mapping(target = "isSupportCustomName", source = "instFundsAccountEntity.isSupportCustomName"),
            @Mapping(target = "subAccountMode", source = "instFundsAccountEntity.subAccountMode"),
            @Mapping(target = "instMid", source = "instFundsAccountEntity.instMid"),
            @Mapping(target = "bankName", source = "instFundsAccountEntity.bankName"),
            @Mapping(target = "bankAddress", source = "instFundsAccountEntity.bankAddress"),
            @Mapping(target = "subAccountId", source = "instSubFundsAccountEntity.subAccountId"),
            @Mapping(target = "subAccountNo", source = "instSubFundsAccountEntity.subAccountNo"),
            @Mapping(target = "subAccountName", source = "instSubFundsAccountEntity.subAccountName"),
            @Mapping(target = "subAccountStatus", source = "instSubFundsAccountEntity.status"),
            @Mapping(target = "businessKey", source = "instSubFundsAccountEntity.businessKey"),
            @Mapping(target = "subUseType", source = "instSubFundsAccountEntity.subUseType"),
            @Mapping(target = "merchantNo", source = "instSubFundsAccountEntity.merchantNo"),
            @Mapping(target = "subMerchantNo", source = "instSubFundsAccountEntity.subMerchantNo"),
            @Mapping(target = "subScenes", source = "instSubFundsAccountEntity.scenes"),
            @Mapping(target = "accountJson", source = "instSubFundsAccountEntity.accountJson")
    })
    AccountStatusChangeMqInfo toAccountStatusChangeInfo(ResponseAccountDO responseAccountDO);

    /**
     * 转换成List<InstFundsAccountDO>
     *
     * @param instFundsAccountEntityList
     * @return
     */
    List<QueryAccountsResponseDO> toQueryAccountsResponseDOList(List<InstFundsAccountEntity> instFundsAccountEntityList);

    /**
     * 转换成InstFundsAccountDO
     *
     * @param instFundsAccountEntity
     * @return
     */
    QueryAccountsResponseDO toQueryAccountsResponseDO(InstFundsAccountEntity instFundsAccountEntity);

    /**
     * 转换成InstFundsAccountDO
     *
     * @param instFundsAccountEntity
     * @return
     */
    @Named("toQueryAccountDetailByIdResponseDO")
    @Mappings({
            @Mapping(target = "subAccountNameWebRule", expression = "java(instFundsAccountEntity.getAccountJsonBo().getSubAccountNameRule())")
    })
    QueryAccountDetailByIdResponseDO toQueryAccountDetailByIdResponseDO(InstFundsAccountEntity instFundsAccountEntity);

    /**
     * 转换成QueryAccountDetailResponseDO
     *
     * @param accountEntity
     * @return
     */
    QueryAccountDetailResponseDO toQueryAccountDetailResponseDO(InstFundsAccountEntity accountEntity);

    /**
     * List<QueryInstBrandResponseDO>转List<QueryInstBrandResponse>
     *
     * @param queryInstBrandResponseDOList
     * @return
     */
    List<QueryInstBrandResponse> toQueryInstBrandResponseList(List<QueryInstBrandResponseDO> queryInstBrandResponseDOList);

    /**
     * List<InstBrandEntity>转List<QueryInstBrandResponseDO>
     *
     * @param instBrandEntityList
     * @return
     */
    List<QueryInstBrandResponseDO> toQueryInstBrandResponseDOList(List<InstBrandEntity> instBrandEntityList);


    List<InstBankAccountResponse> toQueryInstBankAccountResponseDOList(List<InstBankAccountVO> instBankAccountVOList);


    InstBankAccountResponse instBankAccountVOToInstBankAccountResponse(InstBankAccountVO instBankAccountVO);

    /**
     * List<InstFundsAccountEntity>转List<QuerySubFundsAccountResponseDO>
     *
     * @param accountEntitys
     * @return
     */
    List<QuerySubFundsAccountResponseDO> toQuerySubFundsAccountResponseDOs(List<InstFundsAccountEntity> accountEntitys);

    /**
     * InstFundsAccountEntity转QuerySubFundsAccountResponseDO
     *
     * @param accountEntity
     * @return
     */
    QuerySubFundsAccountResponseDO toQuerySubFundsAccountResponseDO(InstFundsAccountEntity accountEntity);

    /**
     * InstFundsAccountEntity、InstSubFundsAccountEntity转QuerySubAccountDetailByIdResponseDO
     *
     * @param accountEntity
     * @param subAccountEntity
     * @return
     */
    @Mappings({
            @Mapping(target = "accountId", source = "accountEntity.accountId"),
            @Mapping(target = "scenes", source = "accountEntity.scenes"),
            @Mapping(target = "subAccountId", source = "subAccountEntity.subAccountId"),
            @Mapping(target = "subAccountNo", source = "subAccountEntity.subAccountNo"),
            @Mapping(target = "BSubAccountNo", source = "subAccountEntity.BSubAccountNo"),
            @Mapping(target = "subAccountName", source = "subAccountEntity.subAccountName"),
            @Mapping(target = "subAccountStatus", source = "subAccountEntity.status"),
            @Mapping(target = "numberSegmentNo", source = "subAccountEntity.numberSegmentNo"),
            @Mapping(target = "businessKey", source = "subAccountEntity.businessKey"),
            @Mapping(target = "subUseType", source = "subAccountEntity.subUseType"),
            @Mapping(target = "merchantNo", source = "subAccountEntity.merchantNo"),
            @Mapping(target = "subMerchantNo", source = "subAccountEntity.subMerchantNo"),
            @Mapping(target = "subScenes", source = "subAccountEntity.scenes"),
            @Mapping(target = "subUtcCreate", source = "subAccountEntity.utcCreate"),
            @Mapping(target = "subAccountNameWebRule", expression = "java(accountEntity.getAccountJsonBo().getSubAccountNameRule())")
    })
    QuerySubAccountDetailByIdResponseDO toQuerySubAccountDetailByIdResponseDO(InstFundsAccountEntity accountEntity, InstSubFundsAccountEntity subAccountEntity);

    /**
     * QuerySubAccountDetailByIdResponseDO转QuerySubAccountDetailByIdResponse
     *
     * @param responseDO
     * @return
     */
    QuerySubAccountDetailByIdResponse toQuerySubAccountDetailByIdResponse(QuerySubAccountDetailByIdResponseDO responseDO);

    /**
     * CloseSubAccountResponseDO转CloseSubAccountResponse
     *
     * @param closeSubAccountResponseDO
     * @return
     */
    CloseSubAccountResponse toCloseSubAccountResponse(CloseSubAccountResponseDO closeSubAccountResponseDO);

    /**
     * 转换成CloseSubAccountResponseDO
     *
     * @param accountEntity
     * @return
     */
    CloseSubAccountResponseDO toCloseSubAccountResponseDO(InstFundsAccountEntity accountEntity);

    /**
     * 转 InstBaseInfoWithBrandResponse
     */
    @Mapping(target = "instEntityCountry", source = "baseInfo.entityCountry")
    void toInstBaseInfoWithBrandResponse(@MappingTarget InstBaseInfoWithBrandResponse response, ChannelInfoEntity channelInfo, InstBaseInfoEntity baseInfo, InstBrandPO brand);
}
