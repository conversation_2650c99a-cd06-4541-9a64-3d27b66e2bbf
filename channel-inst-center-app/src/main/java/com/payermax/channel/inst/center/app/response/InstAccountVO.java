package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构账号 VO
 *
 * <AUTHOR>
 * @date 2022/6/4 14:43
 */
@Data
public class InstAccountVO implements Serializable {

    private static final long serialVersionUID = -6193931389343772541L;

    @ApiModelProperty(notes = "主键")
    private Long id;

    @ApiModelProperty(notes = "机构标识")
    private Long instId;

    @ApiModelProperty(notes = "集成需求单ID")
    private Long requirementOrderId;

    @ApiModelProperty(notes = "账号类型")
    private String type;

    @ApiModelProperty(notes = "账号环境")
    private String env;

    @ApiModelProperty(notes = "账号")
    private String account;

    @ApiModelProperty(notes = "账号密钥类型")
    private String keyType;

    @ApiModelProperty(notes = "账号密钥值")
    private String keyValue;

    @ApiModelProperty(notes = "加密方式")
    private String encryptType;

    @ApiModelProperty(notes = "备注")
    private String remark;

    @ApiModelProperty(hidden = true)
    private String status;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

}
