package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/6/28 19:02
 */
@Data
public class InstRequirementScheduleQueryVO extends InstRequirementScheduleVO {

    private String applyNo;

    private String shareitEntity;

    private Long instId;

    private String instCode;

    private String instName;

    private String bdId;

    private String amId;

    private String applyPriority;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date expectReleaseTime;

    private List<InstProductCapabilityVO> productCapabilityList;

}
