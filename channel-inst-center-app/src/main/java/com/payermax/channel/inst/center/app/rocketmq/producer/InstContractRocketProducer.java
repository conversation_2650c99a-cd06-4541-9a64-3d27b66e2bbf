package com.payermax.channel.inst.center.app.rocketmq.producer;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.response.InstContractFeeItemModifiedNotifyDTO;
import com.payermax.channel.inst.center.app.response.InstContractVersionUpgradeNotifyDto;
import com.payermax.channel.inst.center.common.constants.RocketMqConstant;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import com.payermax.infra.ionia.rocketmq.handler.IoniaRocketMqTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/26
 * @DESC
 */
@Slf4j
@Component
public class InstContractRocketProducer {


    @NacosValue("${spring.application.name}")
    private String appName;

    @NacosValue(value = "${rocket.topic.inst-contract.fee-item.modified.notify:}", autoRefreshed = true)
    private String INST_CONTRACT_FEE_ITEM_MODIFIED_TOPIC;


    @Autowired
    private IoniaRocketMqTemplate ioniaRocketMqTemplate;



    /**
     * 发送费用信息变更通知
     */
    public SendResult sendFeeItemFeeConfigModifiedNotify(InstContractFeeItemModifiedNotifyDTO notify) {
        return sendMessage(INST_CONTRACT_FEE_ITEM_MODIFIED_TOPIC, notify, notify.getInstContractFeeItemNo());
    }

    /**
     * 发送版本升级通知
     */
    public SendResult sendContractVersionUpgradeNotify(InstContractVersionUpgradeNotifyDto notify) {
        log.info("发送机构合约升级成功 MQ 消息");
        SendResult sendResult = sendMessage(RocketMqConstant.TOPIC_INST_CENTER_CONTRACT_VERSION_UPGRADE, notify, notify.getContractNo());
        log.info("sendResult:{},{}",sendResult.getSendStatus(),sendResult.getMsgId());
        return sendResult;
    }


    /**
     * 同步发送消息
     */
    public <T extends BaseMqMessage> SendResult sendMessage(String topic, T data, String key) {
        data.setKey(key);
        data.setSendTime(new Date());
        data.setSource(appName);
        String traceId = TraceContext.traceId();
        if (StringUtils.isNotBlank(traceId)) {
            data.setTraceId(TraceContext.traceId());
        }
        return ioniaRocketMqTemplate.sendMsgSync(topic, data);
    }
}
