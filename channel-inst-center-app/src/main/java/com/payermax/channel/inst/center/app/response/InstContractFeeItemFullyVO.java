package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.infrastructure.repository.po.InstContractFeeItemPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/17
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InstContractFeeItemFullyVO extends InstContractFeeItemPO implements Serializable {

    /**
     * 合同、产品信息
     */
    @ApiModelProperty(notes = "合同编号")
    private String contractNo;

    @ApiModelProperty(notes = "我司主体")
    private String contractEntity;

    @ApiModelProperty(notes = "机构标识")
    private String instCode;

    @ApiModelProperty(notes = "机构产品类型")
    private String instProductType;

    @ApiModelProperty(notes = "机构产品名称")
    private String instProductName;

    @ApiModelProperty(notes = "生效时间")
    private Date effectiveDate;

    @ApiModelProperty(notes = "支付方式、目标机构、卡组")
    private List<InstContractBaseResponse.StandardProductMsg> standardProductMsgList;


}
