package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstAccountKeyVO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/28 20:44
 */
@Data
public class InstAccountKeyVO implements Serializable {
    private Long keyId;

    private Long accountId;

    private String keyType;

    private String keyValue;

    private String encryptType;

    private String remark;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}
