package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstContactVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 21:17
 * @Version 1.0
 */
@Data
public class InstContactVO implements Serializable {

    private static final long serialVersionUID = 3604614635464519348L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 联系人姓名
     */
    private String name;

    /**
     * 联系人职能
     */
    private String position;

    /**
     * 职能-中文
     */
    private String positionZhName;

    /**
     * 职能-英文
     */
    private String positionEnName;

    /**
     * 联系人头衔
     */
    private String title;

    /**
     * 头衔-中文
     */
    private String titleZhName;

    /**
     * 头衔-英文
     */
    private String titleEnName;

    /**
     * 联系人邮箱
     */
    private String email;

    /**
     * 联系人电话
     */
    private String phoneNo;

    /**
     * 联系人IM
     */
    private String imNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态
     */
    private String status;
}
