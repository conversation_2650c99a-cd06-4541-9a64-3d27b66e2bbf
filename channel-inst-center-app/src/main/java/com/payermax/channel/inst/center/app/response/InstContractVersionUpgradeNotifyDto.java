package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.domain.enums.contract.management.ContractBusinessTypeEnum;
import com.payermax.channel.inst.center.domain.enums.contract.management.ContractStatusEnum;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/3/24
 * @DESC
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InstContractVersionUpgradeNotifyDto extends BaseMqMessage {

    private String contractNo;

    private String instCode;

    private String contractEntity;

    private ContractBusinessTypeEnum instProductType;

    private ContractStatusEnum status;
}
