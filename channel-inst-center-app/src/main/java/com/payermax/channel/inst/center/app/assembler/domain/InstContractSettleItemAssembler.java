package com.payermax.channel.inst.center.app.assembler.domain;

import com.payermax.channel.inst.center.app.dto.InstContractSettleRowItemDTO;
import com.payermax.channel.inst.center.common.utils.ConvertUtils;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstFeeConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettleDateConfig;
import com.payermax.channel.inst.center.domain.entity.contract.content.InstSettlePaymentConfig;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractSettlementItem;
import com.payermax.channel.inst.center.domain.enums.contract.content.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;
import java.util.List;

@Mapper(componentModel = "spring", imports = {FeeTypeEnum.class, FeeCalculateTypeEnum.class, FeeCalculateBaseModeEnum.class,
        TaxCalculateFormulaEnum.class, CurrencyExchangeTimingEnum.class, RoundingModeEnum.class,
        BigDecimal.class, ConvertUtils.class, InstFeeConfig.FeeStepCombineConfig.class, List.class, WithdrawMethodEnum.class})
public interface InstContractSettleItemAssembler {


    /**
     * 构造 结算条款 - 结算合同基础信息
     */
    @Mappings({
            @Mapping(target = "mccLogic", expression = "java(ConvertUtils.processOnLogicKeyEnum(dto.getStandardMcc()))"),
            @Mapping(target = "standardMcc", expression = "java(ConvertUtils.processExceptLogicKeyEnum(dto.getStandardMcc()))"),
    })
    InstContractSettlementItem convertContractSettleItemFromExcel(InstContractSettleRowItemDTO dto);

    /**
     * 构造 结算条款 - 结算打款基本信息
     */
    @Mappings({
            @Mapping(target = "withdrawMethod", expression = "java(ConvertUtils.getEnumByDesc(WithdrawMethodEnum.class, dto.getWithdrawMethod()))"),
            @Mapping(target = "minSettleAmount", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getMinSettleAmount()))"),
    })
    InstSettlePaymentConfig convertInstSettlePaymentInfoFromExcel(InstContractSettleRowItemDTO dto);

    /**
     * 构造 结算条款 - 结算周期列表信息
     */
    List<InstSettleDateConfig> convertInstSettleRoundFromExcel(List<InstContractSettleRowItemDTO.SettleDateInfo> settleDateInfoList);

    /**
     * 结算周期单笔信息转换
     */
    InstSettleDateConfig convertInstSingleSettleRoundFromExcel(InstContractSettleRowItemDTO.SettleDateInfo settleDateInfo);

    /**
     * 构造 结算条款 - 结算费用信息
     */
    @Mappings({
            @Mapping(target = "feeType", expression = "java(FeeTypeEnum.SETTLEMENT)"),
            @Mapping(target = "calculateType", expression = "java(ConvertUtils.getEnumByDesc(FeeCalculateTypeEnum.class, dto.getCalculateType()))"),
            @Mapping(target = "feeRateValue", expression = "java(ConvertUtils.convertPercentBigDecimal(dto.getSingleRate()))"),
            @Mapping(target = "feeValue", expression = "java(ConvertUtils.convertRawBigDecimal(dto.getSingleFixedAmount()))"),
            @Mapping(target = "feeCurrency", expression = "java(dto.getSingleFixedCurrency())")
    })
    InstFeeConfig convertInstSettleFeeFromExcel(InstContractSettleRowItemDTO dto);


}