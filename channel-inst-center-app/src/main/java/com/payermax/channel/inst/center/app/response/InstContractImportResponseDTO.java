package com.payermax.channel.inst.center.app.response;

import com.payermax.channel.inst.center.common.model.ExportErrorInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * InstContractInitResponseDTO
 *
 * <AUTHOR>
 * @desc
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InstContractImportResponseDTO {

    /**
     * 总的错误提示信息
     */
    private List<String> errorMsgList;

    /**
     * 费用导入错误信息
     */
    private ExportErrorInfo feeErrorInfo;

    /**
     * 结算条款导入错误信息
     */
    private ExportErrorInfo settleErrorInfo;

    /**
     * 累计导入错误信息
     */
    private ExportErrorInfo accumulateErrorInfo;

    public boolean isHasErrors() {
        if(!CollectionUtils.isEmpty(getErrorMsgList())) {
            return true;
        }
        if(Objects.nonNull(getSettleErrorInfo()) && getSettleErrorInfo().isHasErrors()) {
            return true;
        }
        if(Objects.nonNull(getFeeErrorInfo()) && getFeeErrorInfo().isHasErrors()) {
            return true;
        }
        if(Objects.nonNull(getAccumulateErrorInfo()) && getAccumulateErrorInfo().isHasErrors()) {
            return true;
        }
        return false;
    }

    public static InstContractImportResponseDTO merge(List<InstContractImportResponseDTO> list) {
        InstContractImportResponseDTO result = new InstContractImportResponseDTO();
        if(CollectionUtils.isEmpty(list)) {
            return result;
        }
        result.setErrorMsgList(new ArrayList<>());
        list.forEach(item -> {
            if(!CollectionUtils.isEmpty(item.getErrorMsgList())) {
                result.getErrorMsgList().addAll(item.getErrorMsgList());
            }
            result.setSettleErrorInfo(ExportErrorInfo.merge(result.getSettleErrorInfo(), item.getSettleErrorInfo()));
            result.setFeeErrorInfo(ExportErrorInfo.merge(result.getFeeErrorInfo(), item.getFeeErrorInfo()));
            result.setAccumulateErrorInfo(ExportErrorInfo.merge(result.getAccumulateErrorInfo(), item.getAccumulateErrorInfo()));
        });
        return result;
    }
}
