package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstContractVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/26 0:07
 */
@Data
public class InstContractVO implements Serializable {
    private String contractNo;

    private String realContractNo;

    private Long instId;

    private String applyNo;

    private String fundsSettleInst;

    private String servicePurchaser;

    private String serviceProvider;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date startDate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date endDate;

    private String signFlag;

    private String contractAttachId;

    private String remark;

    private String status;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;
}
