package com.payermax.channel.inst.center.app.assembler;

import cn.hutool.core.date.DateUtil;
import com.payermax.channel.inst.center.app.request.InstBankAccountReqDTO;
import com.payermax.channel.inst.center.domain.subaccount.request.*;
import com.payermax.channel.inst.center.domain.subaccount.response.FileAccountStatusChangeMqInfo;
import com.payermax.channel.inst.center.facade.enums.SubAccountStatusEnum;
import com.payermax.channel.inst.center.facade.enums.UseTypeEnum;
import com.payermax.channel.inst.center.facade.request.*;
import com.payermax.channel.inst.center.facade.response.InstFundsAccountResponse;
import com.payermax.channel.inst.center.facade.response.QueryAccountByChannelMerchantCodeResponse;
import com.payermax.channel.inst.center.infrastructure.entity.InstBrandEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountBucketEntity;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountAndSubQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstFundsAccountQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubAccountBathQueryEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstSubFundsAccountQueryEntity;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.*;

/**
 * 请求转换
 *
 * <AUTHOR>
 * @date 2022/10/10 21:11
 */
@Mapper(componentModel = "spring", imports = {UseTypeEnum.class, DateUtil.class, Date.class, ArrayList.class, SubAccountStatusEnum.class})
public interface ReqDoAssembler {

    @Mapping(target = "accountUse", source = "useType")
    InstBankAccountReqDTO toInstBankAccountRequest(InstBankAccountRequest req);

    /**
     * 转换成QueryAccountsRequestDO
     *
     * @param request
     * @return
     */
    @Mapping(target = "useType", expression = "java(request.getUseType().name())")
    QueryAccountsRequestDO toInstFundsAccountRequestDO(QueryAccountsRequest request);

    /**
     * 转换成InstFundsAccountEntity
     *
     * @param request
     * @return
     */
    @Mappings({
            @Mapping(target = "useType", expression = "java(request.getUseType().name())")
    })
    CreateSubAccountRequestDO toCreateSubAccountRequestDO(CreateSubAccountRequest request);

    /**
     * 转换成QueryAccountDetailRequestDO
     *
     * @param request
     * @return
     */
    QueryAccountDetailRequestDO toQueryAccountDetailRequestDO(QueryAccountDetailRequest request);

    /**
     * 转换成QueryAccountDetailRequestDO
     *
     * @param request
     * @return
     */
    QueryAccountDetailRequestDO toQueryAccountDetailRequestDO(QueryAccountDetailInfoRequest request);


    /**
     * 转换成QueryAccountDetailByIdRequestDO
     *
     * @param instSubFundsAccountEntity
     * @return
     */
    QuerySubAccountDetailByIdRequestDO toQuerySubAccountDetailByIdRequestDO(InstSubFundsAccountEntity instSubFundsAccountEntity);

    /**
     * 转换成QuerySubAccountDetailByIdRequestDO
     *
     * @param request
     * @return
     */
    QuerySubAccountDetailByIdRequestDO toQuerySubAccountDetailByIdRequestDO(QuerySubAccountDetailByIdRequest request);

    /**
     * 转换成QueryAccountDetailByIdRequestDO
     *
     * @param request
     * @return
     */
    QueryAccountDetailByIdRequestDO toQueryAccountDetailByIdRequestDO(QueryAccountDetailByIdRequest request);

    /**
     * 转换成List<String>
     *
     * @param country
     * @return
     */
    default List<String> countryToList(String country) {
        if (StringUtils.isNotBlank(country)) {
            List<String> list = new ArrayList<>(1);
            list.add(country);
            return list;
        }
        return null;
    }

    /**
     * 转换成InstFundsAccountQueryEntity
     *
     * @param instFundsAccountQueryDO
     * @return
     */
    InstFundsAccountQueryEntity toInstFundsAccountQueryEntity(QueryAccountsRequestDO instFundsAccountQueryDO);

    /**
     * 转换成InstFundsAccountQueryEntity
     *
     * @param createSubAccountRequestDO
     * @return
     */
    @Mappings({
            @Mapping(target = "country", expression = "java(countryToList(createSubAccountRequestDO.getCountry()))")
    })
    InstFundsAccountQueryEntity toInstFundsAccountQueryEntity(CreateSubAccountRequestDO createSubAccountRequestDO);

    /**
     * 转换成InstFundsAccountQueryEntity
     *
     * @param queryAccountDetailRequestDO
     * @return
     */
    InstFundsAccountQueryEntity toInstFundsAccountQueryEntity(QueryAccountDetailRequestDO queryAccountDetailRequestDO);

    /**
     * 转换成InstFundsAccountQueryEntity
     *
     * @param queryAccountDetailByIdRequestDO
     * @return
     */
    InstFundsAccountQueryEntity toInstFundsAccountQueryEntity(QueryAccountDetailByIdRequestDO queryAccountDetailByIdRequestDO);
    
    /**
     * request 转换到 Entity
     */
    InstFundsAccountQueryEntity request2InstFundsAccountEntity(InstFundsAccountRequest instFundsAccountRequest);


    InstFundsAccountResponse instFundsAccount2ResponseEntity(InstFundsAccountEntity account);


    List<InstFundsAccountResponse> instFundsAccount2ResponseEntityList(List<InstFundsAccountEntity> instFundsAccountEntities);


    /**
     * 转换成InstSubFundsAccountQueryEntity
     *
     * @param queryAccountDetailRequestDO
     * @return
     */
    InstSubFundsAccountQueryEntity toInstSubFundsAccountQueryEntity(QueryAccountDetailRequestDO queryAccountDetailRequestDO);

    /**
     * 转换成InstSubFundsAccountQueryEntity
     *
     * @param querySubAccountDetailByIdRequestDO
     * @return
     */
    InstSubFundsAccountQueryEntity toInstSubFundsAccountQueryEntity(QuerySubAccountDetailByIdRequestDO querySubAccountDetailByIdRequestDO);

    /**
     * 转换成InstSubFundsAccountQueryEntity
     *
     * @param queryAccountDetailByIdRequestDO
     * @return
     */
    @Mappings({
            @Mapping(target = "subAccountId", source = "accountId"),
            @Mapping(target = "accountId", ignore = true)
    })
    InstSubFundsAccountQueryEntity toInstSubFundsAccountQueryEntity(QueryAccountDetailByIdRequestDO queryAccountDetailByIdRequestDO);

    /**
     * 转换成InstSubFundsAccountRequestDO
     *
     * @param createSubAccountRequestDO
     * @return
     */
    @Mappings({
            @Mapping(target = "subUseType", expression = "java(createSubAccountRequestDO.getUseType())")
    })
    InstSubFundsAccountRequestDO toInstSubFundsAccountRequestDO(CreateSubAccountRequestDO createSubAccountRequestDO);

    /**
     * 转换成InstSubFundsAccountRequestDO
     *
     * @param queryAccountsRequestDO
     * @return
     */
    @Mappings({
            @Mapping(target = "subUseType", expression = "java(queryAccountsRequestDO.getUseType())")
    })
    InstSubFundsAccountRequestDO toInstSubFundsAccountRequestDO(QueryAccountsRequestDO queryAccountsRequestDO);

    /**
     * 转换成InstSubFundsAccountRequestDO
     *
     * @param instSubFundsAccountEntity
     * @return
     */
    InstSubFundsAccountRequestDO toInstSubFundsAccountRequestDO(InstSubFundsAccountEntity instSubFundsAccountEntity);

    /**
     * 转换成InstSubFundsAccountEntity
     *
     * @param request
     * @return
     */
    @Mappings({
            @Mapping(target = "utcCreate", expression = "java(DateUtil.offsetHour(new Date(),-request.getRecentHour()))")
    })
    InstSubAccountBathQueryEntity toInstSubAccountBathQueryEntity(PatchSubAccountCompensateTaskRequestDO request);

    /**
     * 转换成InstSubFundsAccountEntity
     *
     * @param instSubFundsAccountDO
     * @return
     */
    InstSubFundsAccountEntity toInstSubFundsAccountEntity(InstSubFundsAccountRequestDO instSubFundsAccountDO);

    /**
     * 转换成InstSubFundsAccountEntity
     *
     * @param instSubFundsAccountBucketEntity
     * @return
     */
    @Mappings({
            @Mapping(target = "status", ignore = true)
    })
    InstSubFundsAccountEntity toInstSubFundsAccountEntity(InstSubFundsAccountBucketEntity instSubFundsAccountBucketEntity);

    /**
     * 转换成InstSubFundsAccountBucketEntity
     *
     * @param instSubFundsAccountEntity
     * @return
     */
    @Mappings({
            @Mapping(target = "status", constant = "Y"),
            @Mapping(target = "subAccountStatus", source = "status")
    })
    InstSubFundsAccountBucketEntity toInstSubFundsAccountBucketEntity(InstSubFundsAccountEntity instSubFundsAccountEntity);


    /**
     * QueryInstBrandRequest转换成QueryInstBrandRequestDO
     * @param queryInstBrandRequest
     * @return
     */
    QueryInstBrandRequestDO toQueryInstBrandRequestDO(QueryInstBrandRequest queryInstBrandRequest);

    /**
     * QueryInstBrandRequestDO转InstBrandEntity
     * @param queryInstBrandRequestDO
     * @return
     */
    InstBrandEntity toInstBrandEntity(QueryInstBrandRequestDO queryInstBrandRequestDO);
    
    /**
     * QuerySubFundsAccountRequestDO转换成InstFundsAccountAndSubQueryEntity
     * @param queryAccountDetailRequestDO
     * @return
     */
    @Mappings({
            @Mapping(target = "isSupportSubAccount", source = "isSupportSubAccount", defaultValue = "Y")
    })
    InstFundsAccountAndSubQueryEntity toInstFundsAccountAndSubQueryEntity(QuerySubFundsAccountRequestDO queryAccountDetailRequestDO);

    /**
     * FileAccountStatusChangeMqInfo转QuerySubFundsAccountRequestDO
     *
     * @param accountStatusChangeMq
     * @return
     */
    @Mappings({
            @Mapping(target = "isSupportSubAccount", source = "isSupportSubAccount", defaultValue = "Y"),
            @Mapping(target = "subAccountQuery.status", source = "subAccountQuery.status", defaultValue = "4"),
    })
    QuerySubFundsAccountRequestDO toQuerySubFundsAccountRequestDO(FileAccountStatusChangeMqInfo.AccountStatusChangeMq accountStatusChangeMq);
    
    /**
     * InstFundsAccountEntity\InstSubFundsAccountEntity转PatchInstSubFundsAccountRequestDO
     * @param instFundsAccountEntity
     * @param instSubFundsAccountEntity
     * @return PatchInstSubFundsAccountRequestDO
     */
    @Mappings({
            @Mapping(target = "accountIds", expression = "java(countryToList(instFundsAccountEntity.getAccountId()))"),
            @Mapping(target = "accountId", source = "instFundsAccountEntity.accountId"),
            @Mapping(target = "instCode", source = "instFundsAccountEntity.instCode"),
            @Mapping(target = "entity", source = "instFundsAccountEntity.entity"),
            @Mapping(target = "accountNo", source = "instFundsAccountEntity.accountNo"),
            @Mapping(target = "accountType", source = "instFundsAccountEntity.accountType"),
            @Mapping(target = "accountName", source = "instFundsAccountEntity.accountName"),
            @Mapping(target = "useType", source = "instFundsAccountEntity.useType"),
            @Mapping(target = "scenes", source = "instFundsAccountEntity.scenes"),
            @Mapping(target = "country", source = "instFundsAccountEntity.country"),
            @Mapping(target = "currency", source = "instFundsAccountEntity.currency"),
            @Mapping(target = "iban", source = "instFundsAccountEntity.iban"),
            @Mapping(target = "status", source = "instFundsAccountEntity.status"),
            @Mapping(target = "accountJson", ignore = true),
            @Mapping(target = "accountJsonBo", ignore = true),
            @Mapping(target = "utcCreate", ignore = true),
            @Mapping(target = "utcModified", ignore = true),
            @Mapping(target = "accountAlias", source = "instFundsAccountEntity.accountAlias"),
            @Mapping(target = "subAccountQuery.subAccountId", source = "instSubFundsAccountEntity.subAccountId"),
            @Mapping(target = "subAccountQuery.businessKey", source = "instSubFundsAccountEntity.businessKey"),
            @Mapping(target = "subAccountQuery.numberSegmentNo", source = "instSubFundsAccountEntity.numberSegmentNo"),
            @Mapping(target = "subAccountQuery.subUseType", source = "instSubFundsAccountEntity.subUseType"),
            @Mapping(target = "subAccountQuery.subAccountNo", source = "instSubFundsAccountEntity.subAccountNo"),
            @Mapping(target = "subAccountQuery.subAccountName", source = "instSubFundsAccountEntity.subAccountName"),
            @Mapping(target = "subAccountQuery.BSubAccountNo", source = "instSubFundsAccountEntity.BSubAccountNo"),
            @Mapping(target = "subAccountQuery.merchantNo", source = "instSubFundsAccountEntity.merchantNo"),
            @Mapping(target = "subAccountQuery.subMerchantNo", source = "instSubFundsAccountEntity.subMerchantNo"),
            @Mapping(target = "subAccountQuery.scenes", source = "instSubFundsAccountEntity.scenes"),
            @Mapping(target = "subAccountQuery.accountJsonBo", ignore = true),

    })
    QuerySubFundsAccountRequestDO toQuerySubFundsAccountRequestDO(InstFundsAccountEntity instFundsAccountEntity, InstSubFundsAccountEntity instSubFundsAccountEntity);


    default QueryAccountByChannelMerchantCodeResponse composeChannelMerchantCodeAccountsMappingResp(Map<String, List<InstFundsAccountEntity>> boResultMap) {
        Map<String, List<InstFundsAccountResponse>> voResultMap = new HashMap<>();

        for (Map.Entry<String, List<InstFundsAccountEntity>> entry : boResultMap.entrySet()) {
            voResultMap.put(entry.getKey(), instFundsAccount2ResponseEntityList(entry.getValue()));
        }

        QueryAccountByChannelMerchantCodeResponse res = new QueryAccountByChannelMerchantCodeResponse();
        res.setChannelMerchantCodeAccountsMap(voResultMap);

        return res;
    }
}
