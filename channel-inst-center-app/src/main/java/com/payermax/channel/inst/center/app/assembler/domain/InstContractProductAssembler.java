package com.payermax.channel.inst.center.app.assembler.domain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.payermax.channel.inst.center.app.dto.contract.InstContractFxBatchSyncDto;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractBaseFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractFeeFormMsgDTO;
import com.payermax.channel.inst.center.app.dto.workflow.InstContractSettleFormMsgDTO;
import com.payermax.channel.inst.center.app.request.InstContractFeeItemRequestDTO;
import com.payermax.channel.inst.center.app.request.InstContractFxBatchModifiedReqDTO;
import com.payermax.channel.inst.center.app.response.*;
import com.payermax.channel.inst.center.common.enums.InstProcessStatusEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.BusinessTypeEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateModuleEnum;
import com.payermax.channel.inst.center.common.enums.operatelog.OperateTypeEnum;
import com.payermax.channel.inst.center.domain.entity.contract.management.InstContractFeeItem;
import com.payermax.channel.inst.center.facade.request.contract.InstContractFxBatchSyncRequest;
import com.payermax.channel.inst.center.facade.request.contract.config.FeeConfig;
import com.payermax.channel.inst.center.facade.request.contract.config.TaxConfig;
import com.payermax.channel.inst.center.infrastructure.repository.po.*;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/9
 * @DESC
 */
@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        imports = {JSON.class, Optional.class, FeeConfig.class, TaxConfig.class, StringUtils.class, BigDecimal.class, EnumUtils.class, SerializerFeature.class,
                BusinessTypeEnum.class, OperateModuleEnum.class, OperateTypeEnum.class,
                InstProcessStatusEnum.class,
        })
public interface InstContractProductAssembler {


    /**
     * 全量费用信息转 VO 脱敏
     * @param feeItemFullyVo 费用信息
     * @return 脱敏费用信息
     */
    InstFeeQueryVO feeFullyVo2QueryVo(InstContractFeeItemFullyVO feeItemFullyVo);

    /**
     * 费用信息转 VO，未处理合同、产品信息
     * @param instContractFeeItemPo 费用信息
     * @return 费用信息
     */
    @Named("feeItem2FullyVO")
    InstContractFeeItemFullyVO feeItem2FullyVO(InstContractFeeItemPO instContractFeeItemPo);

    @IterableMapping(qualifiedByName = "feeItem2FullyVO")
    List<InstContractFeeItemFullyVO> feeItem2FullyVOList(List<InstContractFeeItemPO> feeItemList);

    /**
     * 费用信息转 FX VO，未填充合同、产品信息
     * @param feeItem 费用信息
     * @return FX信息
     */
    @Mappings({
            @Mapping(target = "instCode", source = "contractBaseInfo.instCode"),
            @Mapping(target = "contractEntity", source = "contractBaseInfo.contractEntity"),
            @Mapping(target = "instProductType", source = "contractBaseInfo.instProductType"),
            @Mapping(target = "fxConfig.contractFxSpread", source = "feeItem.contractFxSpread"),
            @Mapping(target = "fxConfig.fxSpread", source = "feeItem.fxSpread"),
            @Mapping(target = "fxConfig.currencyExchangeTime", source = "feeItem.currencyExchangeTime"),
    })
    InstContractFxQueryVO.FxItem feeItem2FxQueryVO(InstContractFeeItemPO feeItem, InstContractBaseInfoPO contractBaseInfo);

    /**
     * 费用信息 VO 添加合同、产品信息
     * @param vo 费用 VO
     * @param originProduct 原始产品
     * @param contractBaseInfo 合同
     * @param versionInfoPo 合同版本
     */
    @Mappings({
            @Mapping(target = "instOriginProductNo", source = "originProduct.instOriginProductNo"),
            @Mapping(target = "contractNo", source = "originProduct.contractNo"),
            @Mapping(target = "instProductName", source = "originProduct.instProductName"),
            @Mapping(target = "instCode", source = "contractBaseInfo.instCode"),
            @Mapping(target = "instProductType", source = "contractBaseInfo.instProductType"),
            @Mapping(target = "contractEntity", source = "contractBaseInfo.contractEntity"),
            @Mapping(target = "effectiveDate", source = "versionInfoPo.effectStartTime"),
    })
    void feeFullyVoAddContractMsg(@MappingTarget InstContractFeeItemFullyVO vo, InstContractOriginProductPO originProduct, InstContractBaseInfoPO contractBaseInfo, InstContractVersionInfoPO versionInfoPo);

    /**
     * 全量费用信息入参转 PO
     * @param request 费用信息
     * @return 费用信息
     */
    InstContractFeeItemPO feeRequest2Po(InstContractFeeItemRequestDTO request);



    @Mappings({
            @Mapping(target = "instOriginProductNo", source = "originProduct.instOriginProductNo"),
            @Mapping(target = "contractNo", source = "originProduct.contractNo"),
            @Mapping(target = "instProductName", source = "originProduct.instProductName"),
            @Mapping(target = "payCurrency", source = "settleItem.payCurrency"),
            @Mapping(target = "instCode", source = "contractBaseInfo.instCode"),
            @Mapping(target = "instProductType", source = "contractBaseInfo.instProductType"),
            @Mapping(target = "contractEntity", source = "contractBaseInfo.contractEntity"),
            @Mapping(target = "effectiveDate", source = "versionInfoPO.effectStartTime"),
    })
    InstFeeQueryVO settleItem2QueryVO(InstContractSettlementItemPO settleItem, InstContractOriginProductPO originProduct,  InstContractBaseInfoPO contractBaseInfo, InstContractVersionInfoPO versionInfoPO);


    InstContractFeeItemVO instContractFeeItemPO2VO(InstContractFeeItemPO instContractFeeItemPO);

    @Mappings({
            @Mapping(target = "fxSpread", source = "fxSpread"),
            @Mapping(target = "currencyExchangeTime", source = "currencyExchangeTime"),
    })
    InstContractFeeItemVO.FxConfig instContractFeeItem2FXConfig(InstContractFeeItemPO instContractFeeItemPO);

    InstFeeQueryVO.StandardProductMsg standardProductPO2QueryMsg(InstContractStandardProductPO standardProduct);


    @Mappings({
            @Mapping(target = "settleConfig.settleCurrency", source = "settleCurrency"),
            @Mapping(target = "settleConfig.channelMerchantNo", source = "channelMerchantNo"),
            @Mapping(target = "settleFeeConfig", expression = "java(JSON.parseObject(settlementItemPO.getSettleFeeConfig(), InstContractFeeItemVO.FeeConfig.class))"),
            @Mapping(target = "settleDateConfigs", expression = "java(JSON.parseArray(settlementItemPO.getSettleDateConfig(), InstContractSettlementVO.InstSettleDateConfig.class))"),
            @Mapping(target = "settlePaymentConfig", expression = "java(JSON.parseObject(settlementItemPO.getSettlePaymentConfig(), InstContractSettlementVO.InstSettlePaymentConfig.class))")
    })
    InstContractSettlementVO settleItem2VO(InstContractSettlementItemPO settlementItemPO);


    @Mappings({
            @Mapping(target = "instCode", source = "originProduct.contractBaseInfo.instCode"),
            @Mapping(target = "contractEntity", source = "originProduct.contractBaseInfo.contractEntity"),
            @Mapping(target = "instProductType", source = "originProduct.contractBaseInfo.instProductType"),
    } )
    InstContractProductQueryVO originProductPO2VO(InstContractOriginProductPO originProduct);

    /**
     * 结算信息 VO 转 PO，用于批量修改
     */
    @Mappings({
            @Mapping(target = "settleCurrency", source = "vo.settleConfig.settleCurrency"),
            @Mapping(target = "channelMerchantNo", source = "vo.settleConfig.channelMerchantNo"),
            @Mapping(target = "settleFeeConfig", ignore = true),
            @Mapping(target = "settleDateConfig", ignore = true),
            @Mapping(target = "settlePaymentConfig", ignore = true ),
    })
    void settleVO2PO(@MappingTarget InstContractSettlementItemPO po, InstContractSettlementVO vo) ;

    /**
     * settle 对象深拷贝
     * @param po settle 对象
     * @return settle 对象
     */
    InstContractSettlementItemPO settlePoDeepCopy(InstContractSettlementItemPO po);

    /**
     * fee 对象深拷贝
     * @param po fee 对象
     * @return fee 对象
     */
    InstContractFeeItemPO feePoDeepCopy(InstContractFeeItemPO po);


    InstContractFeeItemVO.FeeConfig feeConfig2Vo(FeeConfig feeConfig);

    /**
     * FX 信息 Request 转 PO
     * @param fxConfig fx 信息
     */
    @Mappings({
            @Mapping(target = "fxSpread", expression = "java(StringUtils.isBlank(fxConfig.getFxSpread()) ? null : new BigDecimal(fxConfig.getFxSpread()))"),
            @Mapping(target = "contractFxSpread", expression = "java(StringUtils.isBlank(fxConfig.getContractFxSpread()) ? null : new BigDecimal(fxConfig.getContractFxSpread()))")
    })
    void fx2FeeItemPoAllowEmpty(@MappingTarget InstContractFeeItemPO feeItemPo, InstContractFxBatchModifiedReqDTO.FxConfig fxConfig);

    /**
     * FX 信息 Request 转 PO,不允许为空
     * @param fxConfig fx 信息
     */

    void fx2FeeItemPo(@MappingTarget InstContractFeeItemPO feeItemPo, InstContractFxBatchModifiedReqDTO.FxConfig fxConfig);

    InstContractFeeFormMsgDTO baseFormMsg2FeeFormMsg(InstContractBaseFormMsgDTO baseFormMsg);
    InstContractSettleFormMsgDTO baseFormMsg2SettleFormMsg(InstContractBaseFormMsgDTO baseFormMsg);

    void fxItem2QueryVO(@MappingTarget InstContractFxQueryVO vo, InstContractFxQueryVO.FxItem fxItem);

    @Mappings({
        @Mapping(target = "paymentMethodTypeGroup", expression = "java(new ArrayList())")
    })
    InstContractFeeItemModifiedNotifyDTO feeItem2NotifyMsg(InstContractFeeItemPO originData);

    void baseInfo2NorifyMsg(@MappingTarget InstContractFeeItemModifiedNotifyDTO notify,InstContractBaseInfoPO baseInfo);


    InstContractFxBatchModifiedReqDTO.FxConfig channelFxBatchSyncItem2Req(InstContractFxBatchSyncDto.FxConfigItem fxConfig);

    InstContractFeeItemPO feeItem2Po(InstContractFeeItem domain);


    InstContractFxBatchSyncDto channelFxBatchSyncReq2Dto(InstContractFxBatchSyncRequest request, long transactionTime);

    @Mappings({
            @Mapping(target = "instCode", source = "fxConfig.instCode"),
            @Mapping(target = "entity", source = "fxConfig.entity"),
            @Mapping(target = "bizType", source = "fxConfig.bizType"),
            @Mapping(target = "payCurrency", source = "fxConfig.payCurrency"),
            @Mapping(target = "originFxSpread", source = "feeItem.fxSpread"),
            @Mapping(target = "modifyFxSpread", source = "fxConfig.fxSpread"),
            @Mapping(target = "contractFxSpread", source = "feeItem.contractFxSpread"),
            @Mapping(target = "fxSpreadDeviation", source = "fxSpreadDeviation"),
    })
    InstContractFxBatchSyncDto.SyncMsg channelFxBatchSyncItem2SyncMsg(InstContractFxBatchSyncDto.FxConfigItem fxConfig, InstContractFeeItemPO feeItem, BigDecimal fxSpreadDeviation);


    /**
     * 日历保存上下文转草稿
     */
    @Mappings({
            @Mapping(target = "draftId", source = "draftId"),
            @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.INST_CENTER.name())"),
            @Mapping(target = "moduleName", expression = "java(OperateModuleEnum.INST_CENTER_FX_MANAGER.name())"),
            @Mapping(target = "operateType", expression = "java(OperateTypeEnum.BATCH_SYNC.name())"),
            @Mapping(target = "status", expression = "java(InstProcessStatusEnum.PROCESSING.name())"),
            @Mapping(target = "draftData", expression = "java(JSON.toJSONString(context,SerializerFeature.DisableCircularReferenceDetect))"),
            @Mapping(target = "businessKey", source = "businessKey"),
            @Mapping(target = "owner", source = "shareId")
    })
    InstBusinessDraftPO channelFxBatchSync2Draft(InstContractFxBatchSyncDto context, String  draftId, String businessKey, String shareId);

}