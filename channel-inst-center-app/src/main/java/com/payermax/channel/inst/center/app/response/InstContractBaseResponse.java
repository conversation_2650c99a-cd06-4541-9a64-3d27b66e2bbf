package com.payermax.channel.inst.center.app.response;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/15
 * @DESC 机构合约通用 Response
 */
@Data
public class InstContractBaseResponse implements Serializable {


    /**
     * 合同、产品信息
     */
    @ApiModelProperty(notes = "合同编号")
    private String contractNo;

    @ApiModelProperty(notes = "原始产品编号")
    private String instOriginProductNo;

    @ApiModelProperty(notes = "费用信息编号")
    private String instContractFeeItemNo;

    @ApiModelProperty(notes = "结算信息编号")
    private String instContractSettlementItemNo;

    @ApiModelProperty(notes = "我司主体")
    private String contractEntity;

    @ApiModelProperty(notes = "机构标识")
    private String instCode;

    @ApiModelProperty(notes = "机构产品类型")
    private String instProductType;

    @ApiModelProperty(notes = "机构产品名称")
    private String instProductName;

    @ApiModelProperty(notes = "生效时间")
    private Date effectiveDate;

    @ApiModelProperty(notes = "支付方式、目标机构、卡组")
    private List<StandardProductMsg> standardProductMsgList;


    @Data
    public static class StandardProductMsg {

        @ApiModelProperty(notes = "支付方式类型")
        private String paymentMethodType;

        @ApiModelProperty(notes = "目标机构")
        private String targetOrg;

        @ApiModelProperty(notes = "卡组织")
        private String cardOrg;

    }
}

