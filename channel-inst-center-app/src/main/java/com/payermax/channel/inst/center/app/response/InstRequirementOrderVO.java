package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 集成需求单 VO
 *
 * <AUTHOR>
 * @date 2022/6/4 14:43
 */
@Data
public class InstRequirementOrderVO implements Serializable {

    private static final long serialVersionUID = 7898151593195937595L;

    private Long id;

    private String applyNo;

    private Long instId;

    private String apiDocId;

    private String apiDocUrl;

    private String platformUrl;

    private String prodPlatformUrl;

    private String payDocId;

    private String payDocUrl;

    private String refundDocId;

    private String refundDocUrl;

    private String disputeDocId;

    private String disputeDocUrl;

    private String billDocId;

    private String billDocUrl;

    private String problemHandleDocId;

    private String problemHandleDocUrl;

    private String releaseRequireDocId;

    private String releaseRequireDocUrl;

    private String remark;

    private String status;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    @ApiModelProperty(notes = "机构账号集合")
    @Valid
    private List<InstAccountVO> accountList;

    @ApiModelProperty(notes = "机构商户报备信息")
    @Valid
    private InstReportMerchantVO instReportMerchant;


    @ApiModelProperty(notes = "机构对账信息")
    @Valid
    private InstReconcileVO instReconcile;

}
