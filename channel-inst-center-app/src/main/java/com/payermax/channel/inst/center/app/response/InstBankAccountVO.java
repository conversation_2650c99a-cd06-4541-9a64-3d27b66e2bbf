package com.payermax.channel.inst.center.app.response;

import com.alibaba.fastjson.JSONObject;
import com.payermax.channel.inst.center.domain.enums.contract.content.YesOrNoEnum;
import com.payermax.channel.inst.center.facade.enums.UseTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.xmlbeans.UserType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName InstBankAccountVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 21:17
 * @Version 1.0
 */
@Data
public class InstBankAccountVO implements Serializable {

    private static final long serialVersionUID = 1313885732726674467L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 机构标识
     */
    private Long instId;

    /**
     * 机构标识code
     */
    private String instCode;

    /**
     * 地区
     */
    private String country;

    /**
     * 币种
     */
    private String currency;

    /**
     * 开户银行
     */
    private String bankCode;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 开户名称
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String accountNo;

    /**
     * 分支行
     */
    private String branch;

    /**
     * 分支行地址
     */
    private String branchAddress;

    /**
     * swift code
     */
    private String swiftCode;

    /**
     * iban
     */
    private String iban;

    /**
     * 账户用途列表
     */
    private List<String> accountUseList;

    /**
     * 我方渠道充值场景下，可充值的币种列表
     */
    private List<String> rechargeCanUseCcyList;

    /**
     * 我方渠道充值场景下，当前账户的优先级
     */
    private Integer rechargeUseOrder;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
