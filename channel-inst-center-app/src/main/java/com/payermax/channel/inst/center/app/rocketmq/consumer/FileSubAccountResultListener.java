package com.payermax.channel.inst.center.app.rocketmq.consumer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.channel.inst.center.app.assembler.ReqDoAssembler;
import com.payermax.channel.inst.center.app.manage.InstSubFundsAccountManage;
import com.payermax.channel.inst.center.app.rocketmq.producer.InstCenterRocketProducer;
import com.payermax.channel.inst.center.app.status.StateMachineExecutor;
import com.payermax.channel.inst.center.app.status.StateRequest;
import com.payermax.channel.inst.center.common.constants.RocketMqConstant;
import com.payermax.channel.inst.center.common.enums.instcenter.SubAccountModeEnum;
import com.payermax.channel.inst.center.domain.subaccount.ResponseAccountDO;
import com.payermax.channel.inst.center.domain.subaccount.request.QuerySubFundsAccountRequestDO;
import com.payermax.channel.inst.center.domain.subaccount.response.FileAccountStatusChangeMqInfo;
import com.payermax.channel.inst.center.domain.subaccount.response.QuerySubFundsAccountResponseDO;
import com.payermax.channel.inst.center.infrastructure.client.DingAlertClient;
import com.payermax.channel.inst.center.infrastructure.entity.InstSubFundsAccountEntity;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> at 2022/10/13 22:25
 **/
@Slf4j
@Component
@RocketMQMessageListener(
        topic = RocketMqConstant.TOPIC_FILE_ACCOUNT_STATUS_CHANGE,
        consumerGroup = RocketMqConstant.CG_CHANNEL_INST_CENTER_ACCOUNT_STATUS_CHANGE,
        consumeThreadNumber = 5,
        //关键是这个属性
        consumeMode = ConsumeMode.CONCURRENTLY)
public class FileSubAccountResultListener extends BaseMqMessageListener<FileAccountStatusChangeMqInfo> implements RocketMQListener<FileAccountStatusChangeMqInfo> {

    @Autowired
    InstSubFundsAccountManage instSubFundsAccountManage;
    @Autowired
    InstCenterRocketProducer instCenterRocketProducer;
    @Autowired
    StateMachineExecutor stateMachineExecutor;
    @Autowired
    DingAlertClient dingAlertClient;
    @Autowired
    ReqDoAssembler reqDoAssembler;
    
    public static final String ALERT_TITLE = "机构子级账号【申请结果】异常通知";

    @NacosValue(value = "${inst.sub.account.alert.message.for.more.than.one:}", autoRefreshed = true)
    private String subAccountSizeMoreThanOne;
    @NacosValue(value = "${inst.sub.account.alert.message.for.is.null:}", autoRefreshed = true)
    private String subAccountSizeIsNull;
    @NacosValue(value = "${inst.sub.account.alert.message.for.handle.exception:}", autoRefreshed = true)
    private String subAccountHandleException;
    
    @Override
    protected void handleMessage(FileAccountStatusChangeMqInfo fileAccountStatusChangeMqInfo) throws Exception {
        log.info("FileSubAccountResultListener-handleMessage:{}", JSON.toJSONString(fileAccountStatusChangeMqInfo));
        // 查询机构账号信息
        FileAccountStatusChangeMqInfo.AccountStatusChangeMq accountStatusChangeMq = fileAccountStatusChangeMqInfo.getMsgData();
        if (Objects.isNull(accountStatusChangeMq)) {
            // 适配新渠道网关MQ数据结构
            accountStatusChangeMq = JSON.parseObject(fileAccountStatusChangeMqInfo.getData(), FileAccountStatusChangeMqInfo.AccountStatusChangeMq.class);
        }
        QuerySubFundsAccountRequestDO requestDO = reqDoAssembler.toQuerySubFundsAccountRequestDO(accountStatusChangeMq);
        List<QuerySubFundsAccountResponseDO> list = instSubFundsAccountManage.queryAccountDetailAndSubList(requestDO);
        // 查询机构账号不可为空, 否则钉钉告警
        if (CollUtil.isEmpty(list)) {
            log.error("查询机构账号异常，未查到机构账号数据,请求参数:{}", JSON.toJSONString(fileAccountStatusChangeMqInfo));
            dingAlertClient.sendMsgForExceptionGroupSubAccount(ALERT_TITLE, subAccountSizeIsNull, accountStatusChangeMq);
            return;
        }
        // 筛选机构子级账号不为空的账号
        List<QuerySubFundsAccountResponseDO> accountList = list.stream()
                .filter(obj -> obj != null && CollUtil.isNotEmpty(obj.getSubAccount()))
                .collect(Collectors.toList());
        // 查询到的子级账号唯一时,更新子级账号,否则钉钉告警
        boolean checkSubAccount = CollUtil.isNotEmpty(accountList) && accountList.size() == 1 && CollUtil.getFirst(accountList).getSubAccount().size() == 1;// CHECKED
        if (!checkSubAccount) {
            log.error("查询机构子级账号异常，查到{}条数据,请求参数:{}", accountList.size(), JSON.toJSONString(fileAccountStatusChangeMqInfo));
            dingAlertClient.sendMsgForExceptionGroupSubAccount(ALERT_TITLE, subAccountSizeMoreThanOne, accountStatusChangeMq);
            return;
        }

        // 更新子级账号
        QuerySubFundsAccountResponseDO instFundsAccountEntity = CollUtil.getFirst(accountList);// CHECKED
        InstSubFundsAccountEntity instSubFundsAccountEntity = CollUtil.getFirst(instFundsAccountEntity.getSubAccount());// CHECKED
        Integer originalStatus = instSubFundsAccountEntity.getStatus();
        if (Objects.nonNull(accountStatusChangeMq)) {
            FileAccountStatusChangeMqInfo.InstSubFundsAccountChange change = accountStatusChangeMq.getSubAccountChange();
            instSubFundsAccountEntity.setSubAccountNo(change.getSubAccountNo());
            instSubFundsAccountEntity.setSubAccountName(change.getSubAccountName());
            instSubFundsAccountEntity.setBSubAccountNo(change.getBSubAccountNo());
            instSubFundsAccountEntity.setStatus(change.getStatus());
            InstSubFundsAccountEntity.AccountJsonBo accountJsonBo = instSubFundsAccountEntity.getAccountJsonBo();
            accountJsonBo.setChannelUniqueNumber(StringUtils.isNotEmpty(change.getChannelUniqueNumber()) ? change.getChannelUniqueNumber() : accountJsonBo.getChannelUniqueNumber());
            accountJsonBo.setRejCode(StringUtils.isNotEmpty(change.getRejCode()) ? change.getRejCode() : accountJsonBo.getRejCode());
            accountJsonBo.setRejMessage(StringUtils.isNotEmpty(change.getRejMessage()) ? change.getRejMessage() : accountJsonBo.getRejMessage());
        }
        // 更新结果状态
        StateRequest stateRequest = new StateRequest(originalStatus, SubAccountModeEnum.getByType(instFundsAccountEntity.getSubAccountMode()), instSubFundsAccountEntity);
        stateMachineExecutor.transChangeSubAccountApplyState(stateRequest);
        // 补偿通知
        ResponseAccountDO responseAccountDO = new ResponseAccountDO();
        responseAccountDO.setInstFundsAccountEntity(instFundsAccountEntity);
        responseAccountDO.setInstSubFundsAccountEntity(instSubFundsAccountEntity);
        SendResult sendResult = instCenterRocketProducer.sendAccountStaticChangeNotify(responseAccountDO);
        log.info("SubAccountCompensateManageImpl-activeSubAccountNo:{}", JSON.toJSONString(sendResult));
    }

    @Override
    protected void overMaxRetryTimesMessage(FileAccountStatusChangeMqInfo fileAccountStatusChangeMqInfo) {
        log.error("FileSubAccountResultListener-消费补偿子级账号异常，参数:{}", JSON.toJSONString(fileAccountStatusChangeMqInfo));
        // 钉钉告警
        dingAlertClient.sendMsgForExceptionGroupSubAccount(ALERT_TITLE, subAccountHandleException, fileAccountStatusChangeMqInfo.getMsgData());
    }
    
    @Override
    public void onMessage(FileAccountStatusChangeMqInfo message) {
        super.dispatchMessage(message);
    }

}
