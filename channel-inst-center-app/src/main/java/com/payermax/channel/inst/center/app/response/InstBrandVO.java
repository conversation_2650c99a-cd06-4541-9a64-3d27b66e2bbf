package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstBrandVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/18 21:17
 * @Version 1.0
 */
@Data
public class InstBrandVO implements Serializable {


    private static final long serialVersionUID = -2939208980626003890L;
    /**
     * 品牌标识
     */
    private Long brandId;

    /**
     * 品牌编码
     */
    private String brandCode;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * BD负责人
     */
    private String bdId;

    /**
     * BD负责人名称
     */
    private String bdName;

    /**
     * 状态 Y：启用，N：停用
     */
    private String status;
}
