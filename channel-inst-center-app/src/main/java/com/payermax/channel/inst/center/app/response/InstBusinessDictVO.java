package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName InstBusinessDictVO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/17 15:34
 */
@Data
public class InstBusinessDictVO implements Serializable {
    private static final long serialVersionUID = 2436741439041082576L;
    private Long id;

    private String businessType;

    private String businessNo;

    private String dictCode;

    private String dictName;

    private String extraInfo;

    private String status;

    private Date utcCreate;

    private Date utcModified;
}
