package com.payermax.channel.inst.center.app.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payermax.channel.inst.center.infrastructure.entity.InstRequirementScheduleEntity;
import com.payermax.channel.inst.center.infrastructure.entity.query.InstRequirementScheduleQueryEntity;

/**
 * 集成需求单排期Service
 *
 * <AUTHOR>
 * @date 2022/6/4 14:00
 */
public interface InstRequirementScheduleService {

    /**
     * 保存
     *
     * @param record
     * @return
     */
    int save(InstRequirementScheduleEntity record);

    /**
     * 根据排期ID查询
     *
     * @param id
     * @return
     */
    InstRequirementScheduleEntity getById(Long id);

    /**
     * 分页查询
     *
     * @param queryEntity
     * @param pageNum
     * @param pageSize
     * @return
     */
    IPage<InstRequirementScheduleQueryEntity> queryPageList(InstRequirementScheduleQueryEntity queryEntity, Long pageNum, Long pageSize);
}
