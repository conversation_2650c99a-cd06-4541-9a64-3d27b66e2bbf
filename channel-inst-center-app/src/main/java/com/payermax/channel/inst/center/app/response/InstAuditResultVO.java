package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstAuditResultVO
 * @Description
 * <AUTHOR>
 * @Date 2022/5/19 9:44
 * @Version 1.0
 */
@Data
public class InstAuditResultVO implements Serializable {


    private static final long serialVersionUID = -5114650830930851300L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 业务主键
     */
    private String businessNo;

    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核结果 AGREE：同意，REFUSE：拒绝，RETURN：驳回
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String extraInfo;
}
