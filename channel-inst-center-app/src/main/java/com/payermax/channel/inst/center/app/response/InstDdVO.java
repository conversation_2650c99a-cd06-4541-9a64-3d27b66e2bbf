package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 机构DD VO
 *
 * <AUTHOR>
 * @date 2022/5/15 22:58
 */
@Data
public class InstDdVO implements Serializable {

    private static final long serialVersionUID = -3850481496334692908L;

    private Long id;

    private Long instId;

    private String applyNo;

    private String registerName;

    private String registerNo;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date registerDate;

    private String registerAddress;

    private String companyScale;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date validityDate;

    private String corporateName;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date corporateBirthDate;

    private String corporateAddress;

    private String website;

    private String businessScope;

    private String status;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    private InstBaseInfoVO instBaseInfo;

    private InstDdSurveyVO ddSurvey;

    private List<InstAuditDataVO> auditDataList;

    private InstAuditResultVO auditResult;

}
