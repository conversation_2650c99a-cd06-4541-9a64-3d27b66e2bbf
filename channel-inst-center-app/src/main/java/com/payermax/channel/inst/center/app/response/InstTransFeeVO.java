package com.payermax.channel.inst.center.app.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName InstTransFeeVO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/1 21:03
 */
@Data
public class InstTransFeeVO implements Serializable {
    private static final long serialVersionUID = 4408465067880899870L;
    private Long id;

    private String feeGroupId;

    private String mcc;

    private String feeType;

    private String calculateType;

    private BigDecimal minFee;

    private BigDecimal maxFee;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcCreate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date utcModified;

    private String feeDetail;

    private String contractNo;

    private String productCode;

    private String productName;

    private String channelType;

    private String paymentMethodType;

    private String countrys;

    private String targetOrgs;

    private String cardOrgs;

    private String version;

    /**
     * 产品维度的列需要合并的行数（计算一个产品下所有交易明细数量）
     * 0：默认不需要合并
     */
    private Long num = 0L;

    /**
     * 每个产品能力列需要合并的行数（计算一个version对应的一个费用分组的交易费用数量）
     */
    private Long versionNum = 0L;
}
