package com.payermax.channel.inst.center.app.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName InstProductCapabilityOperateVO
 * @Description
 * <AUTHOR>
 * @Date 2022/6/2 20:10
 */
@Data
public class InstProductCapabilityOperateVO implements Serializable {

    private static final long serialVersionUID = -4848382687783862670L;

    /**
     * 标识一次操作
     */
    private String version;

    /**
     * 一次能力操作对应的国家
     */
    private String countrys;

    /**
     * 一次能力操作对应的目标机构
     */
    private String targetOrgs;

    /**
     * 一次能力操作对应的卡组织
     */
    private String cardOrgs;

    /**
     * 一次能力操作对应的扩展信息
     */
    private String extraInfo;

}
