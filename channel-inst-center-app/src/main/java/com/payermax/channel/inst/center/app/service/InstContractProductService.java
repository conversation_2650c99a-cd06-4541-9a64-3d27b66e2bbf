package com.payermax.channel.inst.center.app.service;

import com.payermax.channel.inst.center.infrastructure.entity.InstContractProductEntity;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/5/28 0:21
 */
public interface InstContractProductService {
    /**
     * 保存合同签约产品信息
     *
     * @param records
     * @return
     */
    int saveBatch(List<InstContractProductEntity> records);

    /**
     * 根据合同单号查合同签约产品信息
     *
     * @param contractNo
     * @return
     */
    List<InstContractProductEntity> getByContractNo(String contractNo);

    InstContractProductEntity selectByProductCodeAndCapabilityCode(String productCode,String capabilityCode);

    List<InstContractProductEntity> selectByProductCodeAndCapabilityCodes(String productCode,List<String> capabilityCodes);

    /**
     * 查合同签约产品能力
     * @param entity
     * @return
     */
    List<InstContractProductEntity> selectByEntity(InstContractProductEntity entity);

    /**
     * 根据产品编码删除合同签约产品信息
     * @param productCode
     * @return
     */
    int deleteByProductCode(String productCode);

    /**
     * 根据产品编码和能力编码删除合同签约产品信息
     * @param productCode
     * @return
     */
    int deleteByProductCodeAndCapabilityCode(String productCode,List<String> capabilityCodes);

    /**
     * 根据产品编码和操作能力版本删除合同签约产品信息
     * @param productCode
     * @return
     */
    int deleteByProductCodeAndVersion(String productCode,String version);

    int updateFeeGroupId(String contractNo,String productCode,List<String> capabilityCodes,String feeGroupId);
}
